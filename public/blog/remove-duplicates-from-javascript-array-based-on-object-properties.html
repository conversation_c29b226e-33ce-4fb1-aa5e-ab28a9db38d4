<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Remove duplicates from JavaScript Array based on Object Properties - Say to the believing men..</title>
    <link rel="stylesheet" href="style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Rubik:wght@300;400;500;600;700&family=Yellowtail&display=swap" rel="stylesheet">
</head>
<body>
    <header class="header">
        <nav class="nav">
            <div class="nav-brand">
                <img src="avatar.png" alt="Avatar" class="avatar">
                <a href="/blog/" class="brand-title">Say to the believing men..</a>
            </div>
            <div class="nav-links">
                <a href="/blog/" class="nav-link">Home</a>
                <a href="/blog/articles/" class="nav-link">Articles</a>
                <a href="https://twitter.com/ibnyusrat" class="nav-link" target="_blank" rel="noopener">Twitter</a>
                <a href="https://pk.linkedin.com/in/muhammad-bin-yusrat" class="nav-link" target="_blank" rel="noopener">LinkedIn</a>
            </div>
        </nav>
    </header>

    <main class="main">
        <div class="container">
            <article class="post">
                <header class="post-header">
                    <div class="post-meta">
                        <time class="post-date">March 31, 2020</time>
                    </div>
                    <h1 class="post-title">Remove duplicates from JavaScript Array based on Object Properties</h1>
                </header>

                <div class="post-content">
<p>Let's say you have a JavaScript Array that goes something like:</p>

<pre><code>let p = [{
  id: '0923482304',
  someOther: 'detail',
  name: 'Some super nasheed'
}, {
  id: '0923482304',
  someOther: 'which',
  name: 'Some super nasheed'
}, {
  id: '0923482305',
  someOther: 'could be',
  name: 'Another super nasheed'
}, {
  id: '0923482305',
  someOther: 'different as well.',
  name: 'Another super nasheed'
}];</code></pre>

<p>And you'd like to remove duplicates from this array, but not looking just for the unique objects as a whole, but unique properties of objects. So for example, you'd like to remove duplicates based on 'id' property of each object in the Array.</p>

<p>So this can be done in many ways, one of the ways I wrote today is rather brief, here is how it goes.</p>

<p>We use Array.filter(). Array.filter accepts up to 3 arguments:</p>
<ol>
<li>Current array item being filtered</li>
<li>Index of this item</li>
<li>The full array we are filtering</li>
</ol>

<p>So we could do something like:</p>

<pre><code>let filtered = p.filter((item, index, array) => {
  let array_ids = array.map(item => item.id); // to get an array of just the ids, or whichever property we'd like to filter with.
  if(array_ids.indexOf(item.id) === index) {
    return true; // if we return true inside Array.filter(), that item is included in the resultant array.
  } else {
    return false; // otherwise it is not included. Returning false will omit the current item being checked.
  }
});</code></pre>

<p>We can improve this a little bit better by writing it in one line like:</p>

<pre><code>let filtered = p.filter((item, index, arr) => arr.map(item => item.id).indexOf(item.id) == index);</code></pre>

<p>I hope this helps someone else. Please feel free to comment if I made a stupid mistake in this code. Thanks!</p>
                </div>

                <footer class="post-footer">
                    <div class="post-navigation">
                        <a href="/blog/articles/" class="back-link">← Back to All Articles</a>
                    </div>
                </footer>
            </article>
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 Say to the believing men.. All rights reserved.</p>
        </div>
    </footer>

    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-5842028416104201"
     crossorigin="anonymous"></script>
</body>
</html>