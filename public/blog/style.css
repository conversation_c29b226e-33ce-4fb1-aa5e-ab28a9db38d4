/* Reset and Base Styles */
@import url('https://fonts.googleapis.com/css2?family=Amiri:wght@400;700&display=swap');

* {
    margin: 0;
    padding: 0;
    box-sizing: border-box;
}

body {
    font-family: 'Rubik', -apple-system, BlinkMacSystemFont, 'Segoe UI', Roboto, sans-serif;
    line-height: 1.6;
    color: #e5e5e5;
    background-color: #0a0a0a;
    font-size: 16px;
}

/* Container */
.container {
    max-width: 800px;
    margin: 0 auto;
    padding: 0 20px;
}

/* Header */
.header {
    background-color: rgba(10, 10, 10, 0.95);
    backdrop-filter: blur(10px);
    border-bottom: 1px solid #2a2a2a;
    position: sticky;
    top: 0;
    z-index: 100;
}

.nav {
    display: flex;
    justify-content: space-between;
    align-items: center;
    padding: 1rem 0;
    max-width: 800px;
    margin: 0 auto;
    padding-left: 20px;
    padding-right: 20px;
}

.nav-brand {
    display: flex;
    align-items: center;
    gap: 12px;
}

.avatar {
    width: 40px;
    height: 40px;
    border-radius: 50%;
    object-fit: cover;
}

.brand-title {
    font-size: 1.2rem;
    font-weight: 600;
    color: #ffffff;
    text-decoration: none;
    transition: color 0.2s ease;
}

.brand-title:hover {
    color: #60a5fa;
}

.nav-links {
    display: flex;
    gap: 24px;
    align-items: center;
}

.nav-link {
    color: #a1a1aa;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
    font-size: 0.9rem;
}

.nav-link:hover,
.nav-link.active {
    color: #ffffff;
}

/* Main Content */
.main {
    padding: 2rem 0;
}

/* Hero Section */
.hero {
    text-align: center;
    padding: 4rem 0;
    margin-bottom: 3rem;
}

.hero-content {
    max-width: 600px;
    margin: 0 auto;
}

.hero-avatar {
    width: 80px;
    height: 80px;
    border-radius: 50%;
    object-fit: cover;
    margin-bottom: 1.5rem;
    border: 3px solid #2a2a2a;
}

.hero-title {
    font-family: 'Yellowtail', cursive;
    font-size: 3rem;
    font-weight: 400;
    color: #ffffff;
    margin-bottom: 1rem;
    line-height: 1.1;
}

.hero-subtitle {
    font-size: 1.1rem;
    color: #a1a1aa;
    font-weight: 400;
    max-width: 500px;
    margin: 0 auto;
}

/* Section Titles */
.section-title {
    font-size: 1.8rem;
    font-weight: 600;
    color: #ffffff;
    margin-bottom: 2rem;
    text-align: center;
}

/* Posts Grid */
.posts-grid {
    display: grid;
    gap: 2rem;
    margin-bottom: 3rem;
}

.post-card {
    background-color: #1a1a1a;
    border: 1px solid #2a2a2a;
    border-radius: 12px;
    padding: 1.5rem;
    transition: all 0.3s ease;
}

.post-card:hover {
    border-color: #3a3a3a;
    transform: translateY(-2px);
    box-shadow: 0 8px 25px rgba(0, 0, 0, 0.3);
}

.post-meta {
    margin-bottom: 0.75rem;
}

.post-date {
    color: #71717a;
    font-size: 0.85rem;
    font-weight: 500;
}

.post-title {
    margin-bottom: 0.75rem;
}

.post-link {
    color: #ffffff;
    text-decoration: none;
    font-size: 1.2rem;
    font-weight: 600;
    line-height: 1.3;
    transition: color 0.2s ease;
}

.post-link:hover {
    color: #60a5fa;
}

.post-excerpt {
    color: #a1a1aa;
    font-size: 0.95rem;
    line-height: 1.6;
}

/* Ad Sections */
.ad-section {
    background-color: #1a1a1a;
    border: 1px solid #2a2a2a;
    border-radius: 8px;
    padding: 1rem;
    margin: 2rem 0;
    text-align: center;
    color: #71717a;
    font-size: 0.9rem;
}

.ad-section::before {
    content: "Advertisement";
    display: block;
    font-size: 0.75rem;
    color: #52525b;
    margin-bottom: 0.5rem;
    text-transform: uppercase;
    letter-spacing: 0.5px;
}

/* Footer */
.footer {
    background-color: #1a1a1a;
    border-top: 1px solid #2a2a2a;
    padding: 2rem 0;
    margin-top: 4rem;
    text-align: center;
}

.footer p {
    color: #71717a;
    font-size: 0.9rem;
}

/* Responsive Design */
@media (max-width: 768px) {
    .nav {
        flex-direction: column;
        gap: 1rem;
        padding: 1rem 20px;
    }
    
    .nav-links {
        gap: 16px;
    }
    
    .hero-title {
        font-size: 2.5rem;
    }
    
    .hero-subtitle {
        font-size: 1rem;
    }
    
    .container {
        padding: 0 16px;
    }
    
    .post-card {
        padding: 1.25rem;
    }
    
    .hero {
        padding: 2rem 0;
    }
}

@media (max-width: 480px) {
    .hero-title {
        font-size: 2rem;
    }
    
    .nav-links {
        flex-wrap: wrap;
        justify-content: center;
    }
    
    .posts-grid {
        gap: 1.5rem;
    }
}

/* Smooth scrolling */
html {
    scroll-behavior: smooth;
}

/* Focus styles for accessibility */
.nav-link:focus,
.post-link:focus,
.brand-title:focus {
    outline: 2px solid #60a5fa;
    outline-offset: 2px;
}

/* Individual Post Styles */
.post {
    max-width: 700px;
    margin: 0 auto;
}

.post-header {
    text-align: center;
    margin-bottom: 3rem;
    padding-bottom: 2rem;
    border-bottom: 1px solid #2a2a2a;
}

.post-header .post-title {
    font-family: 'Yellowtail', cursive;
    font-size: 3rem;
    font-weight: 400;
    color: #ffffff;
    margin-bottom: 1rem;
    line-height: 1.1;
}

.post-content {
    font-size: 1.1rem;
    line-height: 1.8;
    color: #e5e5e5;
}

.post-content h2 {
    font-size: 1.8rem;
    font-weight: 600;
    color: #ffffff;
    margin: 2.5rem 0 1rem 0;
    padding-top: 1rem;
}

.post-content h3 {
    font-size: 1.4rem;
    font-weight: 600;
    color: #ffffff;
    margin: 2rem 0 1rem 0;
}

.post-content p {
    margin-bottom: 1.5rem;
}

.post-content ul {
    margin: 1.5rem 0;
    padding-left: 1.5rem;
}

.post-content li {
    margin-bottom: 0.5rem;
}

.post-content blockquote {
    border-left: 4px solid #60a5fa;
    padding-left: 1.5rem;
    margin: 2rem 0;
    font-style: italic;
    color: #a1a1aa;
}

.post-content blockquote p {
    margin-bottom: 0;
}

.post-footer {
    margin-top: 3rem;
    padding-top: 2rem;
    border-top: 1px solid #2a2a2a;
}

.back-link {
    color: #60a5fa;
    text-decoration: none;
    font-weight: 500;
    transition: color 0.2s ease;
}

.back-link:hover {
    color: #93c5fd;
}

/* Loading animation for ads */
.ad-section.loading {
    position: relative;
    min-height: 100px;
}

.ad-section.loading::after {
    content: "";
    position: absolute;
    top: 50%;
    left: 50%;
    transform: translate(-50%, -50%);
    width: 20px;
    height: 20px;
    border: 2px solid #2a2a2a;
    border-top: 2px solid #60a5fa;
    border-radius: 50%;
    animation: spin 1s linear infinite;
}

@keyframes spin {
    0% { transform: translate(-50%, -50%) rotate(0deg); }
    100% { transform: translate(-50%, -50%) rotate(360deg); }
}

/* Post Images */
.post-image {
    display: block;
    margin: 2rem auto;
    max-width: 100%;
    width: 100%;
    height: auto;
    border-radius: 8px;
    box-shadow: 0 4px 12px rgba(0, 0, 0, 0.3);
    box-sizing: border-box;
}

/* Ensure images in paragraphs don't overflow */
.post-content p img.post-image {
    max-width: 100%;
    width: 100%;
    height: auto;
}

/* Verse and Arabic Text Formatting */
.post-content .verse {
    font-family: 'Amiri', 'Noto Naskh Arabic', serif;
    font-size: 1.2rem;
    line-height: 2;
    text-align: center;
    margin: 1.5rem 0;
    padding: 1rem;
    background-color: rgba(255, 255, 255, 0.05);
    border-radius: 8px;
    border-left: 4px solid #60a5fa;
    direction: rtl;
}

.post-content .verse-reference {
    font-size: 0.9rem;
    color: #71717a;
    text-align: center;
    margin-top: 0.5rem;
    font-style: italic;
}

/* Arabic text in paragraphs */
.post-content p:has(.arabic) {
    text-align: center;
    direction: rtl;
    font-family: 'Amiri', 'Noto Naskh Arabic', serif;
    font-size: 1.1rem;
    line-height: 1.8;
}

/* Code blocks */
.post-content pre {
    background-color: #1a1a1a;
    border: 1px solid #2a2a2a;
    border-radius: 8px;
    padding: 1rem;
    overflow-x: auto;
    margin: 1.5rem 0;
}

.post-content code {
    font-family: 'Fira Code', 'Monaco', 'Consolas', monospace;
    font-size: 0.9rem;
    color: #e5e5e5;
}

/* Lists */
.post-content ol {
    margin: 1rem 0;
    padding-left: 1.5rem;
}

.post-content ol li {
    margin-bottom: 0.5rem;
    color: #e5e5e5;
} 