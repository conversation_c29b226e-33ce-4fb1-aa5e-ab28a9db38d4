<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>VIMCode, make VIM look and feel like VSCode - Say to the believing men..</title>
    <link rel="stylesheet" href="style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Rubik:wght@300;400;500;600;700&family=Yellowtail&display=swap" rel="stylesheet">
</head>
<body>
    <header class="header">
        <nav class="nav">
            <div class="nav-brand">
                <img src="avatar.png" alt="Avatar" class="avatar">
                <a href="/blog/" class="brand-title">Say to the believing men..</a>
            </div>
            <div class="nav-links">
                <a href="/blog/" class="nav-link">Home</a>
                <a href="/blog/articles/" class="nav-link">Articles</a>
                <a href="https://twitter.com/ibnyusrat" class="nav-link" target="_blank" rel="noopener">Twitter</a>
                <a href="https://pk.linkedin.com/in/muhammad-bin-yusrat" class="nav-link" target="_blank" rel="noopener">LinkedIn</a>
            </div>
        </nav>
    </header>

    <main class="main">
        <div class="container">
            <article class="post">
                <header class="post-header">
                    <div class="post-meta">
                        <time class="post-date">July 17, 2020</time>
                    </div>
                    <h1 class="post-title">VIMCode, make VIM look and feel like VSCode</h1>
                </header>

                <div class="post-content">
<p>I recently had to carry around a small notebook that ran an Intel Atom Processor and couldn't handle IntelliJ Idea Ultimate that I normally use for TypeScript related projects. I knew a little bit of VIM and also had something in my head about being able to install plugins to extend its functionality. VIM of course would not require too many resources, as you can probably tell, so I went ahead and started looking for my options. After reading multiple pages and watching a couple of videos, this is the setup I ended up with:</p>

<img src="/blog/images/vimcode.gif" alt="VIMCode setup demonstration" class="post-image">

<p>So to save some time for my fellow developers, I simply put my ~/.vimrc and to the point instructions to get what you see on the screen.</p>

<p>Simply head over to my github repository called VIMCode and follow the instructions: <a href="https://github.com/ibnYusrat/vimcode">https://github.com/ibnYusrat/vimcode</a></p>

<p>If you have questions, feel free to leave a comment below.</p>
                </div>

                <footer class="post-footer">
                    <div class="post-navigation">
                        <a href="/blog/articles/" class="back-link">← Back to All Articles</a>
                    </div>
                </footer>
            </article>
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 Say to the believing men.. All rights reserved.</p>
        </div>
    </footer>

    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-5842028416104201"
     crossorigin="anonymous"></script>
</body>
</html>