<!DOCTYPE html>
<html lang="en">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Add prayer times to Ubuntu Unity Indicator Panel - Say to the believing men..</title>
    <link rel="stylesheet" href="style.css">
    <link rel="preconnect" href="https://fonts.googleapis.com">
    <link rel="preconnect" href="https://fonts.gstatic.com" crossorigin>
    <link href="https://fonts.googleapis.com/css2?family=Rubik:wght@300;400;500;600;700&family=Yellowtail&display=swap" rel="stylesheet">
</head>
<body>
    <header class="header">
        <nav class="nav">
            <div class="nav-brand">
                <img src="avatar.png" alt="Avatar" class="avatar">
                <a href="/blog/" class="brand-title">Say to the believing men..</a>
            </div>
            <div class="nav-links">
                <a href="/blog/" class="nav-link">Home</a>
                <a href="/blog/articles/" class="nav-link">Articles</a>
                <a href="https://twitter.com/ibnyusrat" class="nav-link" target="_blank" rel="noopener">Twitter</a>
                <a href="https://pk.linkedin.com/in/muhammad-bin-yusrat" class="nav-link" target="_blank" rel="noopener">LinkedIn</a>
            </div>
        </nav>
    </header>

    <main class="main">
        <div class="container">
            <article class="post">
                <header class="post-header">
                    <div class="post-meta">
                        <time class="post-date">October 17, 2017</time>
                    </div>
                    <h1 class="post-title">Add prayer times to Ubuntu Unity Indicator Panel</h1>
                </header>

                <div class="post-content">
<p>Add prayer times to Ubuntu Unity Indicator Panel</p>





<p>Assalam o Aaliakum Wr Wb,<br>I just wanted to take a moment to share this small nifty program that I have running on my Ubuntu 16.04 installation. It very accurately tells me prayer times and shows a small popup 15 minutes before the time for a specific Salah enters.</p>

<p>The best thing about it is that it completely blends in the design of the Ubuntu Unity UI and gives the feel of a System App.<br>Here is how it is looking on my screen at the moment:<br>Prayer times for Ubuntu 16.04</p>

<p>The first step to install it is to add the following PPA to your sources. Open terminal (CTRL + ALT + T) and enter the following command:<br>sudo add-apt-repository ppa:m-alaa8/indicator-prayer-times<br>And then, to update the package list run:<br>sudo apt-get update<br>Once apt update finishes its thing, you should be able to install it by writing:<br>sudo apt install indicator-prayer-times<br>If the above method doesn't work or produces errors, you can try and install it by downloading the deb file from here: prayer-times-indicator.deb<br>Now, by default the app shows remaining time till the next Salah, and the icon in the indicator too is a little bit weird (in my humble opinion). So I went ahead and modified the app a little bit to suit my theme and made it show the time of the next Salah instead of the number of minutes remaining to it. I am using the Ubuntu Paper (inspired by material design) theme.<br>And if some of you would like the app the way it looks on my screen, please feel free to download these files below and patch the app with these files (I'll tell you how) and your indicator panel will look exactly like mine.<br>Head to this link and download these two files somewhere on your computer: prayer-times-indicator<br>Basically all you have to do is to copy the indicator-prayer-times file to /usr/bin/directory and the SVG Icon to /usr/share/icons/hicolor/scalable/apps/ folder.<br>You will have to give execution permission to the downloaded indicator-prayer-times file to allow it to be runnable (before or after moving it to the /usr/bin directory). This can be done easily by writing the following command in the terminal (when you're at the same directory as the above mentioned file:<br>sudo chmod +x indicator-prayer-times<br>If you face trouble moving the file to that directory, you can simply open the file browser as admin and use it to copy and paste easily. To do that, simply type: sudo nautilus ./ & in a terminal.</p>


                </div>

                <footer class="post-footer">
                    <div class="post-navigation">
                        <a href="/blog/articles/" class="back-link">← Back to All Articles</a>
                    </div>
                </footer>
            </article>
        </div>
    </main>

    <footer class="footer">
        <div class="container">
            <p>&copy; 2025 Say to the believing men.. All rights reserved.</p>
        </div>
    </footer>

    <script async src="https://pagead2.googlesyndication.com/pagead/js/adsbygoogle.js?client=ca-pub-5842028416104201"
     crossorigin="anonymous"></script>
</body>
</html>