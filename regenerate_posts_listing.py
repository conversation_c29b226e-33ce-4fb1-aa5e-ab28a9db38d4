#!/usr/bin/env python3
import json
from datetime import datetime

def load_posts():
    """Load posts from JSON file"""
    with open('scraped_posts.json', 'r', encoding='utf-8') as f:
        return json.load(f)

def format_date(date_str):
    """Format date string to readable format"""
    try:
        date_obj = datetime.strptime(date_str, '%Y-%m-%d %H:%M:%S')
        return date_obj.strftime('%B %d, %Y')
    except:
        return date_str

def generate_posts_listing():
    """Generate the posts listing page with all posts"""
    posts = load_posts()
    
    # Sort posts by date (newest first)
    posts.sort(key=lambda x: x['date'], reverse=True)
    
    # Generate HTML for each post
    posts_html = ""
    for post in posts:
        formatted_date = format_date(post['date'])
        excerpt = post.get('excerpt', '')[:200] + '...' if len(post.get('excerpt', '')) > 200 else post.get('excerpt', '')
        
        posts_html += f'''
                    <article class="post-card">
                        <div class="post-meta">
                            <time class="post-date">{formatted_date}</time>
                        </div>
                        <h3 class="post-title">
                            <a href="/blog/{post['slug']}.html" class="post-link">{post['title']}</a>
                        </h3>
                        <p class="post-excerpt">
                            {excerpt}
                        </p>
                    </article>
'''
    
    # Read the clean template
    with open('posts_listing_template.html', 'r', encoding='utf-8') as f:
        template = f.read()
    
    # Fix the CSS path for the subdirectory
    template = template.replace('href="style.css"', 'href="../style.css"')
    template = template.replace('src="avatar.png"', 'src="../avatar.png"')
    
    # Replace the placeholder with posts HTML
    output = template.replace('<!-- POSTS_PLACEHOLDER -->', posts_html)
    
    # Write the new index.html
    with open('public/blog/posts/index.html', 'w', encoding='utf-8') as f:
        f.write(output)
    
    print(f"Generated fresh posts listing page with {len(posts)} posts.")

if __name__ == "__main__":
    generate_posts_listing() 