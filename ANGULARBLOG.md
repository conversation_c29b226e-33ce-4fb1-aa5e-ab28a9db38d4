# Angular Blog Project Plan

## Goal
Create a new Angular project named `angular-blog` in the current directory.

## Key Features
- **Framework:** Angular
- **Backend:** Firebase
- **Hosting:** Firebase Hosting
- **Look:** Minimalist with a dark/light mode switcher.
- **Logo:** `public/blog/avatar.png` to be displayed at the top-center.
- **Content:** Posts will be sourced from the local `scraped_posts.json` file.
- **SEO:** Prerendering (Server-Side Rendering) will be enabled.
- **Homepage:**
    - Display the 5 latest posts initially.
    - Infinite scroll to load more posts on the client-side.
    - When rendered on the server, all posts should be displayed at once.

## Current Status
- **Blocked:** The Angular CLI `ng new` command failed.
- **Reason:** The current Node.js version is `v20.16.0`. The Angular CLI requires `v20.19.0` or higher.

## Next Steps
1.  **User Action:** Update the Node.js version to `v20.19.0` or a newer compatible version.
2.  **Resume:** Once the Node.js version is updated, I will run the following command to create the project:
    ```bash
    npx --yes @angular/cli@latest new angular-blog --routing=true --style=scss --standalone=false --ssr=true
    ```
