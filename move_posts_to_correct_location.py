#!/usr/bin/env python3
import os
import shutil
import json

def load_posts():
    """Load posts from JSON file"""
    with open('scraped_posts.json', 'r', encoding='utf-8') as f:
        return json.load(f)

def move_posts_to_correct_location():
    """Move post files from /blog/posts/{slug}/index.html to /blog/{slug}.html"""
    posts = load_posts()
    
    # Create the blog directory if it doesn't exist
    blog_dir = 'public/blog'
    if not os.path.exists(blog_dir):
        os.makedirs(blog_dir)
    
    moved_count = 0
    for post in posts:
        slug = post['slug']
        
        # Source path: /blog/posts/{slug}/index.html
        source_path = f'public/blog/posts/{slug}/index.html'
        
        # Destination path: /blog/{slug}.html
        dest_path = f'public/blog/{slug}.html'
        
        if os.path.exists(source_path):
            try:
                # Read the content and fix any relative paths
                with open(source_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Fix CSS and image paths for the new location
                content = content.replace('href="style.css"', 'href="style.css"')
                content = content.replace('src="avatar.png"', 'src="avatar.png"')
                
                # Write to the new location
                with open(dest_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"Moved: {slug}")
                moved_count += 1
                
            except Exception as e:
                print(f"Error moving {slug}: {e}")
        else:
            print(f"Source file not found: {source_path}")
    
    print(f"\nMoved {moved_count} posts to correct location.")
    print("Posts are now accessible at /blog/{slug}.html")

if __name__ == "__main__":
    move_posts_to_correct_location() 