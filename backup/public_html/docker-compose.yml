version: '3.9'

services:
  mysql:
    image: arm64v8/mysql
    container_name: mysql
    restart: unless-stopped
    environment:
      MYSQL_ROOT_PASSWORD: my-secret-pw
      MYSQL_DATABASE: muhammad_hawk
    volumes:
      - db-data:/var/lib/mysql
    networks:
      - my-network

  phpmyadmin:
    image: phpmyadmin/phpmyadmin:5.0.2
    container_name: phpmyadmin
    restart: unless-stopped
    ports:
      - "8080:80"
    environment:
      PMA_HOST: mysql
    depends_on:
      - mysql
    networks:
      - my-network

  wordpress:
    image: wordpress:php7.4-apache
    container_name: my-old-blog
    restart: unless-stopped
    ports:
      - "8090:80"
    volumes:
      - ./blog:/var/www/html
    depends_on:
      - mysql
    networks:
      - my-network

volumes:
  db-data:

networks:
  my-network:
