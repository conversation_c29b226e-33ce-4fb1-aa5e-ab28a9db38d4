msgid ""
msgstr ""
"Project-Id-Version: WP Photo Album Plus\n"
"POT-Creation-Date: 2017-07-13 13:49+0200\n"
"PO-Revision-Date: 2017-07-13 13:49+0200\n"
"Last-Translator: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language-Team: <PERSON><PERSON><PERSON><PERSON> <<EMAIL>>\n"
"Language: zh_CN\n"
"MIME-Version: 1.0\n"
"Content-Type: text/plain; charset=UTF-8\n"
"Content-Transfer-Encoding: 8bit\n"
"X-Generator: Poedit 1.8.12\n"
"X-Poedit-Basepath: ..\n"
"X-Poedit-WPHeader: wppa.php\n"
"Plural-Forms: nplurals=1; plural=0;\n"
"X-Poedit-SourceCharset: UTF-8\n"
"X-Poedit-KeywordsList: __;_e;_n:1,2;_x:1,2c;_ex:1,2c;_nx:4c,1,2;esc_attr__;"
"esc_attr_e;esc_attr_x:1,2c;esc_html__;esc_html_e;esc_html_x:1,2c;_n_noop:1,2;"
"_nx_noop:3c,1,2;__ngettext_noop:1,2\n"
"X-Poedit-SearchPath-0: .\n"
"X-Poedit-SearchPathExcluded-0: *.js\n"

#: theme/photo-album-page-2010.php:33 theme/photo-album-search-page.php:52
msgid "Pages:"
msgstr "页数："

#: theme/photo-album-page-2010.php:34 theme/photo-album-search-page.php:53
#: wppa-album-admin-autosave.php:1515 wppa-album-admin-autosave.php:1553
#: wppa-album-admin-autosave.php:1653 wppa-album-admin-autosave.php:1888
#: wppa-album-admin-autosave.php:1984 wppa-album-admin-autosave.php:2067
#: wppa-album-admin-autosave.php:2119 wppa-album-admin-autosave.php:2256
#: wppa-album-admin-autosave.php:2770 wppa-setup.php:1423
#: wppa-thumbnails.php:685
msgid "Edit"
msgstr "编辑"

#: theme/photo-album-search-page.php:30
msgid "Warning. No page defined for search results!"
msgstr "警告。无页搜索结果中定义！"

#: theme/photo-album-search-page.php:39 wppa-boxes-html.php:208
#: wppa-settings-autosave.php:384 wppa-settings-autosave.php:3364
msgid "Search"
msgstr "搜索"

#: theme/search-2010.php:20 theme/search-2016.php:23
#, php-format
msgid "Search Results for: %s"
msgstr "搜索结果: %s"

#: theme/search-2010.php:31
msgid "Nothing Found"
msgstr "什么都没找到"

#: theme/search-2010.php:33
msgid ""
"Sorry, but nothing matched your search criteria. Please try again with some "
"different keywords."
msgstr "抱歉，没有符合您搜索条件。请换其它关键词再试。"

#: theme/search-2016.php:46 wppa-album-admin-autosave.php:2158
#: wppa-links.php:1089 wppa-links.php:1099
msgid "Previous page"
msgstr ""

#: theme/search-2016.php:47 wppa-admin-functions.php:761
#: wppa-album-admin-autosave.php:2178 wppa-links.php:1117
msgid "Next page"
msgstr ""

#: theme/search-2016.php:48 wppa-settings-autosave.php:382
#: wppa-settings-autosave.php:1635 wppa-settings-autosave.php:9396
msgid "Page"
msgstr ""

#: theme/wppa-theme.php:328 theme/wppa-theme.php:347
msgid "No photos found matching your search criteria."
msgstr "没有照片找到符合搜索条件。"

#: theme/wppa-theme.php:331
msgid "No albums found matching your search criteria."
msgstr "没有相册找到符合搜索条件。"

#: theme/wppa-theme.php:334
msgid "No albums or photos found matching your search criteria."
msgstr "没有找到符合搜索条件的相册或照片。"

#: wppa-admin-functions.php:27
msgid "Settings successfully backed up"
msgstr ""

#: wppa-admin-functions.php:31
msgid "Unable to backup settings"
msgstr ""

#: wppa-admin-functions.php:40
msgid "Error writing to settings backup file"
msgstr ""

#: wppa-admin-functions.php:119
msgid "Settings file not found"
msgstr ""

#: wppa-admin-functions.php:219 wppa-admin-functions.php:223 wppa-ajax.php:3294
#: wppa-ajax.php:3301
msgid "Please supply a numeric value greater than or equal to"
msgstr ""

#: wppa-admin-functions.php:219 wppa-admin-functions.php:223 wppa-ajax.php:3294
#: wppa-ajax.php:3301
msgid "for"
msgstr ""

#: wppa-admin-functions.php:223 wppa-ajax.php:3301
msgid "and less than or equal to"
msgstr ""

#: wppa-admin-functions.php:238 wppa-album-admin-autosave.php:398
msgid "--- public ---"
msgstr ""

#: wppa-admin-functions.php:518
#, php-format
msgid "File %s is of an unsupported filetype and has been removed."
msgstr ""

#: wppa-admin-functions.php:637 wppa-admin-functions.php:676
#: wppa-admin-functions.php:680 wppa-admin-functions.php:684
msgid "Unexpected error:"
msgstr ""

#: wppa-admin-functions.php:637
msgid "Missing database table:"
msgstr ""

#: wppa-admin-functions.php:676
msgid "Missing directory:"
msgstr ""

#: wppa-admin-functions.php:680
msgid "Directory is not writable:"
msgstr ""

#: wppa-admin-functions.php:684
msgid "Directory is not readable:"
msgstr ""

#: wppa-admin-functions.php:692
msgid ""
"Please de-activate and re-activate the plugin. If this problem persists, ask "
"your administrator."
msgstr ""

#: wppa-admin-functions.php:721
msgid "Prev page"
msgstr ""

#: wppa-admin-functions.php:876 wppa-admin-functions.php:879
#, php-format
msgid "Album %s is full"
msgstr ""

#: wppa-admin-functions.php:897
#, php-format
msgid "Photo %s already exists in album number %s. Removed from depot."
msgstr ""

#: wppa-admin-functions.php:900
#, php-format
msgid "Photo %s already exists in album number %s."
msgstr ""

#: wppa-admin-functions.php:924
msgid "Not found"
msgstr ""

#: wppa-admin-functions.php:936
#, php-format
msgid "ERROR: Attempt to upload a photo that is too large to process (%s)."
msgstr ""

#: wppa-admin-functions.php:937
msgid "Too big"
msgstr ""

#: wppa-admin-functions.php:941
msgid ""
"WARNING: You are uploading photos that are too small. Photos must be larger "
"than the thumbnail size and larger than the coverphotosize."
msgstr ""

#: wppa-admin-functions.php:942
msgid "Too small"
msgstr ""

#: wppa-admin-functions.php:949
msgid "ERROR: Unable to retrieve image size of"
msgstr ""

#: wppa-admin-functions.php:949
msgid "Are you sure it is a photo?"
msgstr ""

#: wppa-admin-functions.php:950
msgid "No photo found"
msgstr ""

#: wppa-admin-functions.php:960
msgid "Unsupported mime type encountered:"
msgstr ""

#: wppa-admin-functions.php:977
msgid "Album not known while trying to add a photo"
msgstr ""

#: wppa-admin-functions.php:981
#, php-format
msgid ""
"Album %s does not exist or is not accessible while trying to add a photo"
msgstr ""

#: wppa-admin-functions.php:1000
msgid "Could not insert photo."
msgstr ""

#: wppa-admin-functions.php:1048
msgid "ERROR: Unknown file or album."
msgstr ""

#: wppa-admin-functions.php:1162
#, php-format
msgid ""
"There are <strong>%d</strong> albums and <strong>%d</strong> photos in the "
"system."
msgstr ""

#: wppa-admin-functions.php:1163
#, php-format
msgid "<strong>%d</strong> photos are pending moderation."
msgstr ""

#: wppa-admin-functions.php:1164
#, php-format
msgid "<strong>%d</strong> photos are scheduled for later publishing."
msgstr ""

#: wppa-admin-functions.php:1167
#, php-format
msgid "The most recently added album is <strong>%s</strong> (%d)."
msgstr ""

#: wppa-admin-functions.php:1179
#, php-format
msgid "The most recently added photo is <strong>%s</strong> (%d)"
msgstr ""

#: wppa-admin-functions.php:1180
#, php-format
msgid "in album <strong>%s</strong> (%d)."
msgstr ""

#: wppa-admin-functions.php:1182
msgid "Deleted"
msgstr ""

#: wppa-admin.php:56
msgid "Photo&thinsp;Albums"
msgstr ""

#: wppa-admin.php:59 wppa-adminbar.php:40 wppa-settings-autosave.php:6264
msgid "Album Admin"
msgstr ""

#: wppa-admin.php:60 wppa-adminbar.php:47 wppa-upload-widget.php:30
#: wppa-upload-widget.php:92 wppa-upload.php:126
msgid "Upload Photos"
msgstr ""

#: wppa-admin.php:63 wppa-adminbar.php:53
msgid "Edit Photos"
msgstr ""

#: wppa-admin.php:65 wppa-adminbar.php:61 wppa-import.php:168
msgid "Import Photos"
msgstr ""

#: wppa-admin.php:66 wppa-adminbar.php:68
msgid "Moderate Photos"
msgstr ""

#: wppa-admin.php:67 wppa-adminbar.php:75 wppa-export.php:32
msgid "Export Photos"
msgstr ""

#: wppa-admin.php:68 wppa-adminbar.php:82 wppa-settings-autosave.php:6269
msgid "Settings"
msgstr ""

#: wppa-admin.php:69
msgid "Photo of the day Widget"
msgstr ""

#: wppa-admin.php:69 wppa-adminbar.php:89 wppa-potd-widget.php:64
#: wppa-settings-autosave.php:6270 wppa-setup.php:1744
msgid "Photo of the day"
msgstr ""

#: wppa-admin.php:70
msgid "Manage comments"
msgstr ""

#: wppa-admin.php:70 wppa-adminbar.php:96 wppa-settings-autosave.php:373
#: wppa-settings-autosave.php:3189 wppa-settings-autosave.php:6271
#: wppa-settings-autosave.php:9195 wppa-settings-autosave.php:9240
msgid "Comments"
msgstr ""

#: wppa-admin.php:71
msgid "Help &amp; Info"
msgstr ""

#: wppa-admin.php:71 wppa-adminbar.php:103 wppa-settings-autosave.php:6272
msgid "Documentation"
msgstr ""

#: wppa-admin.php:73 wppa-adminbar.php:111
msgid "Logfile"
msgstr ""

#: wppa-admin.php:114
msgid "Uploading is temporary disabled for you"
msgstr ""

#: wppa-admin.php:121
msgid "Editing is temporary disabled for you"
msgstr ""

#: wppa-admin.php:128
msgid "Importing is temporary disabled for you"
msgstr ""

#: wppa-admin.php:131 wppa-import.php:1265 wppa-settings-autosave.php:6266
msgid "Import"
msgstr ""

#: wppa-admin.php:131 wppa-comment-admin.php:115
#: wppa-settings-autosave.php:10511
msgid "Update"
msgstr ""

#: wppa-adminbar.php:120 wppa-album-navigator-widget.php:42
#: wppa-album-widget.php:37 wppa-album-widget.php:47
msgid "Photo Albums"
msgstr ""

#: wppa-adminbar.php:150 wppa-featen-widget.php:204 wppa-lasten-widget.php:207
#: wppa-potd-admin.php:331 wppa-search-widget.php:134
#: wppa-slideshow-widget.php:197 wppa-thumbnail-widget.php:190
#: wppa-topten-widget.php:331 wppa-topten-widget.php:372
#: wppa-upload-widget.php:101
msgid "Album"
msgstr ""

#: wppa-admins-choice-widget.php:14
msgid "Display admins choice of photos download links"
msgstr ""

#: wppa-admins-choice-widget.php:15
msgid "WPPA+ Admins Choice"
msgstr ""

#: wppa-admins-choice-widget.php:35 wppa-admins-choice-widget.php:83
#: wppa-settings-autosave.php:3420
msgid "Admins Choice"
msgstr ""

#: wppa-admins-choice-widget.php:48
msgid "This feature is not enabled"
msgstr ""

#: wppa-admins-choice-widget.php:78
msgid "Please enable this feature in Table IV-A27"
msgstr ""

#: wppa-admins-choice-widget.php:87 wppa-album-navigator-widget.php:98
#: wppa-album-widget.php:325 wppa-bestof-widget.php:129
#: wppa-comment-widget.php:122 wppa-featen-widget.php:196 wppa-gp-widget.php:85
#: wppa-lasten-widget.php:197 wppa-multitag-widget.php:76
#: wppa-potd-widget.php:216 wppa-qr-widget.php:92 wppa-search-widget.php:104
#: wppa-slideshow-widget.php:189 wppa-stereo-widget.php:69
#: wppa-super-view-widget.php:73 wppa-tagcloud-widget.php:72
#: wppa-thumbnail-widget.php:179 wppa-topten-widget.php:326
#: wppa-upldr-widget.php:198 wppa-upload-widget.php:96
msgid "Title"
msgstr ""

#: wppa-ajax.php:208
msgid ""
"All modifications are instantly updated on the server.<br />The <b style="
"\"color:#070\" >Remark</b> field keeps you informed on the actions taken at "
"the background."
msgstr ""

#: wppa-ajax.php:211
msgid "Exit & Refresh"
msgstr ""

#: wppa-ajax.php:284 wppa-ajax.php:353 wppa-ajax.php:388 wppa-ajax.php:670
#: wppa-ajax.php:685 wppa-ajax.php:1022 wppa-ajax.php:1046 wppa-ajax.php:3251
msgid "Security check failure"
msgstr ""

#: wppa-ajax.php:318 wppa-ajax.php:377
msgid "You do not have the rights to moderate photos this way"
msgstr ""

#: wppa-ajax.php:334 wppa-ajax.php:1354 wppa-comment-admin.php:244
#: wppa-functions.php:2436
msgid "Photo comment approved"
msgstr ""

#: wppa-ajax.php:350
#, php-format
msgid "Failed to update stutus of photo %s"
msgstr ""

#: wppa-ajax.php:350 wppa-ajax.php:357
msgid "Please refresh the page"
msgstr ""

#: wppa-ajax.php:357
#, php-format
msgid "Failed to update stutus of comment %s"
msgstr ""

#: wppa-ajax.php:372 wppa-ajax.php:392
msgid "Photo removed"
msgstr ""

#: wppa-ajax.php:402
msgid "Comment removed"
msgstr ""

#: wppa-ajax.php:404
msgid "Could not remove comment"
msgstr ""

#: wppa-ajax.php:407 wppa-ajax.php:888
msgid "Unexpected error"
msgstr ""

#: wppa-ajax.php:413
msgid "This feature is not enabled on this website"
msgstr ""

#: wppa-ajax.php:425
msgid "The album is empty"
msgstr ""

#: wppa-ajax.php:434 wppa-ajax.php:513 wppa-ajax.php:633
msgid "Unable to create zip archive"
msgstr ""

#: wppa-ajax.php:446
#, php-format
msgid "Unable to create zip archive. code = %s"
msgstr ""

#: wppa-ajax.php:483
#, php-format
msgid "Only %s out of %s photos could be added to the zipfile"
msgstr ""

#: wppa-ajax.php:521
msgid "Unable to create zipsdir"
msgstr ""

#: wppa-ajax.php:545 wppa-functions.php:2175 wppa-thumbnails.php:659
msgid "Selected"
msgstr ""

#: wppa-ajax.php:575
msgid "Unknown source of request"
msgstr ""

#: wppa-ajax.php:593
msgid "Empty filename"
msgstr ""

#: wppa-ajax.php:619
msgid "Unable to create tempdir"
msgstr ""

#: wppa-ajax.php:643
msgid "Unknown type"
msgstr ""

#: wppa-ajax.php:652
msgid "The photo does no longer exist"
msgstr ""

#: wppa-ajax.php:704
msgid "An error occurred while processing you rating request."
msgstr ""

#: wppa-ajax.php:705
msgid "Maybe you opened the page too long ago to recognize you."
msgstr ""

#: wppa-ajax.php:706
msgid "You may refresh the page and try again."
msgstr ""

#: wppa-ajax.php:707
msgid ""
"Althoug an error occurred while processing your rating, your vote has been "
"registered."
msgstr ""

#: wppa-ajax.php:708
msgid "However, this may not be reflected in the current pageview"
msgstr ""

#: wppa-ajax.php:737
msgid "Photo has been removed."
msgstr ""

#: wppa-ajax.php:749 wppa-slideshow.php:960
msgid "Sorry, you can not rate your own photos"
msgstr ""

#: wppa-ajax.php:761
msgid "Please enter a comment."
msgstr ""

#: wppa-ajax.php:817
msgid "You can not change your vote"
msgstr ""

#: wppa-ajax.php:823
msgid "You can not change a dislike"
msgstr ""

#: wppa-ajax.php:829
msgid "You can not change your vote into a dislike"
msgstr ""

#: wppa-ajax.php:845 wppa-ajax.php:866
msgid "Photo rated"
msgstr ""

#: wppa-ajax.php:972
msgid ""
"Please explain your vote in a comment.\n"
"Your vote will be discarded if you don't.\n"
"\n"
"After completing your comment,\n"
"you can refresh the page to see\n"
"your vote became effective."
msgstr ""

#: wppa-ajax.php:1018
msgid "You do not have the rights to delete a photo"
msgstr ""

#: wppa-ajax.php:1029
#, php-format
msgid "Photo %s has been deleted"
msgstr ""

#: wppa-ajax.php:1042
msgid "You do not have the rights to undelete a photo"
msgstr ""

#: wppa-ajax.php:1053
#, php-format
msgid "Photo %s has been undeleted"
msgstr ""

#: wppa-ajax.php:1056
#, php-format
msgid "Could not undelete photo %s"
msgstr ""

#: wppa-ajax.php:1070
msgid "You do not have the rights to update album information"
msgstr ""

#: wppa-ajax.php:1082 wppa-ajax.php:2486
msgid "Ratings cleared"
msgstr ""

#: wppa-ajax.php:1082 wppa-ajax.php:1088 wppa-photo-admin-autosave.php:803
msgid "No ratings for this photo."
msgstr ""

#: wppa-ajax.php:1085
msgid "An error occurred while clearing ratings"
msgstr ""

#: wppa-ajax.php:1088 wppa-ajax.php:1109 wppa-ajax.php:1131
msgid "No photos in this album"
msgstr ""

#: wppa-ajax.php:1103
msgid "Tags set to defaults"
msgstr ""

#: wppa-ajax.php:1106
msgid "An error occurred while setting tags"
msgstr ""

#: wppa-ajax.php:1125
msgid "Tags added with defaults"
msgstr ""

#: wppa-ajax.php:1128
msgid "An error occurred while adding tags"
msgstr ""

#: wppa-ajax.php:1154
msgid "No subalbums found to process"
msgstr ""

#: wppa-ajax.php:1159
msgid "No categories found to process"
msgstr ""

#: wppa-ajax.php:1163
#, php-format
msgid "%d album updated"
msgid_plural "%d albums updated"
msgstr[0] ""

#: wppa-ajax.php:1170
#, php-format
msgid "Album name may not be empty.<br />Reset to %s"
msgstr ""

#: wppa-ajax.php:1172 wppa-ajax.php:1847 wppa-album-admin-autosave.php:488
#: wppa-album-admin-autosave.php:537 wppa-album-admin-autosave.php:1462
#: wppa-album-admin-autosave.php:1600 wppa-album-admin-autosave.php:1835
#: wppa-album-admin-autosave.php:1931 wppa-boxes-html.php:450
#: wppa-boxes-html.php:564 wppa-photo-admin-autosave.php:2525
#: wppa-photo-admin-autosave.php:2721 wppa-photo-admin-autosave.php:3145
#: wppa-potd-admin.php:71 wppa-potd-admin.php:358
#: wppa-settings-autosave.php:518 wppa-settings-autosave.php:689
#: wppa-settings-autosave.php:711 wppa-settings-autosave.php:1501
#: wppa-settings-autosave.php:1522 wppa-settings-autosave.php:3086
#: wppa-settings-autosave.php:3107 wppa-settings-autosave.php:3492
#: wppa-settings-autosave.php:3516 wppa-settings-autosave.php:4081
#: wppa-settings-autosave.php:4210 wppa-settings-autosave.php:4927
#: wppa-settings-autosave.php:4948 wppa-settings-autosave.php:5143
#: wppa-settings-autosave.php:5167 wppa-settings-autosave.php:6230
#: wppa-settings-autosave.php:6746 wppa-settings-autosave.php:6995
#: wppa-settings-autosave.php:7017 wppa-settings-autosave.php:7868
#: wppa-settings-autosave.php:7892 wppa-settings-autosave.php:9189
#: wppa-settings-autosave.php:9912 wppa-settings-autosave.php:10068
#: wppa-thumbnail-widget.php:195 wppa-upload.php:290
msgid "Name"
msgstr "姓名"

#: wppa-ajax.php:1175 wppa-ajax.php:1850 wppa-album-admin-autosave.php:1472
#: wppa-album-admin-autosave.php:1610 wppa-album-admin-autosave.php:1845
#: wppa-album-admin-autosave.php:1941 wppa-photo-admin-autosave.php:2526
#: wppa-photo-admin-autosave.php:2722 wppa-photo-admin-autosave.php:3146
#: wppa-potd-admin.php:72 wppa-potd-admin.php:138 wppa-potd-admin.php:359
#: wppa-settings-autosave.php:519 wppa-settings-autosave.php:690
#: wppa-settings-autosave.php:712 wppa-settings-autosave.php:1502
#: wppa-settings-autosave.php:1523 wppa-settings-autosave.php:3087
#: wppa-settings-autosave.php:3108 wppa-settings-autosave.php:3493
#: wppa-settings-autosave.php:3517 wppa-settings-autosave.php:4928
#: wppa-settings-autosave.php:4949 wppa-settings-autosave.php:5144
#: wppa-settings-autosave.php:5168 wppa-settings-autosave.php:6231
#: wppa-settings-autosave.php:6747 wppa-settings-autosave.php:6996
#: wppa-settings-autosave.php:7018 wppa-settings-autosave.php:7869
#: wppa-settings-autosave.php:7893 wppa-settings-autosave.php:9913
#: wppa-settings-autosave.php:9935 wppa-settings-autosave.php:9975
#: wppa-settings-autosave.php:9997 wppa-settings-autosave.php:10043
#: wppa-settings-autosave.php:10069
msgid "Description"
msgstr ""

#: wppa-ajax.php:1179
msgid "Unbalanced tags in album description!"
msgstr ""

#: wppa-ajax.php:1186 wppa-album-admin-autosave.php:427
msgid "Album order #"
msgstr ""

#: wppa-ajax.php:1189
msgid "Cover photo"
msgstr ""

#: wppa-ajax.php:1192
msgid "Parent album"
msgstr ""

#: wppa-ajax.php:1197 wppa-settings-autosave.php:4075
msgid "Photo order"
msgstr ""

#: wppa-ajax.php:1200
msgid "Use Alt thumbsize"
msgstr ""

#: wppa-ajax.php:1203
msgid "Cover Type"
msgstr ""

#: wppa-ajax.php:1206 wppa-settings-autosave.php:5169
#: wppa-settings-autosave.php:6232
msgid "Link type"
msgstr ""

#: wppa-ajax.php:1209 wppa-album-covers.php:1390 wppa-bestof-widget.php:200
#: wppa-slideshow-widget.php:276
msgid "Link to"
msgstr "链接到"

#: wppa-ajax.php:1212 wppa-ajax.php:1871 wppa-album-admin-autosave.php:1483
#: wppa-album-admin-autosave.php:1621 wppa-album-admin-autosave.php:1856
#: wppa-album-admin-autosave.php:1952 wppa-boxes-html.php:572
#: wppa-photo-admin-autosave.php:2528 wppa-photo-admin-autosave.php:2724
#: wppa-potd-admin.php:139 wppa-topten-widget.php:369
msgid "Owner"
msgstr "所有者"

#: wppa-ajax.php:1214
#, php-format
msgid "User %s does not exist"
msgstr ""

#: wppa-ajax.php:1219 wppa-ajax.php:1225
msgid "Upload limit count"
msgstr ""

#: wppa-ajax.php:1232
msgid "Upload limit time"
msgstr ""

#: wppa-ajax.php:1236
msgid "Default tags"
msgstr ""

#: wppa-ajax.php:1241
msgid "Categories"
msgstr ""

#: wppa-ajax.php:1244
msgid "Sub albums sort order"
msgstr ""

#: wppa-ajax.php:1252 wppa-ajax.php:1940
msgid "Schedule date/time"
msgstr ""

#: wppa-ajax.php:1265 wppa-ajax.php:1317
#, php-format
msgid "%s of album %s updated"
msgstr ""

#: wppa-ajax.php:1273
msgid "All photos set to scheduled per date"
msgstr ""

#: wppa-ajax.php:1300
#, php-format
msgid "Custom field %s updated"
msgstr ""

#: wppa-ajax.php:1326
#, php-format
msgid "An error occurred while trying to update %s of album %s"
msgstr ""

#: wppa-ajax.php:1327 wppa-ajax.php:1806
msgid "Press CTRL+F5 and try again."
msgstr ""

#: wppa-ajax.php:1341
msgid "You do not have the rights to update comment status"
msgstr ""

#: wppa-ajax.php:1360
#, php-format
msgid "Status of comment #%s updated"
msgstr ""

#: wppa-ajax.php:1363
#, php-format
msgid "Error updating status comment #%s"
msgstr ""

#: wppa-ajax.php:1374
msgid "You do not have the rights to change photos"
msgstr ""

#: wppa-ajax.php:1383
msgid "Watermark applied"
msgstr ""

#: wppa-ajax.php:1387
msgid "An error occured while trying to apply a watermark"
msgstr ""

#: wppa-ajax.php:1406
msgid "You do not have the rights to update photo information"
msgstr ""

#: wppa-ajax.php:1412
#, php-format
msgid "%s updated to %s."
msgstr ""

#: wppa-ajax.php:1462
#, php-format
msgid "Format error %s. Must be yyyy:mm:dd hh:mm:ss"
msgstr ""

#: wppa-ajax.php:1466
msgid "Exif date/time updated"
msgstr ""

#: wppa-ajax.php:1472
msgid "Enter a value > -90 and < 90"
msgstr ""

#: wppa-ajax.php:1480
msgid "Lattitude updated"
msgstr ""

#: wppa-ajax.php:1482
msgid "Could not update latitude"
msgstr ""

#: wppa-ajax.php:1488
msgid "Enter a value > -180 and < 180"
msgstr ""

#: wppa-ajax.php:1496
msgid "Longitude updated"
msgstr ""

#: wppa-ajax.php:1498
msgid "Could not update longitude"
msgstr ""

#: wppa-ajax.php:1506
msgid "Photo files remade"
msgstr ""

#: wppa-ajax.php:1509
msgid "Could not remake files"
msgstr ""

#: wppa-ajax.php:1515
msgid "Thumbnail remade"
msgstr ""

#: wppa-ajax.php:1518
msgid "Could not remake thumbnail"
msgstr ""

#: wppa-ajax.php:1529 wppa-potd-admin.php:100 wppa-settings-autosave.php:3885
#: wppa-settings-autosave.php:9888 wppa-tinymce-shortcodes.php:628
msgid "left"
msgstr ""

#: wppa-ajax.php:1532
msgid "180&deg;"
msgstr ""

#: wppa-ajax.php:1535 wppa-potd-admin.php:100 wppa-settings-autosave.php:3885
#: wppa-settings-autosave.php:9890 wppa-tinymce-shortcodes.php:630
msgid "right"
msgstr ""

#: wppa-ajax.php:1548 wppa-ajax.php:1551
msgid "Photo flipped"
msgstr ""

#: wppa-ajax.php:1554
#, php-format
msgid "Photo %s rotated %s"
msgstr ""

#: wppa-ajax.php:1558
#, php-format
msgid "An error occurred while trying to rotate or flip photo %s"
msgstr ""

#: wppa-ajax.php:1666
#, php-format
msgid "An error occurred while trying to process photo %s"
msgstr ""

#: wppa-ajax.php:1702 wppa-ajax.php:1749
#, php-format
msgid "Command %s magically executed on photo %s"
msgstr ""

#: wppa-ajax.php:1759 wppa-ajax.php:1789 wppa-photo-admin-autosave.php:2141
#: wppa-photo-admin-autosave.php:2159
#, php-format
msgid "A photo with filename %s already exists in album %s."
msgstr ""

#: wppa-ajax.php:1765 wppa-ajax.php:1795 wppa-functions.php:4309
#, php-format
msgid "Album %s does not exist"
msgstr ""

#: wppa-ajax.php:1775
#, php-format
msgid "Photo %s has been moved to album %s (%s)"
msgstr ""

#: wppa-ajax.php:1779
#, php-format
msgid "An error occurred while trying to move photo %s"
msgstr ""

#: wppa-ajax.php:1802
#, php-format
msgid "Photo %s copied to album %s (%s)"
msgstr ""

#: wppa-ajax.php:1805
#, php-format
msgid "An error occurred while trying to copy photo %s"
msgstr ""

#: wppa-ajax.php:1834
msgid "Photo approved"
msgstr ""

#: wppa-ajax.php:1835
#, php-format
msgid "Your recently uploaded photo %s in album %s has been approved"
msgstr ""

#: wppa-ajax.php:1843 wppa-ajax.php:1891 wppa-comment-admin.php:186
#: wppa-photo-admin-autosave.php:2527 wppa-photo-admin-autosave.php:2723
#: wppa-settings-autosave.php:7021 wppa-settings-autosave.php:7872
#: wppa-settings-autosave.php:9936 wppa-settings-autosave.php:9976
#: wppa-settings-autosave.php:9998 wppa-settings-autosave.php:10044
msgid "Status"
msgstr ""

#: wppa-ajax.php:1854 wppa-ajax.php:2589
msgid "Unbalanced tags in photo description!"
msgstr ""

#: wppa-ajax.php:1860
msgid "Photo order #"
msgstr ""

#: wppa-ajax.php:1865
#, php-format
msgid "User %s does not exists"
msgstr ""

#: wppa-ajax.php:1874
msgid "Link url"
msgstr ""

#: wppa-ajax.php:1877
msgid "Link title"
msgstr ""

#: wppa-ajax.php:1880
msgid "Link target"
msgstr ""

#: wppa-ajax.php:1886
msgid "Photo Tags"
msgstr ""

#: wppa-ajax.php:1894
msgid "HTML Alt"
msgstr ""

#: wppa-ajax.php:1898
msgid "Video width"
msgstr ""

#: wppa-ajax.php:1900 wppa-ajax.php:1907
msgid "Please enter an integer value >= 0"
msgstr ""

#: wppa-ajax.php:1905
msgid "Video height"
msgstr ""

#: wppa-ajax.php:1923 wppa-ajax.php:1956 wppa-ajax.php:1991
#, php-format
msgid "%s of video %s updated"
msgstr ""

#: wppa-ajax.php:1926 wppa-ajax.php:1959 wppa-ajax.php:1994
#, php-format
msgid "%s of photo %s updated"
msgstr ""

#: wppa-ajax.php:1930
#, php-format
msgid "An error occurred while trying to update %s of photo %s"
msgstr ""

#: wppa-ajax.php:1968
msgid "Delete date/time"
msgstr ""

#: wppa-ajax.php:2000
#, php-format
msgid "Scheduled deletion of photo %s cancelled"
msgstr ""

#: wppa-ajax.php:2003
msgid "No rights"
msgstr ""

#: wppa-ajax.php:2029
#, php-format
msgid "Custom field %s of photo %s updated"
msgstr ""

#: wppa-ajax.php:2036
msgid "Error during upload."
msgstr ""

#: wppa-ajax.php:2070
msgid "Photo files updated."
msgstr ""

#: wppa-ajax.php:2075
msgid "Could not update files."
msgstr ""

#: wppa-ajax.php:2086
#, php-format
msgid "Stereo mode updated in %d milliseconds"
msgstr ""

#: wppa-ajax.php:2103 wppa-ajax.php:2145
msgid "You do not have the rights to update settings"
msgstr ""

#: wppa-ajax.php:2139
msgid "You do not have the rights to update photo of the day settings"
msgstr ""

#: wppa-ajax.php:2217
msgid "Capability granted"
msgstr ""

#: wppa-ajax.php:2222
msgid "Capability withdrawn"
msgstr ""

#: wppa-ajax.php:2240 wppa-ajax.php:3124
#, php-format
msgid "Setting %s updated to %s"
msgstr ""

#: wppa-ajax.php:2273 wppa-ajax.php:2274
msgid "Failed to save code"
msgstr ""

#: wppa-ajax.php:2298
msgid "Column width."
msgstr ""

#: wppa-ajax.php:2301
msgid "Initial width."
msgstr ""

#: wppa-ajax.php:2304
msgid "Full size."
msgstr ""

#: wppa-ajax.php:2307
msgid "Max height."
msgstr ""

#: wppa-ajax.php:2312
msgid "Thumbnail size."
msgstr ""

#: wppa-ajax.php:2316
msgid "Thumbnail frame width"
msgstr ""

#: wppa-ajax.php:2320
msgid "Thumbnail frame height"
msgstr ""

#: wppa-ajax.php:2323
msgid "Thumbnail Spacing"
msgstr ""

#: wppa-ajax.php:2326
msgid "Photocount treshold."
msgstr ""

#: wppa-ajax.php:2329
msgid "Thumb page size."
msgstr ""

#: wppa-ajax.php:2332 wppa-ajax.php:2335
msgid "Cover photo size."
msgstr ""

#: wppa-ajax.php:2338
msgid "Album page size."
msgstr ""

#: wppa-ajax.php:2341
msgid "Number of TopTen photos"
msgstr ""

#: wppa-ajax.php:2344
msgid "Widget image thumbnail size"
msgstr ""

#: wppa-ajax.php:2347 wppa-settings-autosave.php:1117
msgid "Max Cover width"
msgstr ""

#: wppa-ajax.php:2350
msgid "Minimal description height"
msgstr ""

#: wppa-ajax.php:2353
msgid "Minimal cover height"
msgstr ""

#: wppa-ajax.php:2356
msgid "Minimal text frame height"
msgstr ""

#: wppa-ajax.php:2359 wppa-settings-autosave.php:1445
msgid "Border width"
msgstr ""

#: wppa-ajax.php:2362 wppa-settings-autosave.php:788
#: wppa-settings-autosave.php:1454
msgid "Border radius"
msgstr ""

#: wppa-ajax.php:2365 wppa-settings-autosave.php:799
msgid "Box spacing"
msgstr ""

#: wppa-ajax.php:2371 wppa-settings-autosave.php:1092
msgid "Popup size"
msgstr ""

#: wppa-ajax.php:2374
msgid "Fullsize border width"
msgstr ""

#: wppa-ajax.php:2377
msgid "Lightbox Bordersize"
msgstr ""

#: wppa-ajax.php:2380
msgid "Lightbox Borderwidth"
msgstr ""

#: wppa-ajax.php:2383
msgid "Lightbox Borderradius"
msgstr ""

#: wppa-ajax.php:2386
msgid "Number of Comment widget entries"
msgstr ""

#: wppa-ajax.php:2389
msgid "Comment Widget image thumbnail size"
msgstr ""

#: wppa-ajax.php:2392 wppa-ajax.php:2395 wppa-ajax.php:2398
msgid "Opacity."
msgstr ""

#: wppa-ajax.php:2407 wppa-settings-autosave.php:1254
msgid "Avatar size"
msgstr ""

#: wppa-ajax.php:2410 wppa-ajax.php:2413
msgid "Watermark opacity"
msgstr ""

#: wppa-ajax.php:2416 wppa-settings-autosave.php:1423
msgid "Number of text lines"
msgstr ""

#: wppa-ajax.php:2419 wppa-settings-autosave.php:4784
msgid "Overlay opacity"
msgstr ""

#: wppa-ajax.php:2422 wppa-settings-autosave.php:8516
msgid "Upload limit"
msgstr ""

#: wppa-ajax.php:2425 wppa-settings-autosave.php:4416
msgid "Notify inappropriate"
msgstr ""

#: wppa-ajax.php:2428
msgid "Dislike pending"
msgstr ""

#: wppa-ajax.php:2431
msgid "Dislike delete"
msgstr ""

#: wppa-ajax.php:2434 wppa-settings-autosave.php:7968
msgid "Max execution time"
msgstr ""

#: wppa-ajax.php:2440
msgid "myCRED / Cube Points"
msgstr ""

#: wppa-ajax.php:2443
msgid "JPG Image quality"
msgstr ""

#: wppa-ajax.php:2449 wppa-settings-autosave.php:4299
msgid "Number of coverphotos"
msgstr ""

#: wppa-ajax.php:2452 wppa-settings-autosave.php:4429
msgid "Dislike value"
msgstr ""

#: wppa-ajax.php:2455 wppa-settings-autosave.php:929
msgid "Slideshow pagesize"
msgstr ""

#: wppa-ajax.php:2458 wppa-settings-autosave.php:948
msgid "Slideonly max"
msgstr ""

#: wppa-ajax.php:2461 wppa-settings-autosave.php:817
msgid "Max Pagelinks"
msgstr ""

#: wppa-ajax.php:2478 wppa-settings-autosave.php:844
msgid "Sticky header size"
msgstr ""

#: wppa-ajax.php:2489
msgid "Could not clear ratings"
msgstr ""

#: wppa-ajax.php:2498
msgid "Viewcounts cleared"
msgstr ""

#: wppa-ajax.php:2501
msgid "Could not clear viewcounts"
msgstr ""

#: wppa-ajax.php:2511
msgid "IPTC data cleared"
msgstr ""

#: wppa-ajax.php:2512
msgid "Refresh this page to clear table X"
msgstr ""

#: wppa-ajax.php:2516
msgid "Could not clear IPTC data"
msgstr ""

#: wppa-ajax.php:2526
msgid "EXIF data cleared"
msgstr ""

#: wppa-ajax.php:2527
msgid "Refresh this page to clear table XI"
msgstr ""

#: wppa-ajax.php:2531
msgid "Could not clear EXIF data"
msgstr ""

#: wppa-ajax.php:2539
msgid "Recuperation performed"
msgstr ""

#: wppa-ajax.php:2554
msgid ""
"Illegal format. Please enter a 6 digit hexadecimal color value. Example: "
"#77bbff"
msgstr ""

#: wppa-ajax.php:2580
msgid "You just changed a setting that requires the recalculation of ratings."
msgstr ""

#: wppa-ajax.php:2581 wppa-ajax.php:3138
msgid "Please run the appropriate action in Table VIII."
msgstr ""

#: wppa-ajax.php:2605 wppa-ajax.php:2621
#, php-format
msgid "Unable to create or write to %s"
msgstr ""

#: wppa-ajax.php:2614
msgid "Source can not be inside the wppa folder."
msgstr ""

#: wppa-ajax.php:2631
msgid "The content must contain w#album"
msgstr ""

#: wppa-ajax.php:2638
msgid "The content must contain w#lat and w#lon"
msgstr ""

#: wppa-ajax.php:2686 wppa-ajax.php:2692
msgid "Members"
msgstr ""

#: wppa-ajax.php:2686
msgid "Parent of the member albums"
msgstr ""

#: wppa-ajax.php:2851
#, php-format
msgid "User %s has been blacklisted."
msgstr ""

#: wppa-ajax.php:2854 wppa-ajax.php:2884
#, php-format
msgid "User %s does not exist."
msgstr ""

#: wppa-ajax.php:2881
#, php-format
msgid "User %s is now superuser."
msgstr ""

#: wppa-ajax.php:2906
msgid ""
"The content of the Custom box has been changed to display the Fotomoto "
"toolbar."
msgstr ""

#: wppa-ajax.php:2910
msgid "The display of the custom box has been enabled"
msgstr ""

#: wppa-ajax.php:2921
msgid "The content of the Custom box has been changed to display maps."
msgstr ""

#: wppa-ajax.php:2925
msgid "The display of the custom box has been enabled."
msgstr ""

#: wppa-ajax.php:2930
msgid ""
"The Google maps API will be loaded on all pages to enable the use of Ajax "
"page content changes that use maps."
msgstr ""

#: wppa-ajax.php:3005
msgid ""
"You must run Table VIII-A13 and VIII-A14 first before you can switch to "
"encrypted urls."
msgstr ""

#: wppa-ajax.php:3008
msgid "Table IV-A3 will be switched off."
msgstr ""

#: wppa-ajax.php:3012
msgid "Table IV-A4 will be switched off."
msgstr ""

#: wppa-ajax.php:3022
msgid "Not allowed when cryptic links is active"
msgstr ""

#: wppa-ajax.php:3034
msgid "A Twitter account name must start with an at sign: @"
msgstr ""

#: wppa-ajax.php:3087
msgid "This path does not contain ImageMagick commands"
msgstr ""

#: wppa-ajax.php:3114
#, php-format
msgid "Failed to set %s to %s"
msgstr ""

#: wppa-ajax.php:3137
msgid ""
"You just changed a setting that requires the regeneration of thumbnails."
msgstr ""

#: wppa-ajax.php:3219
msgid "Missing album id"
msgstr ""

#: wppa-ajax.php:3239
msgid "You do not have the rights to delete this album"
msgstr ""

#: wppa-ajax.php:3260
msgid "An error has occurred"
msgstr ""

#: wppa-ajax.php:3296 wppa-ajax.php:3303
msgid "You may also enter:"
msgstr ""

#: wppa-ajax.php:3297 wppa-ajax.php:3304
msgid "You may also leave/set this blank"
msgstr ""

#: wppa-album-admin-autosave.php:23 wppa-album-admin-autosave.php:1111
msgid "Upload to this album"
msgstr ""

#: wppa-album-admin-autosave.php:37
msgid "Warning:"
msgstr ""

#: wppa-album-admin-autosave.php:37
#, php-format
msgid ""
"The uploads directory does not exist or is not writable by the server. "
"Please make sure that %s is writeable by the server."
msgstr ""

#: wppa-album-admin-autosave.php:76
msgid "Edit Single Photo"
msgstr ""

#: wppa-album-admin-autosave.php:77 wppa-album-admin-autosave.php:101
#: wppa-album-admin-autosave.php:122 wppa-album-admin-autosave.php:1140
#: wppa-setup.php:1424
msgid "Edit photo information"
msgstr ""

#: wppa-album-admin-autosave.php:98 wppa-album-admin-autosave.php:1136
msgid "Manage Photos"
msgstr ""

#: wppa-album-admin-autosave.php:99 wppa-album-admin-autosave.php:1137
msgid "Copy / move / delete / edit name / edit description / change status"
msgstr ""

#: wppa-album-admin-autosave.php:100 wppa-album-admin-autosave.php:1139
msgid "Edit photo information except copy and move"
msgstr ""

#: wppa-album-admin-autosave.php:104 wppa-album-admin-autosave.php:113
msgid "Back to album table"
msgstr ""

#: wppa-album-admin-autosave.php:111 wppa-album-admin-autosave.php:1148
msgid "Top of page"
msgstr ""

#: wppa-album-admin-autosave.php:121
msgid "Manage Trashed Photos"
msgstr ""

#: wppa-album-admin-autosave.php:155 wppa-functions.php:4233
#: wppa-settings-autosave.php:8364 wppa-wpdb-insert.php:367
msgid "New Album"
msgstr "添加新相册"

#: wppa-album-admin-autosave.php:160 wppa-import.php:1700 wppa-import.php:1736
#: wppa-import.php:2848
msgid "Could not create album."
msgstr ""

#: wppa-album-admin-autosave.php:168 wppa-import.php:1706 wppa-import.php:2856
msgid "Album #"
msgstr ""

#: wppa-album-admin-autosave.php:168 wppa-import.php:1706 wppa-import.php:2856
msgid "Added."
msgstr ""

#: wppa-album-admin-autosave.php:251
msgid ""
"Are you sure you want to inherit categories to all (grand)children of this "
"album?"
msgstr ""

#: wppa-album-admin-autosave.php:262
msgid ""
"Are you sure you want to add the categories to all (grand)children of this "
"album?"
msgstr ""

#: wppa-album-admin-autosave.php:273
msgid ""
"Are you sure you want to set the default tags to all photos in this album?"
msgstr ""

#: wppa-album-admin-autosave.php:284
msgid ""
"Are you sure you want to add the default tags to all photos in this album?"
msgstr ""

#: wppa-album-admin-autosave.php:296
msgid "Please switch feature on and set date/time to schedule first"
msgstr ""

#: wppa-album-admin-autosave.php:301
msgid "Are you sure you want to schedule all photos in this album?"
msgstr ""

#: wppa-album-admin-autosave.php:315
msgid "Edit Album Information"
msgstr ""

#: wppa-album-admin-autosave.php:318
msgid ""
"All modifications are instantly updated on the server, except for those that "
"require a button push."
msgstr ""

#: wppa-album-admin-autosave.php:319
msgid ""
"The <b style=\"color:#070\" >Remark</b> fields keep you informed on the "
"actions taken at the background."
msgstr ""

#: wppa-album-admin-autosave.php:348
msgid "Album number:"
msgstr ""

#: wppa-album-admin-autosave.php:353 wppa-photo-admin-autosave.php:661
msgid "Crypt:"
msgstr ""

#: wppa-album-admin-autosave.php:358
msgid "Created:"
msgstr ""

#: wppa-album-admin-autosave.php:359 wppa-album-admin-autosave.php:365
#: wppa-photo-admin-autosave.php:667 wppa-photo-admin-autosave.php:704
msgid "local time"
msgstr ""

#: wppa-album-admin-autosave.php:363 wppa-photo-admin-autosave.php:703
msgid "Modified:"
msgstr ""

#: wppa-album-admin-autosave.php:368 wppa-photo-admin-autosave.php:708
#: wppa-photo-admin-autosave.php:2711
msgid "Not modified"
msgstr ""

#: wppa-album-admin-autosave.php:374
msgid "Album Views:"
msgstr ""

#: wppa-album-admin-autosave.php:376
msgid "Photo views:"
msgstr ""

#: wppa-album-admin-autosave.php:379
msgid "Photo views inc sub albums:"
msgstr ""

#: wppa-album-admin-autosave.php:387
msgid "Clicks:"
msgstr ""

#: wppa-album-admin-autosave.php:395 wppa-photo-admin-autosave.php:673
msgid "Owned by:"
msgstr ""

#: wppa-album-admin-autosave.php:438
msgid ""
"Album order # has only effect if you set the album sort order method to "
"<b>Order #</b> in the Photo Albums -> Settings screen.<br />"
msgstr ""

#: wppa-album-admin-autosave.php:444 wppa-tinymce-shortcodes.php:357
#: wppa-tinymce-shortcodes.php:383 wppa-tinymce-shortcodes.php:571
msgid "Parent album:"
msgstr ""

#: wppa-album-admin-autosave.php:485
msgid "Photo order:"
msgstr ""

#: wppa-album-admin-autosave.php:486
msgid "--- default --- See Table IV-C1"
msgstr ""

#: wppa-album-admin-autosave.php:487 wppa-album-admin-autosave.php:535
#: wppa-settings-autosave.php:4080 wppa-settings-autosave.php:4209
#: wppa-thumbnail-widget.php:194
msgid "Order #"
msgstr ""

#: wppa-album-admin-autosave.php:489 wppa-album-admin-autosave.php:534
#: wppa-potd-admin.php:249 wppa-settings-autosave.php:4082
#: wppa-settings-autosave.php:4211 wppa-thumbnail-widget.php:196
msgid "Random"
msgstr ""

#: wppa-album-admin-autosave.php:490 wppa-settings-autosave.php:4083
msgid "Rating mean value"
msgstr ""

#: wppa-album-admin-autosave.php:491 wppa-bestof-widget.php:166
#: wppa-settings-autosave.php:4084 wppa-topten-widget.php:345
msgid "Number of votes"
msgstr ""

#: wppa-album-admin-autosave.php:492 wppa-album-admin-autosave.php:539
#: wppa-comment-admin.php:185 wppa-settings-autosave.php:4085
#: wppa-settings-autosave.php:4212
msgid "Timestamp"
msgstr ""

#: wppa-album-admin-autosave.php:493 wppa-settings-autosave.php:4086
msgid "EXIF Date"
msgstr ""

#: wppa-album-admin-autosave.php:494 wppa-settings-autosave.php:4087
#: wppa-settings-autosave.php:4213
msgid "Order # desc"
msgstr ""

#: wppa-album-admin-autosave.php:495 wppa-settings-autosave.php:4088
#: wppa-settings-autosave.php:4214
msgid "Name desc"
msgstr ""

#: wppa-album-admin-autosave.php:496 wppa-settings-autosave.php:4089
#: wppa-thumbnail-widget.php:197
msgid "Rating mean value desc"
msgstr ""

#: wppa-album-admin-autosave.php:497 wppa-settings-autosave.php:4090
#: wppa-thumbnail-widget.php:198
msgid "Number of votes desc"
msgstr ""

#: wppa-album-admin-autosave.php:498 wppa-settings-autosave.php:4091
#: wppa-settings-autosave.php:4215 wppa-thumbnail-widget.php:199
msgid "Timestamp desc"
msgstr ""

#: wppa-album-admin-autosave.php:499 wppa-settings-autosave.php:4092
msgid "EXIF Date desc"
msgstr ""

#: wppa-album-admin-autosave.php:529
msgid "Sub album sort order:"
msgstr ""

#: wppa-album-admin-autosave.php:533
msgid "--- default --- See Table IV-D1"
msgstr ""

#: wppa-album-admin-autosave.php:536
msgid "Order # reverse"
msgstr ""

#: wppa-album-admin-autosave.php:538
msgid "Name reverse"
msgstr ""

#: wppa-album-admin-autosave.php:540
msgid "Timestamp reverse"
msgstr ""

#: wppa-album-admin-autosave.php:545
msgid "Use alt thumbsize:"
msgstr ""

#: wppa-album-admin-autosave.php:550 wppa-settings-autosave.php:538
#: wppa-settings-autosave.php:547 wppa-settings-autosave.php:556
#: wppa-settings-autosave.php:567 wppa-settings-autosave.php:576
#: wppa-settings-autosave.php:585 wppa-settings-autosave.php:594
#: wppa-settings-autosave.php:603 wppa-settings-autosave.php:612
#: wppa-settings-autosave.php:621 wppa-settings-autosave.php:630
#: wppa-settings-autosave.php:639 wppa-settings-autosave.php:648
#: wppa-settings-autosave.php:657 wppa-settings-autosave.php:666
msgid "no"
msgstr ""

#: wppa-album-admin-autosave.php:553 wppa-settings-autosave.php:538
#: wppa-settings-autosave.php:547 wppa-settings-autosave.php:556
#: wppa-settings-autosave.php:567 wppa-settings-autosave.php:576
#: wppa-settings-autosave.php:585 wppa-settings-autosave.php:594
#: wppa-settings-autosave.php:603 wppa-settings-autosave.php:612
#: wppa-settings-autosave.php:621 wppa-settings-autosave.php:630
#: wppa-settings-autosave.php:639 wppa-settings-autosave.php:648
#: wppa-settings-autosave.php:657 wppa-settings-autosave.php:666
#: wppa-settings-autosave.php:675
msgid "yes"
msgstr ""

#: wppa-album-admin-autosave.php:561
msgid "Cover Type:"
msgstr ""

#: wppa-album-admin-autosave.php:568
msgid "--- default --- See Table IV-D6"
msgstr ""

#: wppa-album-admin-autosave.php:571 wppa-settings-autosave.php:2101
#: wppa-settings-autosave.php:4279
msgid "Standard"
msgstr ""

#: wppa-album-admin-autosave.php:574 wppa-settings-autosave.php:4280
msgid "Long Descriptions"
msgstr ""

#: wppa-album-admin-autosave.php:577 wppa-settings-autosave.php:4281
msgid "Image Factory"
msgstr ""

#: wppa-album-admin-autosave.php:580 wppa-settings-autosave.php:4282
msgid "Standard mcr"
msgstr ""

#: wppa-album-admin-autosave.php:583 wppa-settings-autosave.php:4283
msgid "Long Descriptions mcr"
msgstr ""

#: wppa-album-admin-autosave.php:586 wppa-settings-autosave.php:4284
msgid "Image Factory mcr"
msgstr ""

#: wppa-album-admin-autosave.php:593
msgid "Cover Photo:"
msgstr ""

#: wppa-album-admin-autosave.php:598
msgid "Upload limit:"
msgstr ""

#: wppa-album-admin-autosave.php:610
msgid "Set the upload limit (0 means unlimited)."
msgstr ""

#: wppa-album-admin-autosave.php:616 wppa-settings-autosave.php:6392
#: wppa-settings-autosave.php:8523
msgid "for ever"
msgstr ""

#: wppa-album-admin-autosave.php:617 wppa-album-admin-autosave.php:629
#: wppa-settings-autosave.php:6393 wppa-settings-autosave.php:8524
msgid "per hour"
msgstr ""

#: wppa-album-admin-autosave.php:618 wppa-album-admin-autosave.php:630
#: wppa-settings-autosave.php:6394 wppa-settings-autosave.php:8525
msgid "per day"
msgstr ""

#: wppa-album-admin-autosave.php:619 wppa-album-admin-autosave.php:631
#: wppa-settings-autosave.php:6395 wppa-settings-autosave.php:8526
msgid "per week"
msgstr ""

#: wppa-album-admin-autosave.php:620 wppa-album-admin-autosave.php:632
#: wppa-settings-autosave.php:6396 wppa-settings-autosave.php:8527
msgid "per month"
msgstr ""

#: wppa-album-admin-autosave.php:621 wppa-album-admin-autosave.php:633
#: wppa-settings-autosave.php:6397 wppa-settings-autosave.php:8528
msgid "per year"
msgstr ""

#: wppa-album-admin-autosave.php:625
msgid "Unlimited"
msgstr ""

#: wppa-album-admin-autosave.php:646
msgid "Watermark file:"
msgstr ""

#: wppa-album-admin-autosave.php:653
msgid "Watermark pos:"
msgstr ""

#: wppa-album-admin-autosave.php:663 wppa-photo-admin-autosave.php:920
msgid "Remark:"
msgstr ""

#: wppa-album-admin-autosave.php:668
#, php-format
msgid "Album %s is not modified yet"
msgstr ""

#: wppa-album-admin-autosave.php:691
msgid "Name:"
msgstr ""

#: wppa-album-admin-autosave.php:702
msgid "Type the name of the album. Do not leave this empty."
msgstr ""

#: wppa-album-admin-autosave.php:713 wppa-functions.php:4273
#: wppa-photo-admin-autosave.php:1674 wppa-photo-admin-autosave.php:1725
msgid "Description:"
msgstr ""

#: wppa-album-admin-autosave.php:730
msgid "Update Album description"
msgstr ""

#: wppa-album-admin-autosave.php:762
msgid "Categories:"
msgstr ""

#: wppa-album-admin-autosave.php:775
msgid "Separate categories with commas."
msgstr ""

#: wppa-album-admin-autosave.php:784
msgid "- select to add -"
msgstr ""

#: wppa-album-admin-autosave.php:790
msgid "No categories yet"
msgstr ""

#: wppa-album-admin-autosave.php:801
msgid "Default photo tags:"
msgstr ""

#: wppa-album-admin-autosave.php:814
msgid ""
"Enter the tags that you want to be assigned to new photos in this album."
msgstr ""

#: wppa-album-admin-autosave.php:865
msgid "Link type:"
msgstr ""

#: wppa-album-admin-autosave.php:874 wppa-settings-autosave.php:8753
msgid "the sub-albums and thumbnails"
msgstr ""

#: wppa-album-admin-autosave.php:875 wppa-settings-autosave.php:8754
msgid "the sub-albums"
msgstr ""

#: wppa-album-admin-autosave.php:876 wppa-settings-autosave.php:8755
msgid "the thumbnails"
msgstr ""

#: wppa-album-admin-autosave.php:877 wppa-settings-autosave.php:8756
msgid "the album photos as slideshow"
msgstr ""

#: wppa-album-admin-autosave.php:878
msgid "the link page with a clean url"
msgstr ""

#: wppa-album-admin-autosave.php:879 wppa-settings-autosave.php:8757
msgid "no link at all"
msgstr ""

#: wppa-album-admin-autosave.php:884
msgid ""
"If you select \"the link page with a clean url\", select an Auto Page of one "
"of the photos in this album."
msgstr ""

#: wppa-album-admin-autosave.php:887
msgid ""
"If you select \"the link page with a clean url\", make sure you enter the "
"correct shortcode on the target page."
msgstr ""

#: wppa-album-admin-autosave.php:901 wppa-potd-admin.php:108
#: wppa-potd-admin.php:124 wppa-potd-admin.php:152
msgid "Link to:"
msgstr ""

#: wppa-album-admin-autosave.php:907 wppa-settings-autosave.php:10637
#: wppa-settings-autosave.php:10679 wppa-settings-autosave.php:10731
msgid "There are no pages (yet) to link to."
msgstr ""

#: wppa-album-admin-autosave.php:917 wppa-settings-autosave.php:466
msgid "--- the same page or post ---"
msgstr ""

#: wppa-album-admin-autosave.php:926
msgid ""
"If you want, you can link the title to a WP page instead of the album's "
"content. If so, select the page the title links to."
msgstr ""

#: wppa-album-admin-autosave.php:940
msgid "Schedule:"
msgstr ""

#: wppa-album-admin-autosave.php:955
msgid ""
"If enabled, new photos will have their status set scheduled for publication "
"on the date/time specified here."
msgstr ""

#: wppa-album-admin-autosave.php:981
msgid "Apply categories to all (grand)children."
msgstr ""

#: wppa-album-admin-autosave.php:983
msgid "Inherit Cats"
msgstr ""

#: wppa-album-admin-autosave.php:987
msgid "Add categories to all (grand)children."
msgstr ""

#: wppa-album-admin-autosave.php:989
msgid "Add Inherit Cats"
msgstr ""

#: wppa-album-admin-autosave.php:996
msgid "Tag all photos in this album with the default tags."
msgstr ""

#: wppa-album-admin-autosave.php:998
msgid "Apply default tags"
msgstr ""

#: wppa-album-admin-autosave.php:1002
msgid "Add the default tags to all photos in this album."
msgstr ""

#: wppa-album-admin-autosave.php:1004
msgid "Add default tags"
msgstr ""

#: wppa-album-admin-autosave.php:1011
msgid "Schedule all photos in this album for later publishing."
msgstr ""

#: wppa-album-admin-autosave.php:1013
msgid "Schedule all"
msgstr ""

#: wppa-album-admin-autosave.php:1018
msgid "Are you sure you want to clear the ratings in this album?"
msgstr ""

#: wppa-album-admin-autosave.php:1023
msgid "Reset ratings"
msgstr ""

#: wppa-album-admin-autosave.php:1034
msgid "Apply new photo desc"
msgstr ""

#: wppa-album-admin-autosave.php:1045
msgid "Remake all"
msgstr ""

#: wppa-album-admin-autosave.php:1053 wppa-album-admin-autosave.php:1071
#: wppa-album-admin-autosave.php:1563 wppa-album-admin-autosave.php:2266
msgid "Are you sure you want to create a subalbum?"
msgstr ""

#: wppa-album-admin-autosave.php:1062
msgid "Create child"
msgstr ""

#: wppa-album-admin-autosave.php:1080
msgid "Create sibling"
msgstr ""

#: wppa-album-admin-autosave.php:1092
msgid "Edit parent"
msgstr ""

#: wppa-album-admin-autosave.php:1106
msgid "Change the upload limit or remove photos to enable new uploads."
msgstr ""

#: wppa-album-admin-autosave.php:1110
msgid "Album is full"
msgstr ""

#: wppa-album-admin-autosave.php:1111
#, php-format
msgid "(max %d)"
msgstr ""

#: wppa-album-admin-autosave.php:1138
msgid "Change sequence order by drag and drop"
msgstr ""

#: wppa-album-admin-autosave.php:1160
msgid "Moderate comment"
msgstr ""

#: wppa-album-admin-autosave.php:1177
msgid "Moderate photo"
msgstr ""

#: wppa-album-admin-autosave.php:1178 wppa-functions.php:2470
#: wppa-photo-admin-autosave.php:25
msgid "Edit photo"
msgstr "编辑照片"

#: wppa-album-admin-autosave.php:1198 wppa-boxes-html.php:1896
#: wppa-boxes-html.php:1899
msgid "Delete Album"
msgstr ""

#: wppa-album-admin-autosave.php:1200 wppa-boxes-html.php:855
#: wppa-breadcrumb.php:330 wppa-breadcrumb.php:354 wppa-breadcrumb.php:384
#: wppa-breadcrumb.php:404 wppa-breadcrumb.php:498 wppa-breadcrumb.php:525
#: wppa-breadcrumb.php:698 wppa-upload.php:187 wppa-upload.php:330
#: wppa-upload.php:407
msgid "Album:"
msgstr "专辑"

#: wppa-album-admin-autosave.php:1201
msgid "Are you sure you want to delete this album?"
msgstr ""

#: wppa-album-admin-autosave.php:1202
msgid "Press Delete to continue, and Cancel to go back."
msgstr ""

#: wppa-album-admin-autosave.php:1207
msgid "What would you like to do with photos currently in the album?"
msgstr ""

#: wppa-album-admin-autosave.php:1208 wppa-album-admin-autosave.php:1217
#: wppa-album-admin-autosave.php:1519 wppa-album-admin-autosave.php:1559
#: wppa-album-admin-autosave.php:1657 wppa-album-admin-autosave.php:1892
#: wppa-album-admin-autosave.php:1988 wppa-album-admin-autosave.php:2262
#: wppa-boxes-html.php:970 wppa-comment-admin.php:142
#: wppa-comment-admin.php:205 wppa-functions.php:2163 wppa-links.php:816
#: wppa-links.php:834 wppa-photo-admin-autosave.php:2428
#: wppa-photo-admin-autosave.php:2572 wppa-thumbnails.php:673
msgid "Delete"
msgstr "删除"

#: wppa-album-admin-autosave.php:1209
msgid "Move to:"
msgstr ""

#: wppa-album-admin-autosave.php:1216 wppa-photo-admin-autosave.php:3276
#: wppa-settings-autosave.php:3001
msgid "Cancel"
msgstr ""

#: wppa-album-admin-autosave.php:1247
msgid "Unable to move photos. Album not deleted."
msgstr ""

#: wppa-album-admin-autosave.php:1265
msgid "Manage Albums"
msgstr ""

#: wppa-album-admin-autosave.php:1271
msgid "Are you sure you want to create a new album?"
msgstr ""

#: wppa-album-admin-autosave.php:1276
msgid "Create New Empty Album"
msgstr ""

#: wppa-album-admin-autosave.php:1282
msgid "Switch to Collapsable table"
msgstr ""

#: wppa-album-admin-autosave.php:1285
msgid "Switch to Flat table"
msgstr ""

#: wppa-album-admin-autosave.php:1297
msgid "Open all"
msgstr ""

#: wppa-album-admin-autosave.php:1310
msgid "Close all"
msgstr ""

#: wppa-album-admin-autosave.php:1452 wppa-album-admin-autosave.php:1590
#: wppa-album-admin-autosave.php:1824 wppa-album-admin-autosave.php:1920
#: wppa-photo-admin-autosave.php:2523 wppa-photo-admin-autosave.php:2719
msgid "ID"
msgstr ""

#: wppa-album-admin-autosave.php:1494 wppa-album-admin-autosave.php:1632
#: wppa-album-admin-autosave.php:1867 wppa-album-admin-autosave.php:1963
msgid "Order"
msgstr ""

#: wppa-album-admin-autosave.php:1504 wppa-album-admin-autosave.php:1642
#: wppa-album-admin-autosave.php:1877 wppa-album-admin-autosave.php:1973
msgid "Parent"
msgstr ""

#: wppa-album-admin-autosave.php:1512 wppa-album-admin-autosave.php:1650
#: wppa-album-admin-autosave.php:1885 wppa-album-admin-autosave.php:1981
msgid "Albums/Photos/Moderation required/Scheduled"
msgstr ""

#: wppa-album-admin-autosave.php:1513 wppa-album-admin-autosave.php:1651
#: wppa-album-admin-autosave.php:1886 wppa-album-admin-autosave.php:1982
msgid "A/P/PM/S"
msgstr ""

#: wppa-album-admin-autosave.php:1516 wppa-album-admin-autosave.php:1554
#: wppa-album-admin-autosave.php:1654 wppa-album-admin-autosave.php:1889
#: wppa-album-admin-autosave.php:1985 wppa-album-admin-autosave.php:2072
#: wppa-album-admin-autosave.php:2124 wppa-album-admin-autosave.php:2257
msgid "Quick"
msgstr ""

#: wppa-album-admin-autosave.php:1517 wppa-album-admin-autosave.php:1555
#: wppa-album-admin-autosave.php:1655 wppa-album-admin-autosave.php:1890
#: wppa-album-admin-autosave.php:1986 wppa-album-admin-autosave.php:2077
#: wppa-album-admin-autosave.php:2258
msgid "Bulk"
msgstr ""

#: wppa-album-admin-autosave.php:1518 wppa-album-admin-autosave.php:1556
#: wppa-album-admin-autosave.php:1656 wppa-album-admin-autosave.php:1891
#: wppa-album-admin-autosave.php:1987 wppa-album-admin-autosave.php:2259
msgid "Seq"
msgstr ""

#: wppa-album-admin-autosave.php:1520 wppa-album-admin-autosave.php:1564
#: wppa-album-admin-autosave.php:1567 wppa-album-admin-autosave.php:1658
#: wppa-album-admin-autosave.php:1893 wppa-album-admin-autosave.php:1989
#: wppa-album-admin-autosave.php:2267 wppa-album-admin-autosave.php:2270
msgid "Create"
msgstr ""

#: wppa-album-admin-autosave.php:1669 wppa-album-admin-autosave.php:2016
msgid "No albums yet."
msgstr ""

#: wppa-album-admin-autosave.php:1818 wppa-album-admin-autosave.php:2230
msgid "Collapse subalbums"
msgstr ""

#: wppa-album-admin-autosave.php:1819 wppa-album-admin-autosave.php:2231
msgid "Expand subalbums"
msgstr ""

#: wppa-album-admin-autosave.php:1901
msgid ""
"The following albums are ---separate--- and do not show up in the generic "
"album display"
msgstr ""

#: wppa-album-admin-autosave.php:2033
msgid "Search for photos to edit"
msgstr ""

#: wppa-album-admin-autosave.php:2036
msgid ""
"Enter search words seperated by commas. Photos will meet all search words by "
"their names, descriptions, translated keywords and/or tags."
msgstr ""

#: wppa-album-admin-autosave.php:2043 wppa-album-admin-autosave.php:2105
msgid "Any"
msgstr ""

#: wppa-album-admin-autosave.php:2047
msgid "Search for"
msgstr ""

#: wppa-album-admin-autosave.php:2112
#, php-format
msgid "There are %s trashed photos that can be rescued"
msgstr ""

#: wppa-album-admin-autosave.php:2304
#, php-format
msgid "Unable to move photos to album %s. Album not deleted."
msgstr ""

#: wppa-album-admin-autosave.php:2344
msgid "Album Deleted."
msgstr ""

#: wppa-album-admin-autosave.php:2366
#, php-format
msgid "auto select max %s random"
msgstr ""

#: wppa-album-admin-autosave.php:2368
#, php-format
msgid "auto select max %s featured"
msgstr ""

#: wppa-album-admin-autosave.php:2370
#, php-format
msgid "max %s most recent added"
msgstr ""

#: wppa-album-admin-autosave.php:2372
#, php-format
msgid "max %s from (grand)child albums"
msgstr ""

#: wppa-album-admin-autosave.php:2374
#, php-format
msgid "max %s most recent from (grand)child albums"
msgstr ""

#: wppa-album-admin-autosave.php:2378
msgid "--- random ---"
msgstr ""

#: wppa-album-admin-autosave.php:2380
msgid "--- random featured ---"
msgstr ""

#: wppa-album-admin-autosave.php:2382
msgid "--- most recent added ---"
msgstr ""

#: wppa-album-admin-autosave.php:2384
msgid "--- random from (grand)children ---"
msgstr ""

#: wppa-album-admin-autosave.php:2386
msgid "--- most recent from (grand)children ---"
msgstr ""

#: wppa-album-admin-autosave.php:2398
msgid "Nameless, filename = "
msgstr ""

#: wppa-album-admin-autosave.php:2436
msgid ""
"You can edit top-level album sequence order here when you set the album "
"order to \"Order #\" or \"Order # desc\" in Table IV-D1."
msgstr ""

#: wppa-album-admin-autosave.php:2439
msgid ""
"You can edit sub-album sequence order here when you set the album order to "
"\"Order #\" or \"Order # desc\" in the \"Sub album sort order:\" selection "
"box above."
msgstr ""

#: wppa-album-admin-autosave.php:2449
msgid "Manage album order"
msgstr ""

#: wppa-album-admin-autosave.php:2453
msgid "Change sequence order by drag and drop, or use the up/down arrows."
msgstr ""

#: wppa-album-admin-autosave.php:2456
msgid "Do not leave this page unless the bar is entirely green."
msgstr ""

#: wppa-album-admin-autosave.php:2465 wppa-settings-autosave.php:8436
#: wppa-settings-autosave.php:8447 wppa-stereo.php:31
msgid "Color"
msgstr ""

#: wppa-album-admin-autosave.php:2468
msgid "Meaning"
msgstr ""

#: wppa-album-admin-autosave.php:2478
msgid "Up to date"
msgstr ""

#: wppa-album-admin-autosave.php:2486
msgid "Updating"
msgstr ""

#: wppa-album-admin-autosave.php:2494
msgid "Needs update"
msgstr ""

#: wppa-album-admin-autosave.php:2502 wppa-maintenance.php:98
#: wppa-upload.php:636
msgid "Error"
msgstr ""

#: wppa-album-admin-autosave.php:2722
msgid "To top"
msgstr ""

#: wppa-album-admin-autosave.php:2733
msgid "One up"
msgstr ""

#: wppa-album-admin-autosave.php:2744
msgid "One down"
msgstr ""

#: wppa-album-admin-autosave.php:2755
msgid "To bottom"
msgstr ""

#: wppa-album-admin-autosave.php:2766
msgid "Id:"
msgstr ""

#: wppa-album-admin-autosave.php:2767
msgid "Ord:"
msgstr ""

#: wppa-album-covers.php:1035
msgid "Number of photo views in this album"
msgstr ""

#: wppa-album-covers.php:1039
msgid "Number of photo views in this album and its sub-albums"
msgstr ""

#: wppa-album-covers.php:1046
msgid "Views:"
msgstr "浏览次数:"

#: wppa-album-covers.php:1420 wppa-album-covers.php:1496
#: wppa-album-covers.php:1504 wppa-album-covers.php:1751
msgid "View the album"
msgstr "查看相册"

#: wppa-album-covers.php:1440
msgid "View the cover photo"
msgid_plural "View the cover photos"
msgstr[0] "查看这张封面照片"

#: wppa-album-covers.php:1517
msgid "View"
msgstr "查看"

#: wppa-album-covers.php:1521 wppa-album-covers.php:1529
#, php-format
msgid "%d album"
msgid_plural "%d albums"
msgstr[0] ""

#: wppa-album-covers.php:1524 wppa-album-covers.php:1538
#, php-format
msgid "%d photo"
msgid_plural "%d photos"
msgstr[0] ""

#: wppa-album-covers.php:1535 wppa-boxes-html.php:1317 wppa-boxes-html.php:1635
#: wppa-breadcrumb.php:175 wppa-breadcrumb.php:181 wppa-breadcrumb.php:188
#: wppa-breadcrumb.php:434 wppa-breadcrumb.php:436 wppa-breadcrumb.php:442
#: wppa-breadcrumb.php:444 wppa-breadcrumb.php:452 wppa-breadcrumb.php:468
#: wppa-breadcrumb.php:481 wppa-breadcrumb.php:487 wppa-utils.php:1575
#: wppa-utils.php:1579 wppa-utils.php:2282
msgid "and"
msgstr "和"

#: wppa-album-covers.php:1619
msgid "New!"
msgstr "新!"

#: wppa-album-covers.php:1622 wppa-thumbnails.php:2026 wppa-thumbnails.php:2027
msgid "New"
msgstr "新建"

#: wppa-album-covers.php:1860 wppa-boxes-html.php:879 wppa-non-admin.php:850
#: wppa-settings-autosave.php:2363 wppa-settings-autosave.php:5800
#: wppa-settings-autosave.php:8919
msgid "Slideshow"
msgstr "幻灯片"

#: wppa-album-covers.php:1861
msgid "Browse photos"
msgstr "浏览照片"

#: wppa-album-covers.php:1898 wppa-breadcrumb.php:436 wppa-breadcrumb.php:444
#: wppa-breadcrumb.php:481 wppa-breadcrumb.php:487
msgid "Category:"
msgid_plural "Categories:"
msgstr[0] ""

#: wppa-album-navigator-widget.php:13
msgid "Display hierarchical album navigator"
msgstr ""

#: wppa-album-navigator-widget.php:14
msgid "WPPA+ Album Navigator"
msgstr ""

#: wppa-album-navigator-widget.php:35 wppa-album-navigator-widget.php:89
msgid "Album Navigator"
msgstr ""

#: wppa-album-navigator-widget.php:101 wppa-album-widget.php:331
msgid "--- all albums ---"
msgstr ""

#: wppa-album-navigator-widget.php:102 wppa-album-widget.php:332
msgid "--- all generic albums ---"
msgstr ""

#: wppa-album-navigator-widget.php:103 wppa-album-widget.php:333
msgid "--- all separate albums ---"
msgstr ""

#: wppa-album-navigator-widget.php:104 wppa-common-functions.php:1758
#: wppa-items.php:492
msgid "--- owner/public ---"
msgstr ""

#: wppa-album-navigator-widget.php:123 wppa-album-widget.php:359
msgid "Album selection or Parent album"
msgstr ""

#: wppa-album-navigator-widget.php:127 wppa-album-widget.php:365
msgid "Skip \"empty\" albums"
msgstr ""

#: wppa-album-widget.php:15
msgid "Display thumbnail images that link to albums"
msgstr ""

#: wppa-album-widget.php:16
msgid "WPPA+ Photo Albums"
msgstr ""

#: wppa-album-widget.php:99
#, php-format
msgid "Upload at least %d photos to this album!"
msgstr ""

#: wppa-album-widget.php:285
msgid "There are no albums (yet)"
msgstr ""

#: wppa-album-widget.php:318
msgid "Thumbnail Albums"
msgstr ""

#: wppa-album-widget.php:334
msgid "--- most recently added albums ---"
msgstr ""

#: wppa-album-widget.php:362
msgid "Show album names"
msgstr ""

#: wppa-album-widget.php:368 wppa-comment-widget.php:126
#: wppa-featen-widget.php:209 wppa-lasten-widget.php:238
#: wppa-thumbnail-widget.php:232 wppa-topten-widget.php:388
msgid ""
"You can set the sizes in this widget in the <b>Photo Albums -> Settings</b> "
"admin page."
msgstr ""

#: wppa-album-widget.php:369
msgid "Table I-F9 and 10"
msgstr ""

#: wppa-audio.php:183
msgid ""
"There is no filetype available for your browser, or your browser does not "
"support html5 audio"
msgstr "有您的浏览器没有可用的文件类型，或者你的浏览器不支持HTML5音频"

#: wppa-bestof-widget.php:16
msgid "Display thumbnails or owners of top rated photos"
msgstr ""

#: wppa-bestof-widget.php:17
msgid "WPPA+ Best Of Photos"
msgstr ""

#: wppa-bestof-widget.php:38 wppa-bestof-widget.php:50
#: wppa-bestof-widget.php:111
msgid "Best Of Photos"
msgstr ""

#: wppa-bestof-widget.php:132
msgid "Max number of thumbnails"
msgstr ""

#: wppa-bestof-widget.php:135 wppa-boxes-html.php:424 wppa-breadcrumb.php:178
msgid "Photos"
msgstr "照片"

#: wppa-bestof-widget.php:136
msgid "Owners"
msgstr ""

#: wppa-bestof-widget.php:143
msgid "Select photos or owners"
msgstr ""

#: wppa-bestof-widget.php:146
msgid "Last week"
msgstr ""

#: wppa-bestof-widget.php:147
msgid "This week"
msgstr ""

#: wppa-bestof-widget.php:148
msgid "Last month"
msgstr ""

#: wppa-bestof-widget.php:149
msgid "This month"
msgstr ""

#: wppa-bestof-widget.php:150
msgid "Last year"
msgstr ""

#: wppa-bestof-widget.php:151
msgid "This year"
msgstr ""

#: wppa-bestof-widget.php:161
msgid "Limit to ratings given during"
msgstr ""

#: wppa-bestof-widget.php:164
msgid "Number of max ratings"
msgstr ""

#: wppa-bestof-widget.php:165 wppa-topten-widget.php:344
msgid "Mean value"
msgstr ""

#: wppa-bestof-widget.php:173 wppa-thumbnail-widget.php:210
#: wppa-topten-widget.php:353 wppa-upldr-widget.php:210
msgid "Sort by"
msgstr ""

#: wppa-bestof-widget.php:176
msgid "Show number of max ratings"
msgstr ""

#: wppa-bestof-widget.php:179
msgid "Show mean rating"
msgstr ""

#: wppa-bestof-widget.php:182
msgid "Show number of ratings"
msgstr ""

#: wppa-bestof-widget.php:185 wppa-common-functions.php:1740
#: wppa-import.php:1660 wppa-items.php:465 wppa-potd-admin.php:100
#: wppa-potd-admin.php:136 wppa-settings-autosave.php:1436
#: wppa-settings-autosave.php:1812 wppa-settings-autosave.php:2017
#: wppa-settings-autosave.php:3871 wppa-settings-autosave.php:3885
#: wppa-settings-autosave.php:4026 wppa-settings-autosave.php:4079
#: wppa-settings-autosave.php:4191 wppa-settings-autosave.php:4208
#: wppa-settings-autosave.php:4321 wppa-settings-autosave.php:4658
#: wppa-settings-autosave.php:5874 wppa-settings-autosave.php:6547
#: wppa-settings-autosave.php:6707 wppa-settings-autosave.php:8320
#: wppa-settings-autosave.php:8539 wppa-settings-autosave.php:8615
#: wppa-settings-autosave.php:9591 wppa-settings-autosave.php:9748
#: wppa-settings-autosave.php:9887 wppa-thumbnail-widget.php:193
#: wppa-tinymce-shortcodes.php:627 wppa-watermark.php:590
msgid "--- none ---"
msgstr "---无---"

#: wppa-bestof-widget.php:186
msgid "The authors album(s)"
msgstr ""

#: wppa-bestof-widget.php:187
msgid "The photos in the authors album(s), thumbnails"
msgstr ""

#: wppa-bestof-widget.php:188
msgid "The photos in the authors album(s), slideshow"
msgstr ""

#: wppa-bestof-widget.php:189
msgid "All the authors photos, thumbnails"
msgstr ""

#: wppa-bestof-widget.php:190
msgid "All the authors photos, slideshow"
msgstr ""

#: wppa-boxes-html.php:139 wppa-boxes-html.php:286
msgid "Photo search results"
msgstr "照片搜索结果"

#: wppa-boxes-html.php:169 wppa-boxes-html.php:443
msgid "Category"
msgstr "分类"

#: wppa-boxes-html.php:186 wppa-common-functions.php:1746 wppa-items.php:483
#: wppa-multitag-widget.php:84 wppa-multitag-widget.php:93
#: wppa-slideshow-widget.php:193 wppa-tagcloud-widget.php:77
#: wppa-tagcloud-widget.php:90
msgid "--- all ---"
msgstr "---所有---"

#: wppa-boxes-html.php:202
msgctxt "wp-photo-album-plus"
msgid "Search photos &hellip;"
msgstr ""

#: wppa-boxes-html.php:418 wppa-breadcrumb.php:163 wppa-lasten-widget.php:214
#: wppa-settings-autosave.php:371
msgid "Albums"
msgstr "专辑"

#: wppa-boxes-html.php:456 wppa-boxes-html.php:589 wppa-gp-widget.php:31
#: wppa-gp-widget.php:81 wppa-settings-autosave.php:1705
#: wppa-settings-autosave.php:8435 wppa-settings-autosave.php:8446
msgid "Text"
msgstr "文本"

#: wppa-boxes-html.php:472 wppa-boxes-html.php:526 wppa-boxes-html.php:672
#: wppa-boxes-html.php:702
msgid "CTRL+Click to add/remove option."
msgstr "CTRL  单击添加/删除选项。"

#: wppa-boxes-html.php:473 wppa-boxes-html.php:527 wppa-boxes-html.php:673
#: wppa-boxes-html.php:703
msgid "Items must meet all selected options."
msgstr "项目必须满足所有选择的选项。"

#: wppa-boxes-html.php:581 wppa-settings-autosave.php:9933
#: wppa-settings-autosave.php:9973 wppa-settings-autosave.php:9995
#: wppa-settings-autosave.php:10041
msgid "Tag"
msgstr "标签"

#: wppa-boxes-html.php:597
msgid "Iptc"
msgstr "IPTC"

#: wppa-boxes-html.php:606
msgid "Exif"
msgstr "EXIF"

#: wppa-boxes-html.php:804 wppa-boxes-html.php:883
msgid "Submit"
msgstr "提交"

#: wppa-boxes-html.php:846
msgid "Super View Photos"
msgstr "超级查看照片"

#: wppa-boxes-html.php:871 wppa-settings-autosave.php:388
#: wppa-settings-autosave.php:5034
msgid "Thumbnails"
msgstr "缩略图"

#: wppa-boxes-html.php:981
msgid "No zipfiles available"
msgstr ""

#: wppa-boxes-html.php:1013
msgid "Tagged photos"
msgstr "标记的照片"

#: wppa-boxes-html.php:1027
msgid "Please select a tagcloud landing page in Table VI-C3b"
msgstr "请选择表VI-C3b的一个标签云登陆页面"

#: wppa-boxes-html.php:1087
msgid "Multi Tagged photos"
msgstr "多标记照片"

#: wppa-boxes-html.php:1102
msgid "Please select a multitag landing page in Table VI-C4b"
msgstr "请选择表VI-C4b的一个多标签登陆页面"

#: wppa-boxes-html.php:1147
msgid "Please check the tag(s) that the photos must have"
msgstr "请检查照片必须有标签"

#: wppa-boxes-html.php:1178
msgid "And"
msgstr "还有"

#: wppa-boxes-html.php:1189
msgid "Or"
msgstr "或"

#: wppa-boxes-html.php:1204
msgid "Inverse selection"
msgstr "逆选择"

#: wppa-boxes-html.php:1257
msgid "Find!"
msgstr "寻找"

#: wppa-boxes-html.php:1288
msgid "Social media landing page"
msgstr "社会化媒体的目标网页"

#: wppa-boxes-html.php:1318 wppa-utils.php:1579
#, php-format
msgid "See this image on %s"
msgstr "查看 %s 的这一形象"

#: wppa-boxes-html.php:1347 wppa-boxes-html.php:1655 wppa-qr-widget.php:47
msgid "QR code"
msgstr "QR码"

#: wppa-boxes-html.php:1392 wppa-boxes-html.php:1687
#, php-format
msgid "Tweet %s on Twitter"
msgstr "鸣叫 %s 上微博"

#: wppa-boxes-html.php:1399 wppa-boxes-html.php:1694
msgid "Share on Twitter"
msgstr "在Twitter上分享"

#: wppa-boxes-html.php:1412 wppa-boxes-html.php:1707
#, php-format
msgid "Share %s on Google+"
msgstr "在Google 分享 %s"

#: wppa-boxes-html.php:1419 wppa-boxes-html.php:1715
msgid "Share on Google+"
msgstr "在Google+上分享"

#: wppa-boxes-html.php:1434
#, php-format
msgid "Share %s on Pinterest"
msgstr "在Pinterest的份额 %s"

#: wppa-boxes-html.php:1444
msgid "Share on Pinterest"
msgstr "分享到 Pinterest"

#: wppa-boxes-html.php:1476
#, php-format
msgid "Share %s on LinkedIn"
msgstr ""

#: wppa-boxes-html.php:1592 wppa-boxes-html.php:1791
msgid "Comment on Facebook:"
msgstr "评论在Facebook上："

#: wppa-boxes-html.php:1636
#, php-format
msgid "See this article on %s"
msgstr ""

#: wppa-boxes-html.php:1894 wppa-import.php:1347
msgid "Working..."
msgstr "处理中..."

#: wppa-boxes-html.php:1991
msgid "Create Sub Album"
msgstr ""

#: wppa-boxes-html.php:1991
msgid "Create Album"
msgstr "创建专辑"

#: wppa-boxes-html.php:2039
msgid "Enter album name."
msgstr "输入相册名称。"

#: wppa-boxes-html.php:2041 wppa-boxes-html.php:3053
msgid "Don't leave this blank!"
msgstr "不要留空！"

#: wppa-boxes-html.php:2062
msgid "Enter album description"
msgstr "进入专辑介绍"

#: wppa-boxes-html.php:2090
msgid "Create album"
msgstr "创建专辑"

#: wppa-boxes-html.php:2250 wppa-boxes-html.php:2264 wppa-functions.php:4507
msgid "Max uploads reached"
msgstr "最大上传达到"

#: wppa-boxes-html.php:2329 wppa-upload.php:210
msgid "Upload Photo"
msgstr "上傳照片"

#: wppa-boxes-html.php:2404
msgid "Enter album id"
msgstr ""

#: wppa-boxes-html.php:2433
msgid "Select Photo / Video / Camera"
msgstr ""

#: wppa-boxes-html.php:2436
msgid "Select Photo / Camera"
msgstr ""

#: wppa-boxes-html.php:2441
msgid "Select Photo / Video"
msgstr ""

#: wppa-boxes-html.php:2444
msgid "Select Photo"
msgstr ""

#: wppa-boxes-html.php:2451
msgid "Select Photos / Video / Camera"
msgstr ""

#: wppa-boxes-html.php:2454
msgid "Select Photos / Camera"
msgstr ""

#: wppa-boxes-html.php:2459
msgid "Select Photos / Video"
msgstr ""

#: wppa-boxes-html.php:2462
msgid "Select Photos"
msgstr ""

#: wppa-boxes-html.php:2512
#, php-format
msgid "You may upload %d photo"
msgid_plural ""
"You may upload up to %d photos at once if your browser supports HTML-5 "
"multiple file upload"
msgstr[0] ""

#: wppa-boxes-html.php:2521
#, php-format
msgid "Min photo size: %d pixels"
msgstr ""

#: wppa-boxes-html.php:2528
#, php-format
msgid "Max photo size: %d pixels"
msgstr ""

#: wppa-boxes-html.php:2536
#, php-format
msgid "Max photo size: %d x %d (%2.1f MegaPixel)"
msgstr "最大照片尺寸：%d x %d（%2.1f 万像素）"

#: wppa-boxes-html.php:2566 wppa-import.php:578 wppa-upload.php:196
#: wppa-upload.php:340 wppa-upload.php:417
msgid "Apply watermark file:"
msgstr "应用水印的文件："

#: wppa-boxes-html.php:2588 wppa-import.php:582 wppa-upload.php:200
#: wppa-upload.php:344 wppa-upload.php:421
msgid "Position:"
msgstr "职位："

#: wppa-boxes-html.php:2616
msgid ""
"If you leave this blank, iptc tag 005 (Graphic name) will be used as "
"photoname if available, else the original filename will be used as photo "
"name."
msgstr ""
"如果你离开这个空白，IPTC标签005（图形名）将用作photoname如果可用，否则原来的"
"文件名会被作为照片的名称。"

#: wppa-boxes-html.php:2621
msgid ""
"If you leave this blank, iptc tag 120 (Caption) will be used as photoname if "
"available, else the original filename will be used as photo name."
msgstr ""
"如果你离开这个空白，IPTC标签120（标题）将被用作photoname如果可用，否则原来的"
"文件名会被作为照片的名称。"

#: wppa-boxes-html.php:2626
msgid "If you leave this blank, \"Photo photoid\" will be used as photo name."
msgstr ""

#: wppa-boxes-html.php:2632
msgid ""
"If you leave this blank, the original filename will be used as photo name."
msgstr "如果你离开这个空白，原来的文件名会被作为照片的名称。"

#: wppa-boxes-html.php:2637 wppa-settings-autosave.php:1751
msgid "Photo name"
msgstr ""

#: wppa-boxes-html.php:2655
msgid "Photo description"
msgstr ""

#: wppa-boxes-html.php:2683
msgid "hidden"
msgstr "隐藏"

#: wppa-boxes-html.php:2754
msgid "Preview tags:"
msgstr "预览标签："

#: wppa-boxes-html.php:2775
msgid "Blog it?"
msgstr ""

#: wppa-boxes-html.php:2785 wppa-boxes-html.php:2860
msgid "Upload and blog"
msgstr ""

#: wppa-boxes-html.php:2789 wppa-boxes-html.php:2860
msgid "Upload photo"
msgstr "上傳照片"

#: wppa-boxes-html.php:2813
msgid "Post title:"
msgstr ""

#: wppa-boxes-html.php:2823
msgid "Text BEFORE the image:"
msgstr ""

#: wppa-boxes-html.php:2833
msgid "Text AFTER the image:"
msgstr ""

#: wppa-boxes-html.php:2853
msgid "Please select an album and try again"
msgstr "请选择一个专辑，然后再试一次"

#: wppa-boxes-html.php:2925
msgid "Processing..."
msgstr ""

#: wppa-boxes-html.php:2930 wppa-import.php:153
msgid "Done!"
msgstr ""

#: wppa-boxes-html.php:2938
msgid "ERROR: unable to upload files."
msgstr "错误：无法上传文件。"

#: wppa-boxes-html.php:2992
msgid "Edit Album Info"
msgstr ""

#: wppa-boxes-html.php:3051
msgid "Enter album name"
msgstr ""

#: wppa-boxes-html.php:3073
msgid "Album description:"
msgstr "专辑介绍："

#: wppa-boxes-html.php:3127
msgid "Update album"
msgstr "最新专辑"

#: wppa-boxes-html.php:3196
msgid "wrote:"
msgstr "寫作"

#: wppa-boxes-html.php:3258
msgid "Avatar"
msgstr "头像"

#: wppa-boxes-html.php:3310 wppa-links.php:860
msgid "Awaiting moderation"
msgstr "等待审核"

#: wppa-boxes-html.php:3313
msgid "Marked as spam"
msgstr "标记为垃圾邮件"

#: wppa-boxes-html.php:3337
msgid "Edit!"
msgstr "退出!"

#: wppa-boxes-html.php:3341
msgid "Send!"
msgstr "发送！"

#: wppa-boxes-html.php:3402
msgid "Your name:"
msgstr "你的名字："

#: wppa-boxes-html.php:3417
msgid "Your email:"
msgstr "您的电子邮件："

#: wppa-boxes-html.php:3433
msgid "Your comment:"
msgstr "您的评论："

#: wppa-boxes-html.php:3477
#, php-format
msgid "You must <a href=\"%s\">login</a> to enter a comment"
msgstr "你必须<a href=\"%s\">登录</a>进入评论"

#: wppa-boxes-html.php:3480
msgid "You must login to enter a comment"
msgstr "你必须登录进入评论"

#: wppa-boxes-html.php:3492 wppa-functions.php:2673 wppa-thumbnails.php:735
#, php-format
msgid "%d comment"
msgid_plural "%d comments"
msgstr[0] ""

#: wppa-boxes-html.php:3496
msgid "Leave a comment"
msgstr "发表评论"

#: wppa-boxes-html.php:3593
msgid "Show IPTC data"
msgstr "显示IPTC数据"

#: wppa-boxes-html.php:3604
msgid "Hide IPTC data"
msgstr "隐藏IPTC数据"

#: wppa-boxes-html.php:3652
msgid "No IPTC data"
msgstr "无IPTC数据"

#: wppa-boxes-html.php:3704
msgid "Show EXIF data"
msgstr "显示EXIF数据"

#: wppa-boxes-html.php:3715
msgid "Hide EXIF data"
msgstr "隐藏EXIF数据"

#: wppa-boxes-html.php:3767
msgid "No EXIF data"
msgstr "无EXIF数据"

#: wppa-boxes-html.php:3881 wppa-boxes-html.php:3886
msgid "< Previous"
msgstr "< 上一張"

#: wppa-boxes-html.php:3892 wppa-boxes-html.php:3897
msgid "Next >"
msgstr "下一个 >"

#: wppa-boxes-html.php:3999 wppa-boxes-html.php:4078
msgid "See the authors albums"
msgstr "见笔者专辑"

#: wppa-boxes-html.php:4003 wppa-boxes-html.php:4011 wppa-boxes-html.php:4082
msgid "See the authors photos"
msgstr "看到照片的作者"

#: wppa-boxes-html.php:4007 wppa-boxes-html.php:4015 wppa-boxes-html.php:4086
msgid "See all the authors photos"
msgstr "查看所有作家的照片"

#: wppa-boxes-html.php:4041
#, php-format
msgid "Photo by: %s"
msgstr "攝影：%s"

#: wppa-boxes-html.php:4044 wppa-boxes-html.php:4107
#, php-format
msgid "%d max rating"
msgid_plural "%d max ratings"
msgstr[0] ""

#: wppa-boxes-html.php:4048 wppa-boxes-html.php:4111 wppa-non-admin.php:1014
#: wppa-topten-widget.php:209 wppa-topten-widget.php:226
#: wppa-topten-widget.php:262
#, php-format
msgid "%d vote"
msgid_plural "%d votes"
msgstr[0] ""

#: wppa-boxes-html.php:4052
#, php-format
msgid "Rating: %4.2f."
msgstr ""

#: wppa-boxes-html.php:4060
#, php-format
msgid "Photo %s not found."
msgstr "照片%s未找到。"

#: wppa-boxes-html.php:4115
#, php-format
msgid "Mean value: %4.2f."
msgstr "平均值：%4.2f。"

#: wppa-boxes-html.php:4469
msgid "Refresh"
msgstr "刷新"

#: wppa-breadcrumb.php:125
msgid "Post:"
msgstr "文章分类："

#: wppa-breadcrumb.php:125 wppa-breadcrumb.php:723
msgid "Page:"
msgstr "页面："

#: wppa-breadcrumb.php:166
msgid "with category:"
msgstr "与类别："

#: wppa-breadcrumb.php:169 wppa-breadcrumb.php:184
msgid "with name:"
msgstr "用名字："

#: wppa-breadcrumb.php:172 wppa-breadcrumb.php:188
msgid "with words:"
msgstr "文字："

#: wppa-breadcrumb.php:181
msgid "with tag:"
msgstr "与标签："

#: wppa-breadcrumb.php:192
msgid "of owner:"
msgstr "：主人"

#: wppa-breadcrumb.php:197
msgid "with iptc tag:"
msgstr "与 iptc 标记:"

#: wppa-breadcrumb.php:197 wppa-breadcrumb.php:202
msgid "with content:"
msgstr "内容:"

#: wppa-breadcrumb.php:202
msgid "with exif tag:"
msgstr "与 exif 标记:"

#: wppa-breadcrumb.php:211 wppa-breadcrumb.php:259 wppa-breadcrumb.php:317
#: wppa-breadcrumb.php:341 wppa-breadcrumb.php:366 wppa-breadcrumb.php:391
#: wppa-breadcrumb.php:411 wppa-breadcrumb.php:424 wppa-breadcrumb.php:439
#: wppa-breadcrumb.php:465 wppa-breadcrumb.php:484 wppa-breadcrumb.php:505
msgid "View the thumbnails"
msgstr "查看缩略图"

#: wppa-breadcrumb.php:229
#, php-format
msgid "Searchresults from album %s and its subalbums"
msgstr "從相冊 %s 和其子相冊的SearchResult"

#: wppa-breadcrumb.php:234
msgid "Searchstring:"
msgstr "搜寻字串："

#: wppa-breadcrumb.php:248
msgid "in category:"
msgstr ""

#: wppa-breadcrumb.php:270 wppa-breadcrumb.php:288
msgid "Photos by EXIF date"
msgstr "照片的 EXIF 日期"

#: wppa-breadcrumb.php:274 wppa-breadcrumb.php:292
msgid "Photos by date of upload"
msgstr "按日期上传照片"

#: wppa-breadcrumb.php:278 wppa-breadcrumb.php:296
msgid "Photos by date last modified"
msgstr "照片由上次修改日期"

#: wppa-breadcrumb.php:308 wppa-breadcrumb.php:320
#, php-format
msgid "Photos by %s"
msgstr "攝影 %s"

#: wppa-breadcrumb.php:327 wppa-breadcrumb.php:351 wppa-breadcrumb.php:381
#: wppa-breadcrumb.php:401 wppa-breadcrumb.php:520
msgid "Various albums"
msgstr "各种专辑"

#: wppa-breadcrumb.php:330 wppa-breadcrumb.php:354 wppa-breadcrumb.php:384
#: wppa-breadcrumb.php:404 wppa-breadcrumb.php:525
msgid "Albums:"
msgstr "相册"

#: wppa-breadcrumb.php:334 wppa-breadcrumb.php:344
msgid "Top rated photos"
msgstr "评分最高的照片"

#: wppa-breadcrumb.php:359 wppa-breadcrumb.php:370
msgid "Recently modified photos"
msgstr "最近修改过的照片"

#: wppa-breadcrumb.php:362 wppa-breadcrumb.php:373
msgid "Recently uploaded photos"
msgstr "最近上传的照片"

#: wppa-breadcrumb.php:388 wppa-breadcrumb.php:394 wppa-comment-widget.php:40
msgid "Recently commented photos"
msgstr "最近评论照片"

#: wppa-breadcrumb.php:408 wppa-breadcrumb.php:414 wppa-featen-widget.php:36
#: wppa-featen-widget.php:40
msgid "Featured photos"
msgstr "所有图片"

#: wppa-breadcrumb.php:421 wppa-breadcrumb.php:427
msgid "Related photos"
msgstr "相关照片"

#: wppa-breadcrumb.php:434 wppa-breadcrumb.php:442 wppa-breadcrumb.php:452
#: wppa-breadcrumb.php:468
msgid "Tagged photos:"
msgstr "标记的照片："

#: wppa-breadcrumb.php:434 wppa-breadcrumb.php:436 wppa-breadcrumb.php:442
#: wppa-breadcrumb.php:444 wppa-breadcrumb.php:452 wppa-breadcrumb.php:468
#: wppa-breadcrumb.php:481 wppa-breadcrumb.php:487
msgid "or"
msgstr "或"

#: wppa-breadcrumb.php:435 wppa-breadcrumb.php:443
msgid "From albums with"
msgstr ""

#: wppa-breadcrumb.php:454 wppa-breadcrumb.php:470
msgid "out of various albums"
msgstr ""

#: wppa-breadcrumb.php:457 wppa-breadcrumb.php:473
msgid "Inverted"
msgstr "颠倒"

#: wppa-breadcrumb.php:502 wppa-breadcrumb.php:508
msgid "Recently updated albums"
msgstr "最近更新的相册"

#: wppa-breadcrumb.php:518
#, php-format
msgid "Various albums by %s"
msgstr "不同的專輯將由 %s"

#: wppa-breadcrumb.php:549 wppa-breadcrumb.php:564
msgid "Thumbnail view"
msgstr "缩略图视图"

#: wppa-breadcrumb.php:721
msgid "Unpublished"
msgstr "未发布"

#: wppa-breadcrumb.php:751
msgid "Found photos will meet the search criteria as follows:"
msgstr "发现的照片将满足搜索条件，如下所示:"

#: wppa-breadcrumb.php:754
msgid "AND"
msgstr "和"

#: wppa-breadcrumb.php:758
msgid "OR"
msgstr "或者"

#: wppa-cart.php:30
msgid "Buy now"
msgstr ""

#: wppa-cart.php:94
#, php-format
msgid "Unit Price: %s each"
msgstr ""

#: wppa-cart.php:98
msgid "Qty:"
msgstr ""

#: wppa-comment-admin.php:41 wppa-settings-autosave.php:6550
msgid "all"
msgstr ""

#: wppa-comment-admin.php:42
msgid "pending"
msgstr ""

#: wppa-comment-admin.php:43
msgid "approved"
msgstr ""

#: wppa-comment-admin.php:44
msgid "spam"
msgstr ""

#: wppa-comment-admin.php:51
msgid "Filter"
msgstr ""

#: wppa-comment-admin.php:92 wppa-comment-admin.php:182 wppa-non-admin.php:855
msgid "Photo"
msgstr "照片"

#: wppa-comment-admin.php:139 wppa-comment-admin.php:204 wppa-links.php:814
#: wppa-links.php:831
msgid "Approve"
msgstr ""

#: wppa-comment-admin.php:140 wppa-comment-admin.php:147
#: wppa-photo-admin-autosave.php:853 wppa-photo-admin-autosave.php:886
#: wppa-photo-admin-autosave.php:2064 wppa-photo-admin-autosave.php:2073
#: wppa-photo-admin-autosave.php:2449 wppa-photo-admin-autosave.php:2683
#: wppa-photo-admin-autosave.php:2695
msgid "Pending"
msgstr ""

#: wppa-comment-admin.php:141 wppa-comment-admin.php:157
#: wppa-photo-admin-autosave.php:2066 wppa-photo-admin-autosave.php:2075
msgid "Spam"
msgstr ""

#: wppa-comment-admin.php:152 wppa-photo-admin-autosave.php:2065
#: wppa-photo-admin-autosave.php:2074
msgid "Approved"
msgstr ""

#: wppa-comment-admin.php:183
msgid "User"
msgstr ""

#: wppa-comment-admin.php:184
msgid "User email"
msgstr ""

#: wppa-comment-admin.php:187
msgid "Comment"
msgstr ""

#: wppa-comment-admin.php:383
msgid "Photo Albums -> Moderate Comment"
msgstr ""

#: wppa-comment-admin.php:384
msgid "Photo Albums -> Comment admin"
msgstr ""

#: wppa-comment-admin.php:393 wppa-comment-admin.php:394
#: wppa-common-functions.php:622 wppa-functions.php:4948
#: wppa-settings-autosave.php:6840 wppa-settings-autosave.php:6841
#, php-format
msgid "%d minute"
msgid_plural "%d minutes"
msgstr[0] ""

#: wppa-comment-admin.php:395 wppa-common-functions.php:626
#: wppa-functions.php:4944 wppa-settings-autosave.php:6842
#: wppa-settings-autosave.php:8330
#, php-format
msgid "%d hour"
msgid_plural "%d hours"
msgstr[0] ""

#: wppa-comment-admin.php:396 wppa-common-functions.php:630
#: wppa-functions.php:4940 wppa-settings-autosave.php:6843
#: wppa-settings-autosave.php:8331 wppa-settings-autosave.php:8332
#: wppa-settings-autosave.php:8333 wppa-settings-autosave.php:8334
#: wppa-settings-autosave.php:8335 wppa-settings-autosave.php:8336
#: wppa-settings-autosave.php:8338 wppa-settings-autosave.php:8339
#: wppa-settings-autosave.php:8340 wppa-settings-autosave.php:9651
#, php-format
msgid "%d day"
msgid_plural "%d days"
msgstr[0] ""

#: wppa-comment-admin.php:397 wppa-common-functions.php:634
#: wppa-functions.php:4936 wppa-settings-autosave.php:6844
#: wppa-settings-autosave.php:8337 wppa-settings-autosave.php:8341
#: wppa-settings-autosave.php:8342 wppa-settings-autosave.php:8343
#: wppa-settings-autosave.php:9652
#, php-format
msgid "%d week"
msgid_plural "%d weeks"
msgstr[0] ""

#: wppa-comment-admin.php:405
msgid "Total:"
msgstr ""

#: wppa-comment-admin.php:410
msgid "Approved:"
msgstr ""

#: wppa-comment-admin.php:415
msgid "Pending:"
msgstr ""

#: wppa-comment-admin.php:420
msgid "Spam:"
msgstr ""

#: wppa-comment-admin.php:427
msgid "Auto deleted spam:"
msgstr ""

#: wppa-comment-admin.php:429
#, php-format
msgid ""
"Comments marked as spam will be deleted when they are entered longer than %s "
"ago."
msgstr ""

#: wppa-comment-widget.php:15
msgid "Display comments on Photos"
msgstr ""

#: wppa-comment-widget.php:16
msgid "WPPA+ Comments on Photos"
msgstr ""

#: wppa-comment-widget.php:39
msgid "Comments on photos"
msgstr ""

#: wppa-comment-widget.php:73 wppa-non-admin.php:984 wppa-thumbnails.php:504
msgid "wrote"
msgstr "寫作"

#: wppa-comment-widget.php:87 wppa-featen-widget.php:150
#: wppa-lasten-widget.php:149 wppa-non-admin.php:985 wppa-non-admin.php:990
#: wppa-non-admin.php:995 wppa-non-admin.php:999 wppa-non-admin.php:1006
#: wppa-non-admin.php:1016 wppa-potd-widget.php:163
#: wppa-thumbnail-widget.php:115 wppa-topten-widget.php:270
msgid "Photo not found"
msgstr ""

#: wppa-comment-widget.php:93 wppa-non-admin.php:986
msgid "There are no commented photos (yet)"
msgstr ""

#: wppa-comment-widget.php:118
msgid "Comments on Photos"
msgstr ""

#: wppa-comment-widget.php:127
msgid "Table I-F3 and 4"
msgstr ""

#: wppa-common-functions.php:618 wppa-functions.php:4952
#, php-format
msgid "%d second"
msgid_plural "%d seconds"
msgstr[0] ""

#: wppa-common-functions.php:638 wppa-settings-autosave.php:8344
#: wppa-settings-autosave.php:9653 wppa-settings-autosave.php:9654
#: wppa-settings-autosave.php:9655 wppa-settings-autosave.php:9656
#: wppa-settings-autosave.php:9657 wppa-settings-autosave.php:9659
#, php-format
msgid "%d month"
msgid_plural "%d months"
msgstr[0] ""

#: wppa-common-functions.php:641 wppa-settings-autosave.php:9658
#: wppa-settings-autosave.php:9660
#, php-format
msgid "%d year"
msgid_plural "%d years"
msgstr[0] ""

#: wppa-common-functions.php:1409
#, php-format
msgid ""
"Based on your server memory limit you should not upload images larger then "
"<strong>%d x %d (%2.1f MP)</strong>"
msgstr ""
"基于服务器的内存限制你应该不要上传照片然后大<strong>%d x %d (%2.1f MP)</"
"strong>"

#: wppa-common-functions.php:1734
msgid "- select an album -"
msgstr "- 选择专辑 -"

#: wppa-common-functions.php:1752
msgid "--- generic ---"
msgstr "---通用---"

#: wppa-common-functions.php:1769
msgid "--- multiple see below ---"
msgstr "---多见下文---"

#: wppa-common-functions.php:1775
msgid "--- a selection box ---"
msgstr "---选择框---"

#: wppa-common-functions.php:1826 wppa-import.php:1661 wppa-items.php:474
#: wppa-settings-autosave.php:8539 wppa-settings-autosave.php:8615
msgid "--- separate ---"
msgstr "---独立---"

#: wppa-common-functions.php:1944
msgid "Photo id ="
msgstr "照片证件"

#: wppa-common-functions.php:1944
msgid "Value ="
msgstr "值:"

#: wppa-date-time.php:85 wppa-date-time.php:225
msgid "Jan"
msgstr ""

#: wppa-date-time.php:85 wppa-date-time.php:225
msgid "Feb"
msgstr ""

#: wppa-date-time.php:85 wppa-date-time.php:225
msgid "Mar"
msgstr ""

#: wppa-date-time.php:85 wppa-date-time.php:225
msgid "Apr"
msgstr ""

#: wppa-date-time.php:85 wppa-date-time.php:225
msgid "May"
msgstr ""

#: wppa-date-time.php:85 wppa-date-time.php:225
msgid "Jun"
msgstr ""

#: wppa-date-time.php:85 wppa-date-time.php:225
msgid "Jul"
msgstr ""

#: wppa-date-time.php:85 wppa-date-time.php:225
msgid "Aug"
msgstr ""

#: wppa-date-time.php:85 wppa-date-time.php:225
msgid "Sep"
msgstr ""

#: wppa-date-time.php:85 wppa-date-time.php:225
msgid "Oct"
msgstr ""

#: wppa-date-time.php:85 wppa-date-time.php:225
msgid "Nov"
msgstr ""

#: wppa-date-time.php:85 wppa-date-time.php:225
msgid "Dec"
msgstr ""

#: wppa-encrypt.php:139
msgid "Invalid photo identifier:"
msgstr ""

#: wppa-encrypt.php:197
msgid "Invalid album identifier:"
msgstr ""

#: wppa-exif-iptc-common.php:82 wppa-exif-iptc-common.php:161
msgid "n.a."
msgstr "呐"

#: wppa-exif-iptc-common.php:239 wppa-utils.php:2563
msgid "Not Defined"
msgstr "未定義"

#: wppa-exif-iptc-common.php:240 wppa-utils.php:2564
msgid "Manual"
msgstr "管理器"

#: wppa-exif-iptc-common.php:241 wppa-utils.php:2565
msgid "Program AE"
msgstr "程序自动曝光"

#: wppa-exif-iptc-common.php:242 wppa-utils.php:2566
msgid "Aperture-priority AE"
msgstr "光圈优先自动曝光"

#: wppa-exif-iptc-common.php:243 wppa-utils.php:2567
msgid "Shutter speed priority AE"
msgstr "快门速度优先AE"

#: wppa-exif-iptc-common.php:244 wppa-utils.php:2568
msgid "Creative (Slow speed)"
msgstr "创意（慢速）"

#: wppa-exif-iptc-common.php:245 wppa-utils.php:2569
msgid "Action (High speed)"
msgstr "动作（高速）"

#: wppa-exif-iptc-common.php:246 wppa-utils.php:2570
msgid "Portrait"
msgstr "纵向"

#: wppa-exif-iptc-common.php:247 wppa-utils.php:2571
msgid "Landscape"
msgstr "横向"

#: wppa-exif-iptc-common.php:248 wppa-utils.php:2572
msgid "Bulb"
msgstr "燈泡"

#: wppa-exif-iptc-common.php:270 wppa-utils.php:2573
msgid "Average"
msgstr "一般"

#: wppa-exif-iptc-common.php:271 wppa-utils.php:2574
msgid "Center-weighted average"
msgstr "中央重点平均测光"

#: wppa-exif-iptc-common.php:272 wppa-utils.php:2575
msgid "Spot"
msgstr "现场"

#: wppa-exif-iptc-common.php:273 wppa-utils.php:2576
msgid "Multi-spot"
msgstr "多点"

#: wppa-exif-iptc-common.php:274 wppa-utils.php:2577
msgid "Multi-segment"
msgstr "多段"

#: wppa-exif-iptc-common.php:275 wppa-utils.php:2578
msgid "Partial"
msgstr "部分"

#: wppa-exif-iptc-common.php:276 wppa-settings-autosave.php:5051
#: wppa-utils.php:2579
msgid "Other"
msgstr "其他"

#: wppa-exif-iptc-common.php:313 wppa-utils.php:2580
msgid "No Flash"
msgstr "閃光燈:"

#: wppa-exif-iptc-common.php:315 wppa-utils.php:2581
msgid "Fired"
msgstr "已解除"

#: wppa-exif-iptc-common.php:317 wppa-utils.php:2582
msgid "Fired, Return not detected"
msgstr "开除，回到未检出"

#: wppa-exif-iptc-common.php:319 wppa-utils.php:2583
msgid "Fired, Return detected"
msgstr "解雇，返回检测"

#: wppa-exif-iptc-common.php:321 wppa-utils.php:2584
msgid "On, Did not fire"
msgstr "对，没有火"

#: wppa-exif-iptc-common.php:323 wppa-utils.php:2585
msgid "On, Fired"
msgstr "上，被解雇"

#: wppa-exif-iptc-common.php:325 wppa-utils.php:2586
msgid "On, Return not detected"
msgstr "时，返回未检出"

#: wppa-exif-iptc-common.php:327 wppa-utils.php:2587
msgid "On, Return detected"
msgstr "上，检测到返回"

#: wppa-exif-iptc-common.php:329 wppa-utils.php:2588
msgid "Off, Did not fire"
msgstr "关，没有火"

#: wppa-exif-iptc-common.php:331 wppa-utils.php:2589
msgid "Off, Did not fire, Return not detected"
msgstr "关，没有火，返回未检出"

#: wppa-exif-iptc-common.php:333 wppa-utils.php:2590
msgid "Auto, Did not fire"
msgstr "自动，没有火"

#: wppa-exif-iptc-common.php:335 wppa-utils.php:2591
msgid "Auto, Fired"
msgstr "汽车，燃"

#: wppa-exif-iptc-common.php:337 wppa-utils.php:2592
msgid "Auto, Fired, Return not detected"
msgstr "汽车，燃煤，返回未检出"

#: wppa-exif-iptc-common.php:339 wppa-utils.php:2593
msgid "Auto, Fired, Return detected"
msgstr "汽车，燃煤，检测到返回"

#: wppa-exif-iptc-common.php:341 wppa-utils.php:2594
msgid "No flash function"
msgstr "无闪光灯功能"

#: wppa-exif-iptc-common.php:343 wppa-utils.php:2595
msgid "Off, No flash function"
msgstr "关，无闪光灯功能"

#: wppa-exif-iptc-common.php:345 wppa-utils.php:2596
msgid "Fired, Red-eye reduction"
msgstr "解雇，减轻红眼"

#: wppa-exif-iptc-common.php:347 wppa-utils.php:2597
msgid "Fired, Red-eye reduction, Return not detected"
msgstr "解雇，减轻红眼，返回未检出"

#: wppa-exif-iptc-common.php:349 wppa-utils.php:2598
msgid "Fired, Red-eye reduction, Return detected"
msgstr "解雇，减轻红眼，检测到返回"

#: wppa-exif-iptc-common.php:351 wppa-utils.php:2599
msgid "On, Red-eye reduction"
msgstr "开，防红眼"

#: wppa-exif-iptc-common.php:353 wppa-utils.php:2600
msgid "Red-eye reduction, Return not detected"
msgstr "减轻红眼，返回未检出"

#: wppa-exif-iptc-common.php:355 wppa-utils.php:2601
msgid "On, Red-eye reduction, Return detected"
msgstr "开，防红眼，返回检测"

#: wppa-exif-iptc-common.php:357 wppa-utils.php:2602
msgid "Off, Red-eye reduction"
msgstr "关闭，防红眼"

#: wppa-exif-iptc-common.php:359 wppa-utils.php:2603
msgid "Auto, Did not fire, Red-eye reduction"
msgstr "自动，没有火，防红眼"

#: wppa-exif-iptc-common.php:361 wppa-utils.php:2604
msgid "Auto, Fired, Red-eye reduction"
msgstr "汽车，燃烧，减轻红眼"

#: wppa-exif-iptc-common.php:363 wppa-utils.php:2605
msgid "Auto, Fired, Red-eye reduction, Return not detected"
msgstr "汽车，燃烧，减轻红眼，返回未检出"

#: wppa-exif-iptc-common.php:365 wppa-utils.php:2606
msgid "Auto, Fired, Red-eye reduction, Return detected"
msgstr "汽车，燃烧，减轻红眼，检测到返回"

#: wppa-export.php:36
#, php-format
msgid "Photos will be exported to: <b>%s</b>."
msgstr ""

#: wppa-export.php:37
msgid ""
"Export photos from album <span style=\"font-size:12px;\">(Including Album "
"information)</span>:"
msgstr ""

#: wppa-export.php:66 wppa-settings-autosave.php:6268
msgid "Export"
msgstr ""

#: wppa-export.php:81
msgid "Exporting...<br/>"
msgstr ""

#: wppa-export.php:90
msgid "ok, <br/>Filling"
msgstr ""

#: wppa-export.php:92
msgid "failed<br/>"
msgstr ""

#: wppa-export.php:98
msgid ""
"Can export albums and photos, but cannot make a zipfile. Your php version is "
"< 5.2.7."
msgstr ""

#: wppa-export.php:99
msgid ""
"Can export albums and photos, but cannot make a zipfile. Your php version "
"does not support ZipArchive."
msgstr ""

#: wppa-export.php:109
msgid "<br/>Processing album"
msgstr ""

#: wppa-export.php:130
msgid "done."
msgstr ""

#: wppa-export.php:130
msgid "photos processed."
msgstr ""

#: wppa-export.php:134
msgid "<br/>Done export albums."
msgstr ""

#: wppa-export.php:137
msgid "Nothing to export"
msgstr ""

#: wppa-export.php:141
msgid "<br/>Closing zip."
msgstr ""

#: wppa-export.php:142
msgid "<br/>Deleting temp files."
msgstr ""

#: wppa-export.php:151
msgid "<br/>Done!"
msgstr ""

#: wppa-export.php:197 wppa-export.php:266
#, php-format
msgid "Cannot write to file %s."
msgstr ""

#: wppa-export.php:211
msgid "Could not open album output file."
msgstr ""

#: wppa-export.php:216
msgid "Could not read album data."
msgstr ""

#: wppa-export.php:280
msgid "Could not open photo output file."
msgstr ""

#: wppa-export.php:285
msgid "Could not read photo data."
msgstr ""

#: wppa-featen-widget.php:15
msgid "Display thumbnails of featured photos"
msgstr ""

#: wppa-featen-widget.php:16
msgid "WPPA+ Featured Photos"
msgstr ""

#: wppa-featen-widget.php:120 wppa-non-admin.php:989
msgid "View the featured photos"
msgstr ""

#: wppa-featen-widget.php:162 wppa-non-admin.php:991
msgid "There are no featured photos (yet)"
msgstr ""

#: wppa-featen-widget.php:190
msgid "Featured Photos"
msgstr ""

#: wppa-featen-widget.php:210
msgid "Table I-F11 and 12"
msgstr ""

#: wppa-filter.php:515
#, php-format
msgid "Photo %d does not exist"
msgstr ""

#: wppa-filter.php:519
msgid "Missing photo id"
msgstr ""

#: wppa-functions.php:690
msgid "No related photos found."
msgstr "没有相关的照片找到。"

#: wppa-functions.php:1070
#, php-format
msgid ""
"There are %s albums found. Only the first %s will be shown. Please refine "
"your search criteria."
msgstr "找到 %s 的相冊。只有第一個 %s 將會顯示。請改進搜索標準。"

#: wppa-functions.php:2160 wppa-links.php:809 wppa-links.php:826
#: wppa-thumbnails.php:671
msgid "Are you sure you want to remove this photo?"
msgstr "你确定要移除这个项目？"

#: wppa-functions.php:2187 wppa-thumbnails.php:652
msgid "Are you sure you want to add this photo to your zip?"
msgstr ""

#: wppa-functions.php:2190 wppa-thumbnails.php:655
msgid "MyChoice"
msgstr ""

#: wppa-functions.php:2278
#, php-format
msgid "%d dislike"
msgid_plural "%d dislikes"
msgstr[0] ""

#: wppa-functions.php:2280
msgid "including mine"
msgstr "包括我"

#: wppa-functions.php:2403
msgid ""
"Sorry, you gave a wrong answer.\\n\\nPlease try again to solve the "
"computation."
msgstr ""
"对不起，你给了一个错误的答案。\n"
"\n"
" 请再次尝试解决计算。"

#: wppa-functions.php:2409
msgid "Sorry, your comment is not accepted."
msgstr ""

#: wppa-functions.php:2417
msgid "Comment edited"
msgstr "编辑评论"

#: wppa-functions.php:2428
msgid "Photo comment"
msgstr "照片评论"

#: wppa-functions.php:2445
msgid "Comment on photo:"
msgstr "评论照片："

#: wppa-functions.php:2460
msgid "wrote on photo"
msgstr "写在照片上"

#: wppa-functions.php:2462
msgid "Reply"
msgstr "回复"

#: wppa-functions.php:2464
msgid "Moderate comment admin"
msgstr "中度评论管理"

#: wppa-functions.php:2467 wppa-functions.php:4748 wppa-import.php:1580
#: wppa-upload.php:611
msgid "Moderate manage photo"
msgstr "适度管理照片"

#: wppa-functions.php:2481
msgid "You receive this email as you are assigned to moderate"
msgstr "你收到这封电子邮件，你被分配到适度"

#: wppa-functions.php:2493
msgid "You receive this email as administrator of the site"
msgstr "你收到这封电子邮件作为网站管理员"

#: wppa-functions.php:2510
msgid "You receive this email as uploader of the photo"
msgstr "您收到此邮件的照片上传"

#: wppa-functions.php:2527
msgid "You receive this email as owner of the album"
msgstr "你收到这封电子邮件作为专辑的主人"

#: wppa-functions.php:2544
msgid "You receive this email because you commented this photo earlier."
msgstr "你收到这封电子邮件，因为你这张照片早些时候评论。"

#: wppa-functions.php:2570
msgid "Comment added"
msgstr "添加评论"

#: wppa-functions.php:2582
msgid "Could not process comment.\\nProbably timed out."
msgstr ""
"无法处理意见。\n"
" Probably超时。"

#: wppa-functions.php:2709 wppa-links.php:1682
msgid "A video can not be printed or downloaded"
msgstr "视频不能打印或下载"

#: wppa-functions.php:3247
msgid "ERROR: Illegal attempt to enter a rating."
msgstr "错误: 非法试图进入评级。"

#: wppa-functions.php:3260
msgid "ERROR: Illegal attempt to enter a comment."
msgstr "错误: 非法尝试输入注释。"

#: wppa-functions.php:4236
msgid "ERROR: Illegal attempt to create an album."
msgstr "错误: 非法尝试创建相册。"

#: wppa-functions.php:4244
msgid "Wrong captcha, please try again"
msgstr "验证码错误，请重试"

#: wppa-functions.php:4260
#, php-format
msgid "Album #%s created"
msgstr "專輯＃%s 創建"

#: wppa-functions.php:4271
#, php-format
msgid "User %s created album #%s with name %s."
msgstr ""

#: wppa-functions.php:4282
#, php-format
msgid "The new album is a subalbum of album %s"
msgstr ""

#: wppa-functions.php:4284
msgid ""
"You are receiving this email because you are assigned to monitor new album "
"creations."
msgstr ""

#: wppa-functions.php:4285
msgid "New useralbum created"
msgstr ""

#: wppa-functions.php:4291
msgid "Could not create album"
msgstr "无法创建相册"

#: wppa-functions.php:4303
msgid "ERROR: Illegal attempt to upload a file."
msgstr "错误: 非法尝试上载的文件。"

#: wppa-functions.php:4387
#, php-format
msgid "%d photo successfully uploaded"
msgid_plural "%d photos successfully uploaded"
msgstr[0] ""

#: wppa-functions.php:4392
msgid "Photo upload"
msgstr "上传照片"

#: wppa-functions.php:4396
#, php-format
msgid "%s points added"
msgstr ""

#: wppa-functions.php:4402
msgid "Your post is awaiting moderation."
msgstr ""

#: wppa-functions.php:4405
msgid "Your post is published."
msgstr ""

#: wppa-functions.php:4413
msgid "Upload failed"
msgstr "上传失败"

#: wppa-functions.php:4416
#, php-format
msgid "%d upload failed"
msgid_plural "%d uploads failed"
msgstr[0] ""

#: wppa-functions.php:4513
msgid "Error during upload"
msgstr "上传时出错"

#: wppa-functions.php:4569
msgid "Could not insert media into db."
msgstr ""

#: wppa-functions.php:4613
msgid "Uploaded file is not an image"
msgstr "上传的文件不是图片"

#: wppa-functions.php:4619
#, php-format
msgid "Only gif, jpg and png image files are supported. Returned info = %s."
msgstr ""

#: wppa-functions.php:4627
#, php-format
msgid "Uploaded file is smaller than the allowed minimum of %d pixels."
msgstr ""

#: wppa-functions.php:4636
#, php-format
msgid "Uploaded file is larger than the allowed maximum of %d pixels."
msgstr ""

#: wppa-functions.php:4644
#, php-format
msgid "Uploaded file %s already exists in this album."
msgstr "上傳的文件 %s 已經存在在這張專輯中。"

#: wppa-functions.php:4654
#, php-format
msgid "The image is too big. Max photo size: %d x %d (%2.1f MegaPixel)"
msgstr "该图片太大。最大照片尺寸：%d x %d（%2.1f 万像素）"

#: wppa-functions.php:4697
msgid "Could not insert photo into db."
msgstr "无法将照片变成分贝。"

#: wppa-functions.php:4744 wppa-import.php:1576 wppa-upload.php:607
#, php-format
msgid "New photo uploaded: %s"
msgstr "新的照片上传：%s 的"

#: wppa-functions.php:4745 wppa-import.php:1577 wppa-upload.php:608
#, php-format
msgid "User %1$s uploaded photo %2$s into album %3$s"
msgstr "用户 %1$s 上载照片 %2$s 到专辑 %3$s"

#: wppa-functions.php:4747 wppa-import.php:1579 wppa-upload.php:610
msgid "This upload requires moderation"
msgstr "这上传需要节制"

#: wppa-functions.php:4751 wppa-import.php:1583 wppa-upload.php:614
msgid "Details:"
msgstr "详细信息 ："

#: wppa-functions.php:4752 wppa-import.php:1584 wppa-upload.php:615
#: wppa-utils.php:840 wppa-utils.php:852
msgid "Manage photo"
msgstr "管理照片"

#: wppa-functions.php:4933
msgid "You can upload after"
msgstr "您可以上传后，"

#: wppa-functions.php:4977 wppa-functions.php:4981 wppa-functions.php:4988
#: wppa-functions.php:4992 wppa-links.php:1267 wppa-non-admin.php:910
#: wppa-settings-autosave.php:10080 wppa-settings-autosave.php:10092
#: wppa-settings-autosave.php:10104 wppa-settings-autosave.php:10116
#: wppa-settings-autosave.php:10128 wppa-settings-autosave.php:10140
#: wppa-settings-autosave.php:10152 wppa-settings-autosave.php:10164
msgid "Download"
msgstr "下载"

#: wppa-functions.php:5042
msgid "Zoom in"
msgstr "放大"

#: wppa-functions.php:5073
#, php-format
msgid "You can vote again after %s days, %s hours, %s minutes and %s seconds"
msgstr ""

#: wppa-functions.php:5076
#, php-format
msgid "You can vote again after %s hours, %s minutes and %s seconds"
msgstr ""

#: wppa-functions.php:5079
#, php-format
msgid "You can vote again after %s minutes and %s seconds"
msgstr ""

#: wppa-gp-widget.php:13
msgid "General purpose widget that may contain [wppa][/wppa] shortcodes"
msgstr ""

#: wppa-gp-widget.php:14
msgid "WPPA+ Text"
msgstr ""

#: wppa-gp-widget.php:88
msgid ""
"Enter the content just like a normal text widget. This widget will interpret "
"[wppa] shortcodes"
msgstr ""

#: wppa-gp-widget.php:91
msgid "Automatically add paragraphs"
msgstr ""

#: wppa-gp-widget.php:94
msgid "Show to logged in users only"
msgstr ""

#: wppa-help.php:16
#, php-format
msgid "You will find all information and examples on the new %s%s%s site"
msgstr ""

#: wppa-help.php:16
msgid "Docs & Demos"
msgstr ""

#: wppa-help.php:18
msgid "About and credits"
msgstr ""

#: wppa-help.php:20
msgid ""
"WP Photo Album Plus is extended with many new features and is maintained by "
"J.N. Breetvelt, a.k.a. OpaJaap"
msgstr ""

#: wppa-help.php:21
msgid "Thanx to R.J. Kaplan for WP Photo Album 1.5.1."
msgstr ""

#: wppa-help.php:22
msgid "Thanx to E.S. Rosenberg for programming tips on security issues."
msgstr ""

#: wppa-help.php:23
msgid "Thanx to Pavel &#352;orejs for the Numbar code."
msgstr ""

#: wppa-help.php:24
msgid ""
"Thanx to the users who reported bugs and asked for enhancements. Without "
"them WPPA should not have been what it is now!"
msgstr ""

#: wppa-help.php:27
msgid "Licence"
msgstr ""

#: wppa-help.php:29
msgid "WP Photo Album is released under the"
msgstr ""

#: wppa-help.php:29
msgid "licence."
msgstr ""

#: wppa-import.php:159
msgid "Failed!"
msgstr ""

#: wppa-import.php:251
msgid "Select Local or Remote"
msgstr ""

#: wppa-import.php:254
msgid "Local"
msgstr ""

#: wppa-import.php:255
msgid "Remote"
msgstr ""

#: wppa-import.php:263
msgid "Set Local/Remote"
msgstr ""

#: wppa-import.php:268
msgid ""
"The server does not allow you to import from remote locations. ( The php "
"directive allow_url_fopen is not set to 1 )"
msgstr ""

#: wppa-import.php:271
msgid ""
"The server does not allow you to import from remote locations. ( The curl "
"functions are not set up )"
msgstr ""

#: wppa-import.php:281
msgid "Import photos from:"
msgstr ""

#: wppa-import.php:294
msgid "Set source directory"
msgstr ""

#: wppa-import.php:321
msgid "Max:"
msgstr ""

#: wppa-import.php:333
msgid "Find remote photos"
msgstr ""

#: wppa-import.php:336
msgid "Working, please wait..."
msgstr ""

#: wppa-import.php:339
msgid ""
"You can enter either a web page address like <i>http://mysite.com/mypage/</"
"i> or a full url to an image file like <i>http://mysite.com/wp-content/"
"uploads/wppa/4711.jpg</i>"
msgstr ""

#: wppa-import.php:352 wppa-upload.php:154
msgid "No albums exist. You must"
msgstr ""

#: wppa-import.php:354 wppa-upload.php:156
msgid "create one"
msgstr ""

#: wppa-import.php:356
msgid "before you can import your photos."
msgstr ""

#: wppa-import.php:377
#, php-format
msgid "There is %d zipfile in the depot"
msgid_plural "There are %d zipfiles in the depot"
msgstr[0] ""

#: wppa-import.php:390 wppa-import.php:467 wppa-import.php:620
#: wppa-import.php:878 wppa-import.php:986 wppa-import.php:1078
#: wppa-import.php:1159
msgid "Check/uncheck all"
msgstr ""

#: wppa-import.php:403
msgid "Delete after successful extraction."
msgstr ""

#: wppa-import.php:454
#, php-format
msgid "There is %d albumdefinition in the depot"
msgid_plural "There are %d albumdefinitions in the depot"
msgstr[0] ""

#: wppa-import.php:480
msgid ""
"Remove from depot after successful import, or if the album already exists."
msgstr ""

#: wppa-import.php:537
#, php-format
msgid "There is %d photo in the ngg gallery"
msgid_plural "There are %d photos in the ngg gallery"
msgstr[0] ""

#: wppa-import.php:540
#, php-format
msgid "There is %d photo in the depot"
msgid_plural "There are %d photos in the depot"
msgstr[0] ""

#: wppa-import.php:546
#, php-format
msgid "There is %d possible photo found remote"
msgid_plural "There are %d possible photos found remote"
msgstr[0] ""

#: wppa-import.php:551
msgid "Photos will be downsized during import."
msgstr ""

#: wppa-import.php:560
msgid "Default album for import:"
msgstr ""

#: wppa-import.php:570
msgid ""
"Photos that have (<em>name</em>)[<em>album</em>] will be imported by that "
"<em>name</em> in that <em>album</em>."
msgstr ""

#: wppa-import.php:591
msgid "Delay"
msgstr ""

#: wppa-import.php:636 wppa-import.php:892 wppa-import.php:1002
#: wppa-import.php:1172
msgid "Remove from depot after successful import."
msgstr ""

#: wppa-import.php:647
msgid "Remove from depot after failed import."
msgstr ""

#: wppa-import.php:665
msgid "Import into album"
msgstr ""

#: wppa-import.php:668
msgid "The album will be created if it does not exist"
msgstr ""

#: wppa-import.php:680
msgid "Use backup if available"
msgstr ""

#: wppa-import.php:696
msgid "Update existing photos"
msgstr ""

#: wppa-import.php:721
msgid "Do not create duplicates"
msgstr ""

#: wppa-import.php:737
msgid "Zoom previews"
msgstr ""

#: wppa-import.php:846
#, php-format
msgid "There is %d video in the depot"
msgid_plural "There are %d videos in the depot"
msgstr[0] ""

#: wppa-import.php:852 wppa-import.php:960
msgid "Album to import to:"
msgstr ""

#: wppa-import.php:894
msgid "Files larger than 64MB will always be removed after successful import."
msgstr ""

#: wppa-import.php:954
#, php-format
msgid "There is %d audio in the depot"
msgid_plural "There are %d audios in the depot"
msgstr[0] ""

#: wppa-import.php:1061
#, php-format
msgid "There is %d albumdirectory in the depot"
msgid_plural "There are %d albumdirectories in the depot"
msgstr[0] ""

#: wppa-import.php:1119
#, php-format
msgid "Contains %d file"
msgid_plural "Contains %d files"
msgstr[0] ""

#: wppa-import.php:1122
#, php-format
msgid "and %d subdirectory"
msgid_plural "and %d subdirectories"
msgstr[0] ""

#: wppa-import.php:1142
#, php-format
msgid "There is %d .csv file in the depot"
msgid_plural "There are %d .csv files in the depot"
msgstr[0] ""

#: wppa-import.php:1362
msgid "Start Ajax Import"
msgstr ""

#: wppa-import.php:1363
msgid "Stop Ajax Import"
msgstr ""

#: wppa-import.php:1371
msgid "There are no importable files found in directory:"
msgstr ""

#: wppa-import.php:1374
msgid "There are no photos found or left to process at url:"
msgstr ""

#: wppa-import.php:1378
msgid "You can import the following file types:"
msgstr ""

#: wppa-import.php:1382
msgid "Compressed file types: .zip"
msgstr ""

#: wppa-import.php:1386
msgid "Photo file types:"
msgstr ""

#: wppa-import.php:1393
msgid "Video file types:"
msgstr ""

#: wppa-import.php:1400
msgid "Audio file types:"
msgstr ""

#: wppa-import.php:1406
msgid "WPPA+ file types: .amf .pmf"
msgstr ""

#: wppa-import.php:1408
msgid "Directories with optional subdirs containig photos"
msgstr ""

#: wppa-import.php:1410
msgid "Custom data files of type .csv"
msgstr ""

#: wppa-import.php:1412
msgid "Your depot directory is:"
msgstr ""

#: wppa-import.php:1416
msgid "Trying to continue..."
msgstr ""

#: wppa-import.php:1669
msgid "Unknown parent album:"
msgstr ""

#: wppa-import.php:1669
msgid "--- none --- used."
msgstr ""

#: wppa-import.php:1729
#, php-format
msgid "This album has been converted from ngg gallery %s"
msgstr ""

#: wppa-import.php:1747
msgid "Processing files, please wait..."
msgstr ""

#: wppa-import.php:1747
msgid ""
"If the line of dots stops growing or your browser reports Ready, your server "
"has given up. In that case: try again"
msgstr ""

#: wppa-import.php:1747
msgid "here."
msgstr ""

#: wppa-import.php:1874
#, php-format
msgid "Photo %s already exists in album %s. (1)"
msgstr ""

#: wppa-import.php:1875
msgid "Duplicate"
msgstr ""

#: wppa-import.php:1903 wppa-upload.php:551 wppa-upload.php:587
msgid "Error inserting photo"
msgstr ""

#: wppa-import.php:1911
#, php-format
msgid "Error inserting photo %s, unknown or non existent album."
msgstr ""

#: wppa-import.php:1919
#, php-format
msgid "Time out. %s photos imported. Please restart this operation."
msgstr ""

#: wppa-import.php:1953 wppa-import.php:2025
msgid "Unknown album"
msgstr ""

#: wppa-import.php:2015
#, php-format
msgid "Error inserting video %s, unknown or non existent album."
msgstr ""

#: wppa-import.php:2076
#, php-format
msgid "Error inserting audio %s, unknown or non existent album."
msgstr ""

#: wppa-import.php:2092
msgid "Custom datafields enabled"
msgstr ""

#: wppa-import.php:2162
msgid "Only administrators are allowed to import db table data."
msgstr ""

#: wppa-import.php:2169 wppa-import.php:2170
msgid "Processing db table"
msgstr ""

#: wppa-import.php:2173 wppa-import.php:2174
msgid "Processing"
msgstr ""

#: wppa-import.php:2184
msgid "Can not open file. Can not continue. (1)"
msgstr ""

#: wppa-import.php:2189
msgid "Can not open file. Can not continue. (2)"
msgstr ""

#: wppa-import.php:2196
msgid "Can not read header. Can not continue."
msgstr ""

#: wppa-import.php:2201
msgid "Read header:"
msgstr ""

#: wppa-import.php:2229 wppa-import.php:2321
msgid "Invalid header. Can not continue."
msgstr ""

#: wppa-import.php:2330
msgid "Invalid header. First item must be 'name', 'photoname' or 'filename'"
msgstr ""

#: wppa-import.php:2337
msgid ""
"Invalid header. First item must be 'filename' when importing system data "
"fields"
msgstr ""

#: wppa-import.php:2344
msgid "All available custom data fields are in use. There is no space for"
msgstr ""

#: wppa-import.php:2355
#, php-format
msgid "New caption %s added."
msgstr ""

#: wppa-import.php:2385
msgid "Read data:"
msgstr ""

#: wppa-import.php:2433
msgid "Album does not exist"
msgstr ""

#: wppa-import.php:2476
msgid "Timestamp out of range"
msgstr ""

#: wppa-import.php:2572
msgid "Done processing files."
msgstr ""

#: wppa-import.php:2575
msgid "No files to import."
msgstr ""

#: wppa-import.php:2579
msgid "Zipfiles extracted."
msgstr ""

#: wppa-import.php:2580
msgid "Albums created."
msgstr ""

#: wppa-import.php:2581
msgid "Directory to album imports."
msgstr ""

#: wppa-import.php:2582
#, php-format
msgid "With total %s photos."
msgstr ""

#: wppa-import.php:2585
msgid "Photos updated"
msgstr ""

#: wppa-import.php:2587
#, php-format
msgid "to %s locations"
msgstr ""

#: wppa-import.php:2591
msgid "single photos imported."
msgstr ""

#: wppa-import.php:2594
msgid "Videos imported."
msgstr ""

#: wppa-import.php:2597
msgid "Audios imported."
msgstr ""

#: wppa-import.php:2600
msgid "CSVs imported,"
msgstr ""

#: wppa-import.php:2601
msgid "items processed."
msgstr ""

#: wppa-import.php:2602
msgid "items skipped."
msgstr ""

#: wppa-import.php:2610
#, php-format
msgid "Value %s is not valid for %s."
msgstr ""

#: wppa-import.php:2614
msgid "This value is ignored."
msgstr ""

#: wppa-import.php:2741
msgid "Error: unexpected fgets() fail in wppa_get_meta_data()."
msgstr ""

#: wppa-import.php:2762
msgid "Class ZipArchive does not exist! Check your php configuration"
msgstr ""

#: wppa-import.php:2795
#, php-format
msgid ""
"File %s is of an unsupported filetype and has been ignored during extraction."
msgstr ""

#: wppa-import.php:2801
#, php-format
msgid "Zipfile %s processed. %s files extracted, %s files skipped."
msgstr ""

#: wppa-import.php:2804
msgid "Failed to extract"
msgstr ""

#: wppa-import.php:2870
#, php-format
msgid "Page <a href=\"%s\" target=\"_blank\" >%s</a> created."
msgstr ""

#: wppa-import.php:2874
msgid "Could not create page."
msgstr ""

#: wppa-import.php:2893
#, php-format
msgid ".csv file %s has been moved to your depot."
msgstr ""

#: wppa-import.php:2997
msgid "--- My depot --- "
msgstr ""

#: wppa-import.php:3006
msgid "--- Ngg Galleries --- "
msgstr ""

#: wppa-init.php:197
msgid ""
"</strong><h3>WP Photo ALbum Plus Error message</h3>This is a multi site "
"installation. One of the following 3 lines must be entered in wp-config.php:"
msgstr ""

#: wppa-init.php:198
msgid ""
"<br /><br /><b>define( 'WPPA_MULTISITE_INDIVIDUAL', true );</b> <small>// "
"Multisite WP 3.5 or later with every site its own albums and photos</small>"
msgstr ""

#: wppa-init.php:199
msgid ""
"<br /><b>define( 'WPPA_MULTISITE_BLOGSDIR', true );</b> <small>// Multisite "
"prior to WP 3.5 with every site its own albums and photos</small>"
msgstr ""

#: wppa-init.php:200
msgid ""
"<br /><b>define( 'WPPA_MULTISITE_GLOBAL', true );</b> <small>// Multisite "
"with one common set of albums and photos</small>"
msgstr ""

#: wppa-init.php:201
msgid ""
"<br /><br />For more information see: <a href=\"https://wordpress.org/"
"plugins/wp-photo-album-plus/faq/\">the faq</a>"
msgstr ""

#: wppa-init.php:202
msgid ""
"<br /><br /><em>If you upload photos, they will be placed in the wrong "
"location and will not be visible for visitors!</em><strong>"
msgstr ""

#: wppa-init.php:236
msgid ""
"The avarage ratings need to be recalculated. Please run <i>Photo Albums -> "
"Settings</i> admin page <i>Table VIII-A5</i>"
msgstr ""

#: wppa-init.php:242
msgid ""
"Please do not forget to re-enable cron jobs for wppa when you are ready "
"doing your bulk actions. See <i>Table VIII-A0.2</i>"
msgstr ""

#: wppa-init.php:247
msgid "Please finish setting up WP Photo Album Plus on"
msgstr ""

#: wppa-init.php:247
msgid "this page"
msgstr ""

#: wppa-init.php:270
msgid ""
"</strong>The tags system needs to be converted. Please run <b>Photo Albums -"
"> Settings</b> admin page <b>Table VIII-B16</b><strong>"
msgstr ""

#: wppa-init.php:292
msgid ""
"</strong>The cats system needs to be converted. Please run <b>Photo Albums -"
"> Settings</b> admin page <b>Table VIII-B17</b><strong>"
msgstr ""

#: wppa-init.php:318
#, php-format
msgid ""
"WPPA scripts will no longer be supported in version 6.6. Please convert the "
"%%wppa%% scripts to [wppa][/wppa] shortcodes before upgrading to version 6.6."
msgstr ""

#: wppa-init.php:320
msgid "WPPA scripts found in the following Pages / Posts"
msgstr ""

#: wppa-init.php:339
#, php-format
msgid "For more information see the %s documentation page"
msgstr ""

#: wppa-items.php:281 wppa-thumbnails.php:1968
msgid "Gold medal"
msgstr "金牌"

#: wppa-items.php:281 wppa-photo-admin-autosave.php:862
#: wppa-photo-admin-autosave.php:889 wppa-photo-admin-autosave.php:2453
#: wppa-photo-admin-autosave.php:2687 wppa-photo-admin-autosave.php:2698
#: wppa-potd-admin.php:228
msgid "Gold"
msgstr ""

#: wppa-items.php:282 wppa-thumbnails.php:1969
msgid "Silver medal"
msgstr "银牌"

#: wppa-items.php:282 wppa-photo-admin-autosave.php:865
#: wppa-photo-admin-autosave.php:890 wppa-photo-admin-autosave.php:2454
#: wppa-photo-admin-autosave.php:2688 wppa-photo-admin-autosave.php:2699
#: wppa-potd-admin.php:229
msgid "Silver"
msgstr ""

#: wppa-items.php:283 wppa-thumbnails.php:1970
msgid "Bronze medal"
msgstr "铜牌"

#: wppa-items.php:283 wppa-photo-admin-autosave.php:868
#: wppa-photo-admin-autosave.php:891 wppa-photo-admin-autosave.php:2455
#: wppa-photo-admin-autosave.php:2689 wppa-photo-admin-autosave.php:2700
#: wppa-potd-admin.php:230
msgid "Bronze"
msgstr ""

#: wppa-items.php:368 wppa-items.php:669 wppa-settings-autosave.php:2396
#: wppa-settings-autosave.php:2425 wppa-settings-autosave.php:2436
#: wppa-settings-autosave.php:3074 wppa-settings-autosave.php:3683
msgid "none"
msgstr ""

#: wppa-items.php:384
msgid "Nomen Nescio"
msgstr ""

#: wppa-items.php:387
msgid "Anonymus"
msgstr ""

#: wppa-items.php:413 wppa-items.php:688 wppa-upload.php:132
#: wppa-upload.php:144
msgid "unknown"
msgstr ""

#: wppa-items.php:501 wppa-items.php:544
msgid "--- deleted ---"
msgstr ""

#: wppa-items.php:516 wppa-tinymce-shortcodes.php:321 wppa-upldr-widget.php:240
#: wppa-utils.php:496
msgid "All albums"
msgstr ""

#: wppa-items.php:525
msgid "My and public albums"
msgstr ""

#: wppa-lasten-widget.php:13
msgid "Display most recently uploaded photos"
msgstr ""

#: wppa-lasten-widget.php:14
msgid "WPPA+ Last Ten Photos"
msgstr ""

#: wppa-lasten-widget.php:36 wppa-lasten-widget.php:46
#: wppa-lasten-widget.php:187
msgid "Last Ten Photos"
msgstr ""

#: wppa-lasten-widget.php:132 wppa-non-admin.php:994
msgid "View the most recent uploaded photos"
msgstr ""

#: wppa-lasten-widget.php:156 wppa-non-admin.php:996
msgid "There are no uploaded photos (yet)"
msgstr ""

#: wppa-lasten-widget.php:215
msgid ""
"Select --- multiple see below --- in the Album selection box. Then enter "
"album numbers seperated by commas"
msgstr ""

#: wppa-lasten-widget.php:220
msgid "Include subalbums"
msgstr ""

#: wppa-lasten-widget.php:223 wppa-thumbnail-widget.php:217
#: wppa-topten-widget.php:334
msgid "thumbnail images"
msgstr ""

#: wppa-lasten-widget.php:224 wppa-thumbnail-widget.php:218
#: wppa-topten-widget.php:335
msgid "photo names"
msgstr ""

#: wppa-lasten-widget.php:230 wppa-settings-autosave.php:2097
msgid "Display type"
msgstr ""

#: wppa-lasten-widget.php:234
msgid "Show time since"
msgstr ""

#: wppa-lasten-widget.php:239
msgid "Table I-F7 and 8"
msgstr ""

#: wppa-links.php:797
msgid "App"
msgstr ""

#: wppa-links.php:798
msgid "Mod"
msgstr ""

#: wppa-links.php:799
msgid "Del"
msgstr ""

#: wppa-links.php:803 wppa-links.php:820
msgid "Are you sure you want to publish this photo?"
msgstr ""

#: wppa-links.php:815 wppa-settings-autosave.php:6267
msgid "Moderate"
msgstr ""

#: wppa-links.php:832
msgid "PhotoAdmin"
msgstr ""

#: wppa-links.php:833
msgid "CommentAdmin"
msgstr ""

#: wppa-links.php:837
msgid "Are you sure you want to publish this comment?"
msgstr ""

#: wppa-links.php:849
msgid "Are you sure you want to remove this comment?"
msgstr ""

#: wppa-links.php:863
#, php-format
msgid "Scheduled for %s"
msgstr ""

#: wppa-links.php:1202 wppa-links.php:1205
msgid "Previous"
msgstr "上一个"

#: wppa-links.php:1212 wppa-links.php:1215 wppa-non-admin.php:860
msgid "Next"
msgstr "下一页"

#: wppa-links.php:1269
msgid "Download Album"
msgstr ""

#: wppa-links.php:1420
msgid "View thumbnails"
msgstr ""

#: wppa-links.php:1433
msgid "View fullsize slideshow"
msgstr ""

#: wppa-maintenance.php:98
msgid "You can run only one maintenance procedure at a time"
msgstr ""

#: wppa-maintenance.php:206
msgid "Orphan photos"
msgstr ""

#: wppa-maintenance.php:206
msgid "This album contains refound lost photos"
msgstr ""

#: wppa-maintenance.php:234
msgid "Feature must be enabled in Table IV-A28 first"
msgstr ""

#: wppa-maintenance.php:241
#, php-format
msgid "From album %d does not exist"
msgstr ""

#: wppa-maintenance.php:246
#, php-format
msgid "To album %d does not exist"
msgstr ""

#: wppa-maintenance.php:250
msgid "From and To albums are identical"
msgstr ""

#: wppa-maintenance.php:1154
#, php-format
msgid ""
"List of Searcheable words <small>( Max 1000 entries of total %d )</small>"
msgstr ""

#: wppa-maintenance.php:1187
msgid "There are no index items."
msgstr ""

#: wppa-maintenance.php:1197
msgid "List of WPPA+ log messages"
msgstr ""

#: wppa-maintenance.php:1202
msgid "There are no log messages"
msgstr ""

#: wppa-maintenance.php:1223
#, php-format
msgid "List of recent ratings <small>( Max 1000 entries of total %d )</small>"
msgstr ""

#: wppa-maintenance.php:1273
msgid "There are no ratings"
msgstr ""

#: wppa-maintenance.php:1284
#, php-format
msgid "List of sessions <small>( Max 1000 entries of total %d )</small>"
msgstr ""

#: wppa-maintenance.php:1346
msgid "There are no active sessions"
msgstr ""

#: wppa-maintenance.php:1363
#, php-format
msgid "List of comments <small>( Max 1000 entries of total %d )</small>"
msgstr ""

#: wppa-maintenance.php:1409
msgid "There are no comments"
msgstr ""

#: wppa-maintenance.php:1526
msgid "WP Photo Album Plus Logfile"
msgstr ""

#: wppa-multitag-widget.php:13
msgid "Display checkboxes to select photos by one or more tags"
msgstr ""

#: wppa-multitag-widget.php:14
msgid "WPPA+ Photo Tags Filter"
msgstr ""

#: wppa-multitag-widget.php:34 wppa-multitag-widget.php:68
msgid "Photo Tags Filter"
msgstr ""

#: wppa-multitag-widget.php:80
msgid "Number of columns"
msgstr ""

#: wppa-multitag-widget.php:90 wppa-tagcloud-widget.php:83
msgid "Select multiple tags or --- all ---"
msgstr ""

#: wppa-multitag-widget.php:96 wppa-tagcloud-widget.php:92
msgid "Currently selected tags"
msgstr ""

#: wppa-non-admin.php:430
msgid "Press f for fullscreen."
msgstr "全屏显示的，请按 f。"

#: wppa-non-admin.php:576
msgid ""
"Keys: f = next mode; q,x = exit; p = previous, n = next, s = start/stop, d = "
"dismiss this notice."
msgstr ""
"键: f = 下一模式;q，x = 退出;p = 以前，n = 下一步，s = 启动/停止，d = 驳回此"
"通知。"

#: wppa-non-admin.php:577
msgid "Keys: f = next mode; q,x = exit; d = dismiss this notice."
msgstr "键: f = 下一模式;q，x = 退出;d = 驳回此通知。"

#: wppa-non-admin.php:582
msgid "Toggle fullscreen"
msgstr ""

#: wppa-non-admin.php:851 wppa-settings-autosave.php:3892
#: wppa-slideshow.php:328
msgid "Start"
msgstr "开始"

#: wppa-non-admin.php:852
msgid "Stop"
msgstr "停止"

#: wppa-non-admin.php:853 wppa-slideshow.php:260 wppa-slideshow.php:320
msgid "Slower"
msgstr "较慢"

#: wppa-non-admin.php:854 wppa-slideshow.php:280 wppa-slideshow.php:336
msgid "Faster"
msgstr "较快"

#: wppa-non-admin.php:856
msgid "of"
msgstr "共"

#: wppa-non-admin.php:857 wppa-slideshow.php:1581
msgid "Previous photo"
msgstr "以前的照片"

#: wppa-non-admin.php:858 wppa-slideshow.php:1590
msgid "Next photo"
msgstr "下一张照片"

#: wppa-non-admin.php:859
msgid "Prev."
msgstr "上一条."

#: wppa-non-admin.php:861 wppa-slideshow.php:1007 wppa-slideshow.php:1048
#: wppa-slideshow.php:1197
msgid "Average&nbsp;rating"
msgstr "平均评级"

#: wppa-non-admin.php:862 wppa-slideshow.php:1008 wppa-slideshow.php:1156
msgid "My&nbsp;rating"
msgstr "我的评分"

#: wppa-non-admin.php:863 wppa-slideshow.php:1007
msgid "Avg."
msgstr "平均"

#: wppa-non-admin.php:864 wppa-slideshow.php:1008
msgid "Mine"
msgstr "挖"

#: wppa-non-admin.php:865
msgid "You marked this image as inappropriate."
msgstr "您标记这是不恰当的图像。"

#: wppa-non-admin.php:868
msgid "Please enter your name"
msgstr "填写你的名字"

#: wppa-non-admin.php:869
msgid "Please enter a valid email address"
msgstr "请输入一个有效的email地址"

#: wppa-non-admin.php:870
msgid "Please enter a comment"
msgstr "请输入注释"

#: wppa-non-admin.php:904
msgid "Double click to start/stop slideshow running"
msgstr "双击启动/停止运行的幻灯片放映"

#: wppa-non-admin.php:905
msgid "Click to view"
msgstr ""

#: wppa-non-admin.php:1000 wppa-photo-admin-autosave.php:670
#: wppa-potd-widget.php:182
msgid "By:"
msgstr ""

#: wppa-non-admin.php:1003
msgid "No album defined (yet)"
msgstr ""

#: wppa-non-admin.php:1007 wppa-thumbnail-widget.php:122
msgid "There are no photos (yet)"
msgstr ""

#: wppa-non-admin.php:1010 wppa-upldr-widget.php:100
msgid "There are too many registered users in the system for this widget"
msgstr ""

#: wppa-non-admin.php:1011 wppa-upldr-widget.php:142 wppa-upldr-widget.php:149
msgid "Photos uploaded by"
msgstr ""

#: wppa-non-admin.php:1015 wppa-thumbnails.php:745 wppa-thumbnails.php:1542
#: wppa-topten-widget.php:216 wppa-topten-widget.php:239
#: wppa-topten-widget.php:249
#, php-format
msgid "%d view"
msgid_plural "%d views"
msgstr[0] ""

#: wppa-non-admin.php:1017 wppa-topten-widget.php:274
msgid "There are no rated photos (yet)"
msgstr ""

#: wppa-photo-admin-autosave.php:39
msgid "Edit photos"
msgstr ""

#: wppa-photo-admin-autosave.php:59
msgid "Moderate photos"
msgstr ""

#: wppa-photo-admin-autosave.php:177 wppa-photo-admin-autosave.php:2111
msgid "You do not have the rights to do this"
msgstr ""

#: wppa-photo-admin-autosave.php:229
msgid "This photo is no longer awaiting moderation."
msgstr ""

#: wppa-photo-admin-autosave.php:237
msgid "There are no photos awaiting moderation at this time."
msgstr ""

#: wppa-photo-admin-autosave.php:245
msgid "Manage all photos by timestamp"
msgstr ""

#: wppa-photo-admin-autosave.php:287
#, php-format
msgid "Photo %s has been removed."
msgstr ""

#: wppa-photo-admin-autosave.php:299 wppa-photo-admin-autosave.php:2735
msgid "No photos matching your search criteria."
msgstr ""

#: wppa-photo-admin-autosave.php:307 wppa-photo-admin-autosave.php:2741
msgid "No photos yet in this album."
msgstr ""

#: wppa-photo-admin-autosave.php:326 wppa-photo-admin-autosave.php:2366
msgid "Please select an album to move to first."
msgstr ""

#: wppa-photo-admin-autosave.php:331 wppa-photo-admin-autosave.php:2371
msgid "Are you sure you want to move this video?"
msgstr ""

#: wppa-photo-admin-autosave.php:334 wppa-photo-admin-autosave.php:2374
msgid "Are you sure you want to move this photo?"
msgstr ""

#: wppa-photo-admin-autosave.php:347
msgid "Please select an album to copy to first."
msgstr ""

#: wppa-photo-admin-autosave.php:352
msgid "Are you sure you want to copy this video?"
msgstr ""

#: wppa-photo-admin-autosave.php:355
msgid "Are you sure you want to copy this photo?"
msgstr ""

#: wppa-photo-admin-autosave.php:368
msgid "Are you sure you want to delete this video?"
msgstr ""

#: wppa-photo-admin-autosave.php:371 wppa-photo-admin-autosave.php:2567
msgid "Are you sure you want to delete this photo?"
msgstr ""

#: wppa-photo-admin-autosave.php:385
msgid "Are you sure you want to rotate this photo left?"
msgstr ""

#: wppa-photo-admin-autosave.php:394
msgid "Are you sure you want to rotate this photo 180&deg;?"
msgstr ""

#: wppa-photo-admin-autosave.php:403
msgid "Are you sure you want to rotate this photo right?"
msgstr ""

#: wppa-photo-admin-autosave.php:412 wppa-photo-admin-autosave.php:421
msgid "Are you sure you want to flip this photo?"
msgstr ""

#: wppa-photo-admin-autosave.php:432
msgid "No watermark selected"
msgstr ""

#: wppa-photo-admin-autosave.php:435
msgid "Are you sure? Once applied it can not be removed!"
msgstr ""

#: wppa-photo-admin-autosave.php:437
msgid "And I do not know if there is already a watermark on this photo"
msgstr ""

#: wppa-photo-admin-autosave.php:446
msgid "Are you sure you want to magically process this photo?"
msgstr ""

#: wppa-photo-admin-autosave.php:477
msgid "Move video"
msgstr ""

#: wppa-photo-admin-autosave.php:478
msgid "Move photo"
msgstr ""

#: wppa-photo-admin-autosave.php:479
msgid "Copy video"
msgstr ""

#: wppa-photo-admin-autosave.php:480
msgid "Copy photo"
msgstr ""

#: wppa-photo-admin-autosave.php:544 wppa-watermark.php:616
msgid "top - left"
msgstr ""

#: wppa-photo-admin-autosave.php:544 wppa-watermark.php:616
msgid "top - center"
msgstr ""

#: wppa-photo-admin-autosave.php:544 wppa-watermark.php:616
msgid "top - right"
msgstr ""

#: wppa-photo-admin-autosave.php:545 wppa-watermark.php:617
msgid "center - left"
msgstr ""

#: wppa-photo-admin-autosave.php:545 wppa-watermark.php:617
msgid "center - center"
msgstr ""

#: wppa-photo-admin-autosave.php:545 wppa-watermark.php:617
msgid "center - right"
msgstr ""

#: wppa-photo-admin-autosave.php:546 wppa-watermark.php:618
msgid "bottom - left"
msgstr ""

#: wppa-photo-admin-autosave.php:546 wppa-watermark.php:618
msgid "bottom - center"
msgstr ""

#: wppa-photo-admin-autosave.php:546 wppa-watermark.php:618
msgid "bottom - right"
msgstr ""

#: wppa-photo-admin-autosave.php:550
#, php-format
msgid "Edit/Moderate photos from album %s by %s"
msgstr ""

#: wppa-photo-admin-autosave.php:606
msgid "Preview fullsize video"
msgstr ""

#: wppa-photo-admin-autosave.php:627
msgid "Preview fullsize photo"
msgstr ""

#: wppa-photo-admin-autosave.php:645
msgid "Audio disabled"
msgstr ""

#: wppa-photo-admin-autosave.php:664
msgid "Filename:"
msgstr ""

#: wppa-photo-admin-autosave.php:667
msgid "Upload:"
msgstr ""

#: wppa-photo-admin-autosave.php:691
#, php-format
msgid "Album: %d (%s)."
msgstr ""

#: wppa-photo-admin-autosave.php:697
msgid "Trashed"
msgstr ""

#: wppa-photo-admin-autosave.php:715
msgid "EXIF Date:"
msgstr ""

#: wppa-photo-admin-autosave.php:735
msgid "Location:"
msgstr ""

#: wppa-photo-admin-autosave.php:741
msgid "Lat:"
msgstr ""

#: wppa-photo-admin-autosave.php:750
msgid "Lon:"
msgstr ""

#: wppa-photo-admin-autosave.php:763
msgid "Photo sort order #:"
msgstr ""

#: wppa-photo-admin-autosave.php:786
msgid "Likes:"
msgstr ""

#: wppa-photo-admin-autosave.php:792
msgid "Rating:"
msgstr ""

#: wppa-photo-admin-autosave.php:793
msgid "Entries:"
msgstr ""

#: wppa-photo-admin-autosave.php:795
msgid "Mean value:"
msgstr ""

#: wppa-photo-admin-autosave.php:809
#, php-format
msgid "Disliked by %d visitor"
msgid_plural "Disliked by %d visitors"
msgstr[0] ""

#: wppa-photo-admin-autosave.php:816
#, php-format
msgid "%d pending votes."
msgstr ""

#: wppa-photo-admin-autosave.php:823
msgid "Views"
msgstr ""

#: wppa-photo-admin-autosave.php:831
msgid "Clicks"
msgstr ""

#: wppa-photo-admin-autosave.php:838
msgid "Status:"
msgstr ""

#: wppa-photo-admin-autosave.php:856 wppa-photo-admin-autosave.php:887
#: wppa-photo-admin-autosave.php:2450 wppa-photo-admin-autosave.php:2684
#: wppa-photo-admin-autosave.php:2696 wppa-potd-admin.php:226
msgid "Publish"
msgstr ""

#: wppa-photo-admin-autosave.php:859 wppa-photo-admin-autosave.php:888
#: wppa-photo-admin-autosave.php:2452 wppa-photo-admin-autosave.php:2686
#: wppa-potd-admin.php:227
msgid "Featured"
msgstr ""

#: wppa-photo-admin-autosave.php:871 wppa-photo-admin-autosave.php:892
#: wppa-photo-admin-autosave.php:2456 wppa-photo-admin-autosave.php:2690
#: wppa-photo-admin-autosave.php:2701
msgid "Scheduled"
msgstr ""

#: wppa-photo-admin-autosave.php:874 wppa-photo-admin-autosave.php:893
#: wppa-photo-admin-autosave.php:2457 wppa-photo-admin-autosave.php:2691
#: wppa-photo-admin-autosave.php:2702
msgid "Private"
msgstr ""

#: wppa-photo-admin-autosave.php:897
msgid ""
"Note: Featured photos should have a descriptive name; a name a search engine "
"will look for!"
msgstr ""

#: wppa-photo-admin-autosave.php:905
msgid "Delete at"
msgstr ""

#: wppa-photo-admin-autosave.php:925
#, php-format
msgid "Video %s is not modified yet"
msgstr ""

#: wppa-photo-admin-autosave.php:926
#, php-format
msgid "Photo %s is not modified yet"
msgstr ""

#: wppa-photo-admin-autosave.php:934
msgid "Available files:"
msgstr ""

#: wppa-photo-admin-autosave.php:938
msgid "Source file:"
msgstr ""

#: wppa-photo-admin-autosave.php:948 wppa-photo-admin-autosave.php:965
#: wppa-photo-admin-autosave.php:982
msgid "Unavailable"
msgstr ""

#: wppa-photo-admin-autosave.php:953
msgid "Poster file:"
msgstr ""

#: wppa-photo-admin-autosave.php:953
msgid "Display file:"
msgstr ""

#: wppa-photo-admin-autosave.php:972
msgid "Thumbnail file:"
msgstr ""

#: wppa-photo-admin-autosave.php:993
msgid "Video size:"
msgstr ""

#: wppa-photo-admin-autosave.php:994
msgid "Width:"
msgstr ""

#: wppa-photo-admin-autosave.php:1001 wppa-photo-admin-autosave.php:1009
#, php-format
msgid "pix, (0=default:%s)"
msgstr ""

#: wppa-photo-admin-autosave.php:1002
msgid "Height:"
msgstr ""

#: wppa-photo-admin-autosave.php:1011 wppa-photo-admin-autosave.php:1031
msgid "Formats:"
msgstr ""

#: wppa-photo-admin-autosave.php:1016 wppa-photo-admin-autosave.php:1036
msgid "Filesize:"
msgstr ""

#: wppa-photo-admin-autosave.php:1070
msgid "Stereophoto:"
msgstr ""

#: wppa-photo-admin-autosave.php:1076
msgid "no stereo image or ready anaglyph"
msgstr ""

#: wppa-photo-admin-autosave.php:1079
msgid "Left - right stereo image"
msgstr ""

#: wppa-photo-admin-autosave.php:1082
msgid "Right - left stereo image"
msgstr ""

#: wppa-photo-admin-autosave.php:1086
msgid "Images:"
msgstr ""

#: wppa-photo-admin-autosave.php:1118 wppa-photo-admin-autosave.php:1149
msgid "Watermark:"
msgstr ""

#: wppa-photo-admin-autosave.php:1126 wppa-photo-admin-autosave.php:1152
msgid "Pos:"
msgstr ""

#: wppa-photo-admin-autosave.php:1136
msgid "Apply watermark"
msgstr ""

#: wppa-photo-admin-autosave.php:1151
msgid "File:"
msgstr ""

#: wppa-photo-admin-autosave.php:1185 wppa-photo-admin-autosave.php:1380
msgid "Rotate left"
msgstr ""

#: wppa-photo-admin-autosave.php:1191 wppa-photo-admin-autosave.php:1389
msgid "Rotate 180&deg;"
msgstr ""

#: wppa-photo-admin-autosave.php:1197 wppa-photo-admin-autosave.php:1398
msgid "Rotate right"
msgstr ""

#: wppa-photo-admin-autosave.php:1203 wppa-photo-admin-autosave.php:1209
#: wppa-photo-admin-autosave.php:1407
msgid "Flip"
msgstr ""

#: wppa-photo-admin-autosave.php:1220
msgid "Remake display file and thumbnail file"
msgstr ""

#: wppa-photo-admin-autosave.php:1222
msgid "Remake files"
msgstr ""

#: wppa-photo-admin-autosave.php:1232 wppa-photo-admin-autosave.php:1234
msgid "Remake thumbnail file"
msgstr ""

#: wppa-photo-admin-autosave.php:1258 wppa-photo-admin-autosave.php:1268
msgid "Target album for copy/move:"
msgstr ""

#: wppa-photo-admin-autosave.php:1273
msgid "Album id"
msgstr ""

#: wppa-photo-admin-autosave.php:1302
msgid "Undelete video"
msgstr ""

#: wppa-photo-admin-autosave.php:1302
msgid "Undelete photo"
msgstr ""

#: wppa-photo-admin-autosave.php:1303
msgid "Delete video"
msgstr ""

#: wppa-photo-admin-autosave.php:1303
msgid "Delete photo"
msgstr ""

#: wppa-photo-admin-autosave.php:1314
msgid "Re-upload file"
msgstr ""

#: wppa-photo-admin-autosave.php:1329 wppa-settings-autosave.php:3308
#: wppa-settings-autosave.php:6265
msgid "Upload"
msgstr ""

#: wppa-photo-admin-autosave.php:1366
msgid ""
"<b>ImageMagick</b> commands. The operations are executed upon the display "
"file."
msgstr ""

#: wppa-photo-admin-autosave.php:1367
msgid "A new thumbnail image will be created from the display file."
msgstr ""

#: wppa-photo-admin-autosave.php:1417
msgid "Flop"
msgstr ""

#: wppa-photo-admin-autosave.php:1427
msgid "Enhance"
msgstr ""

#: wppa-photo-admin-autosave.php:1437
msgid "Sharpen"
msgstr ""

#: wppa-photo-admin-autosave.php:1447
msgid "Blur"
msgstr ""

#: wppa-photo-admin-autosave.php:1457
msgid "Auto Gamma"
msgstr ""

#: wppa-photo-admin-autosave.php:1467
msgid "Auto Level"
msgstr ""

#: wppa-photo-admin-autosave.php:1477
msgid "Contrast+"
msgstr ""

#: wppa-photo-admin-autosave.php:1487
msgid "Contrast-"
msgstr ""

#: wppa-photo-admin-autosave.php:1497
msgid "Brightness+"
msgstr ""

#: wppa-photo-admin-autosave.php:1507
msgid "Brightness-"
msgstr ""

#: wppa-photo-admin-autosave.php:1517
msgid "Despeckle"
msgstr ""

#: wppa-photo-admin-autosave.php:1527
msgid "Linear gray"
msgstr ""

#: wppa-photo-admin-autosave.php:1537
msgid "Non-linear gray"
msgstr ""

#: wppa-photo-admin-autosave.php:1547
msgid "Charcoal"
msgstr ""

#: wppa-photo-admin-autosave.php:1557
msgid "Paint"
msgstr ""

#: wppa-photo-admin-autosave.php:1567
msgid "Sepia"
msgstr ""

#: wppa-photo-admin-autosave.php:1576
msgid "<b>ImageMagick</b> command stack"
msgstr ""

#: wppa-photo-admin-autosave.php:1589
msgid "Undo"
msgstr ""

#: wppa-photo-admin-autosave.php:1590
msgid "Undo last Magick command"
msgstr ""

#: wppa-photo-admin-autosave.php:1653
msgid "Photoname:"
msgstr ""

#: wppa-photo-admin-autosave.php:1694
msgid "Update Photo description"
msgstr ""

#: wppa-photo-admin-autosave.php:1740
msgid "Tags:"
msgstr ""

#: wppa-photo-admin-autosave.php:1756
msgid "Separate tags with commas."
msgstr ""

#: wppa-photo-admin-autosave.php:1767
msgid "- select -"
msgstr ""

#: wppa-photo-admin-autosave.php:1772
msgid "- clear -"
msgstr ""

#: wppa-photo-admin-autosave.php:1776
msgid "No tags yet"
msgstr ""

#: wppa-photo-admin-autosave.php:1782
msgid "Select to add"
msgstr ""

#: wppa-photo-admin-autosave.php:1830
msgid "Autopage Permalink:"
msgstr ""

#: wppa-photo-admin-autosave.php:1847
msgid "Photo specific link url:"
msgstr ""

#: wppa-photo-admin-autosave.php:1868
msgid "Same tab"
msgstr ""

#: wppa-photo-admin-autosave.php:1874 wppa-settings-autosave.php:5171
#: wppa-settings-autosave.php:6234
msgid "New tab"
msgstr ""

#: wppa-photo-admin-autosave.php:1880
msgid "Tryit!"
msgstr ""

#: wppa-photo-admin-autosave.php:1889
msgid "Photo specific link title:"
msgstr ""

#: wppa-photo-admin-autosave.php:1903
msgid ""
"If you want this link to be used, check 'PS Overrule' checkbox in table VI."
msgstr ""

#: wppa-photo-admin-autosave.php:1918
msgid "HTML Alt attribute:"
msgstr ""

#: wppa-photo-admin-autosave.php:1942
msgid "Single image shortcode"
msgstr ""

#: wppa-photo-admin-autosave.php:1949
#, php-format
msgid "See %s The documentation %s for more shortcode options."
msgstr ""

#: wppa-photo-admin-autosave.php:1964
msgid "Permalink"
msgstr ""

#: wppa-photo-admin-autosave.php:1979
msgid "Hi resolution url"
msgstr ""

#: wppa-photo-admin-autosave.php:1994
msgid "Display file url"
msgstr ""

#: wppa-photo-admin-autosave.php:2010
msgid "Thumbnail file url"
msgstr ""

#: wppa-photo-admin-autosave.php:2067 wppa-photo-admin-autosave.php:2076
msgid "Trash"
msgstr ""

#: wppa-photo-admin-autosave.php:2206
#, php-format
msgid "Time is out after processing %d out of %d items."
msgstr ""

#: wppa-photo-admin-autosave.php:2219
#, php-format
msgid "%d photos deleted."
msgstr ""

#: wppa-photo-admin-autosave.php:2222
#, php-format
msgid "%1$s photos moved to album %2$s."
msgstr ""

#: wppa-photo-admin-autosave.php:2225
#, php-format
msgid "%1$s photos copied to album %2$s."
msgstr ""

#: wppa-photo-admin-autosave.php:2228
#, php-format
msgid "Changed status to %1$s on %2$s photos."
msgstr ""

#: wppa-photo-admin-autosave.php:2231
#, php-format
msgid "Changed owner to %1$s on %2$s photos."
msgstr ""

#: wppa-photo-admin-autosave.php:2234
#, php-format
msgid "%d photos processed."
msgstr ""

#: wppa-photo-admin-autosave.php:2378
msgid "Moving..."
msgstr ""

#: wppa-photo-admin-autosave.php:2425
msgid "Bulk action:"
msgstr ""

#: wppa-photo-admin-autosave.php:2429
msgid "Move to"
msgstr ""

#: wppa-photo-admin-autosave.php:2430
msgid "Copy to"
msgstr ""

#: wppa-photo-admin-autosave.php:2432
msgid "Set status to"
msgstr ""

#: wppa-photo-admin-autosave.php:2435
msgid "Set owner to"
msgstr ""

#: wppa-photo-admin-autosave.php:2448
msgid "- select a status -"
msgstr ""

#: wppa-photo-admin-autosave.php:2463
msgid "- select an owner -"
msgstr ""

#: wppa-photo-admin-autosave.php:2475
msgid "Doit!"
msgstr ""

#: wppa-photo-admin-autosave.php:2483
msgid "the previous page"
msgstr ""

#: wppa-photo-admin-autosave.php:2484
msgid "the same page"
msgstr ""

#: wppa-photo-admin-autosave.php:2485
msgid "the next page"
msgstr ""

#: wppa-photo-admin-autosave.php:2487
#, php-format
msgid "Go to %s after Doit!."
msgstr ""

#: wppa-photo-admin-autosave.php:2500
msgid "Confirm delete"
msgstr ""

#: wppa-photo-admin-autosave.php:2509
msgid "Confirm move"
msgstr ""

#: wppa-photo-admin-autosave.php:2524 wppa-photo-admin-autosave.php:2720
#: wppa-potd-admin.php:321 wppa-settings-autosave.php:9160
msgid "Preview"
msgstr ""

#: wppa-photo-admin-autosave.php:2529 wppa-photo-admin-autosave.php:2725
msgid "Remark"
msgstr ""

#: wppa-photo-admin-autosave.php:2539
#, php-format
msgid "Moderate photos from album %s by %s"
msgstr ""

#: wppa-photo-admin-autosave.php:2568
msgid "Deleting..."
msgstr ""

#: wppa-photo-admin-autosave.php:2662
msgid "Target album for move to:"
msgstr ""

#: wppa-photo-admin-autosave.php:2738
msgid "No photos to moderate"
msgstr ""

#: wppa-photo-admin-autosave.php:2746
#, php-format
msgid "Page %d is empty, try <a href=\"%s\" >page %d</a>."
msgstr ""

#: wppa-photo-admin-autosave.php:2871
msgid ""
"Setting photo sequence order has only effect if the photo order method is "
"set to <b>Order#</b>"
msgstr ""

#: wppa-photo-admin-autosave.php:2929
msgid "Id: "
msgstr ""

#: wppa-photo-admin-autosave.php:2930 wppa-settings-autosave.php:392
msgid "Video"
msgstr ""

#: wppa-photo-admin-autosave.php:2931 wppa-settings-autosave.php:372
msgid "Audio"
msgstr ""

#: wppa-photo-admin-autosave.php:2932
msgid "Ord: "
msgstr ""

#: wppa-photo-admin-autosave.php:2945
msgid "The album is empty."
msgstr ""

#: wppa-photo-admin-autosave.php:3012 wppa-photo-admin-autosave.php:3030
#: wppa-settings-autosave.php:4643 wppa-setup.php:359
msgid "Required"
msgstr ""

#: wppa-photo-admin-autosave.php:3055
msgid "Combined"
msgstr ""

#: wppa-photo-admin-autosave.php:3092
msgid "Word"
msgstr ""

#: wppa-photo-admin-autosave.php:3095
msgid "Count"
msgstr ""

#: wppa-photo-admin-autosave.php:3147 wppa-settings-autosave.php:6748
msgid "Tags"
msgstr ""

#: wppa-photo-admin-autosave.php:3267
msgid "Send"
msgstr ""

#: wppa-photo-files.php:160 wppa-photo-files.php:161
#, php-format
msgid "ERROR: File %s is not a valid picture file."
msgstr "錯誤：文件 %s 不是有效的圖片文件。"

#: wppa-photo-files.php:339 wppa-photo-files.php:340
msgid "ERROR: Resized or copied image could not be created."
msgstr "错误: 无法创建已调整大小或复制图像。"

#: wppa-potd-admin.php:55
msgid "Photo of the Day (Widget) Settings"
msgstr ""

#: wppa-potd-admin.php:56
msgid "Changes are updated immediately. The page will reload if required."
msgstr ""

#: wppa-potd-admin.php:70 wppa-potd-admin.php:357
#: wppa-settings-autosave.php:517 wppa-settings-autosave.php:688
#: wppa-settings-autosave.php:710 wppa-settings-autosave.php:1500
#: wppa-settings-autosave.php:1521 wppa-settings-autosave.php:3085
#: wppa-settings-autosave.php:3106 wppa-settings-autosave.php:3491
#: wppa-settings-autosave.php:3515 wppa-settings-autosave.php:4926
#: wppa-settings-autosave.php:4947 wppa-settings-autosave.php:5142
#: wppa-settings-autosave.php:5166 wppa-settings-autosave.php:6229
#: wppa-settings-autosave.php:6994 wppa-settings-autosave.php:7016
#: wppa-settings-autosave.php:7867 wppa-settings-autosave.php:7891
#: wppa-settings-autosave.php:9911 wppa-settings-autosave.php:9932
#: wppa-settings-autosave.php:9972 wppa-settings-autosave.php:9994
#: wppa-settings-autosave.php:10040
msgid "#"
msgstr ""

#: wppa-potd-admin.php:73 wppa-potd-admin.php:360
#: wppa-settings-autosave.php:520 wppa-settings-autosave.php:691
#: wppa-settings-autosave.php:713 wppa-settings-autosave.php:1503
#: wppa-settings-autosave.php:1524 wppa-settings-autosave.php:3088
#: wppa-settings-autosave.php:3518 wppa-settings-autosave.php:4929
#: wppa-settings-autosave.php:6997 wppa-settings-autosave.php:7894
#: wppa-settings-autosave.php:9914
msgid "Setting"
msgstr ""

#: wppa-potd-admin.php:74 wppa-potd-admin.php:361
#: wppa-settings-autosave.php:521 wppa-settings-autosave.php:692
#: wppa-settings-autosave.php:714 wppa-settings-autosave.php:1504
#: wppa-settings-autosave.php:1525 wppa-settings-autosave.php:3089
#: wppa-settings-autosave.php:3113 wppa-settings-autosave.php:3498
#: wppa-settings-autosave.php:3519 wppa-settings-autosave.php:4930
#: wppa-settings-autosave.php:4954 wppa-settings-autosave.php:5149
#: wppa-settings-autosave.php:5173 wppa-settings-autosave.php:6236
#: wppa-settings-autosave.php:6999 wppa-settings-autosave.php:7023
#: wppa-settings-autosave.php:7874 wppa-settings-autosave.php:7895
#: wppa-settings-autosave.php:9915 wppa-settings-autosave.php:9937
#: wppa-settings-autosave.php:9977 wppa-settings-autosave.php:9999
#: wppa-settings-autosave.php:10045
msgid "Help"
msgstr ""

#: wppa-potd-admin.php:82
msgid "Widget Title:"
msgstr ""

#: wppa-potd-admin.php:83
msgid "The title of the widget."
msgstr ""

#: wppa-potd-admin.php:84
msgid ""
"Enter/modify the title for the widget. This is a default and can be "
"overriden at widget activation."
msgstr ""

#: wppa-potd-admin.php:89
msgid "Widget Photo Width:"
msgstr ""

#: wppa-potd-admin.php:90
msgid "Enter the desired display width of the photo in the sidebar."
msgstr ""

#: wppa-potd-admin.php:93 wppa-settings-autosave.php:731
#: wppa-settings-autosave.php:740 wppa-settings-autosave.php:862
#: wppa-settings-autosave.php:1024 wppa-settings-autosave.php:1035
msgid "pixels wide"
msgstr ""

#: wppa-potd-admin.php:96 wppa-tinymce-shortcodes.php:624
msgid "Horizontal alignment:"
msgstr ""

#: wppa-potd-admin.php:97
msgid "Enter the desired display alignment of the photo in the sidebar."
msgstr ""

#: wppa-potd-admin.php:100 wppa-settings-autosave.php:3871
#: wppa-settings-autosave.php:3885 wppa-settings-autosave.php:4142
#: wppa-settings-autosave.php:9889 wppa-slideshow-widget.php:243
#: wppa-tinymce-shortcodes.php:629
msgid "center"
msgstr ""

#: wppa-potd-admin.php:109
msgid "Enter the url. Do'nt forget the HTTP://"
msgstr ""

#: wppa-potd-admin.php:115
msgid "Link Title:"
msgstr ""

#: wppa-potd-admin.php:116
msgid "The balloon text when hovering over the photo."
msgstr ""

#: wppa-potd-admin.php:125
msgid "Links are set on the <b>Photo Albums -> Settings</b> screen."
msgstr ""

#: wppa-potd-admin.php:132
msgid "Subtitle:"
msgstr ""

#: wppa-potd-admin.php:133
msgid "Select the content of the subtitle."
msgstr ""

#: wppa-potd-admin.php:137
msgid "Photo Name"
msgstr ""

#: wppa-potd-admin.php:145
msgid "Counter:"
msgstr ""

#: wppa-potd-admin.php:146
msgid "Display a counter of other photos in the album."
msgstr ""

#: wppa-potd-admin.php:153
msgid "The counter links to."
msgstr ""

#: wppa-potd-admin.php:156 wppa-settings-autosave.php:1087
#: wppa-settings-autosave.php:5976 wppa-settings-autosave.php:6010
#: wppa-settings-autosave.php:6107
msgid "thumbnails"
msgstr ""

#: wppa-potd-admin.php:156 wppa-settings-autosave.php:5977
#: wppa-settings-autosave.php:6011 wppa-settings-autosave.php:6108
msgid "slideshow"
msgstr ""

#: wppa-potd-admin.php:156
msgid "single image"
msgstr ""

#: wppa-potd-admin.php:161
msgid "Type of album(s) to use:"
msgstr ""

#: wppa-potd-admin.php:162
msgid "Select physical or virtual."
msgstr ""

#: wppa-potd-admin.php:165
msgid "physical albums"
msgstr ""

#: wppa-potd-admin.php:165
msgid "virtual albums"
msgstr ""

#: wppa-potd-admin.php:170
msgid "Albums to use:"
msgstr ""

#: wppa-potd-admin.php:171 wppa-potd-admin.php:194
msgid "Select the albums to use for the photo of the day."
msgstr ""

#: wppa-potd-admin.php:190 wppa-potd-admin.php:644 wppa-potd-admin.php:669
#: wppa-potd-admin.php:707 wppa-settings-autosave.php:355
#: wppa-settings-autosave.php:7722 wppa-settings-autosave.php:7749
#: wppa-settings-autosave.php:9029 wppa-settings-autosave.php:9031
#: wppa-settings-autosave.php:10440 wppa-settings-autosave.php:10458
#: wppa-settings-autosave.php:10478 wppa-settings-autosave.php:10495
#: wppa-settings-autosave.php:10524 wppa-settings-autosave.php:10542
#: wppa-settings-autosave.php:10566 wppa-settings-autosave.php:10587
#: wppa-settings-autosave.php:10607 wppa-settings-autosave.php:10624
#: wppa-settings-autosave.php:10668 wppa-settings-autosave.php:10721
#: wppa-settings-autosave.php:10753
msgid "Setting unmodified"
msgstr ""

#: wppa-potd-admin.php:195
msgid "- all albums -"
msgstr ""

#: wppa-potd-admin.php:196
msgid "- all -separate- albums -"
msgstr ""

#: wppa-potd-admin.php:197
msgid "- all albums except -separate-"
msgstr ""

#: wppa-potd-admin.php:198
msgid "- top rated photos -"
msgstr ""

#: wppa-potd-admin.php:206
msgid "Include (grand)children:"
msgstr ""

#: wppa-potd-admin.php:207
msgid "Include the photos of all sub albums?"
msgstr ""

#: wppa-potd-admin.php:213
msgid "Inverse selection:"
msgstr ""

#: wppa-potd-admin.php:214
msgid "Use any album, except the selection made above."
msgstr ""

#: wppa-potd-admin.php:221
msgid "Status filter:"
msgstr ""

#: wppa-potd-admin.php:222
msgid "Use only photos with a certain status."
msgstr ""

#: wppa-potd-admin.php:223
msgid "Select - none - if you want no filtering on status."
msgstr ""

#: wppa-potd-admin.php:225
msgid "- none -"
msgstr ""

#: wppa-potd-admin.php:231
msgid "Any medal"
msgstr ""

#: wppa-potd-admin.php:244
msgid "Display method:"
msgstr ""

#: wppa-potd-admin.php:245
msgid "Select the way a photo will be selected."
msgstr ""

#: wppa-potd-admin.php:248
msgid "Fixed photo"
msgstr ""

#: wppa-potd-admin.php:250
msgid "Last upload"
msgstr ""

#: wppa-potd-admin.php:251
msgid "Change every"
msgstr ""

#: wppa-potd-admin.php:258
msgid "Change every period:"
msgstr ""

#: wppa-potd-admin.php:259
msgid "The time period a certain photo is used."
msgstr ""

#: wppa-potd-admin.php:262
msgid "pageview."
msgstr ""

#: wppa-potd-admin.php:263
msgid "hour."
msgstr ""

#: wppa-potd-admin.php:264
msgid "day."
msgstr ""

#: wppa-potd-admin.php:265
msgid "week."
msgstr ""

#: wppa-potd-admin.php:266
msgid "month."
msgstr ""

#: wppa-potd-admin.php:267
msgid "day of week is order#"
msgstr ""

#: wppa-potd-admin.php:268
msgid "day of month is order#"
msgstr ""

#: wppa-potd-admin.php:269
msgid "day of year is order#"
msgstr ""

#: wppa-potd-admin.php:294
msgid "Day offset:"
msgstr ""

#: wppa-potd-admin.php:295
msgid "The difference between daynumber and photo order number."
msgstr ""

#: wppa-potd-admin.php:306
#, php-format
msgid "Current day# = %s, offset ="
msgstr ""

#: wppa-potd-admin.php:315
#, php-format
msgid "Todays photo order# = %s."
msgstr ""

#: wppa-potd-admin.php:322
msgid "Current \"photo of the day\":"
msgstr ""

#: wppa-potd-admin.php:333
msgid "Uploader"
msgstr ""

#: wppa-potd-admin.php:338
msgid "Not found."
msgstr ""

#: wppa-potd-admin.php:342
msgid "Show selection"
msgstr ""

#: wppa-potd-admin.php:343
msgid "Show the photos in the current selection."
msgstr ""

#: wppa-potd-admin.php:386
msgid "Photos in the current selection"
msgstr ""

#: wppa-potd-admin.php:411
msgid "No photos in the selection"
msgstr ""

#: wppa-potd-admin.php:414
#, php-format
msgid "There are too many photos in the selection to show a preview ( %d )"
msgstr ""

#: wppa-potd-admin.php:573 wppa-settings-autosave.php:10368
msgid "The default for this setting is:"
msgstr ""

#: wppa-potd-admin.php:590 wppa-settings-autosave.php:10385
msgid "Click for help"
msgstr ""

#: wppa-potd-admin.php:614 wppa-settings-autosave.php:10769
msgid "Checked"
msgstr ""

#: wppa-potd-admin.php:615 wppa-settings-autosave.php:10770
msgid "Unchecked"
msgstr ""

#: wppa-potd-admin.php:616 wppa-settings-autosave.php:5231
#: wppa-settings-autosave.php:5274 wppa-settings-autosave.php:5352
#: wppa-settings-autosave.php:5395 wppa-settings-autosave.php:5445
#: wppa-settings-autosave.php:5494 wppa-settings-autosave.php:5543
#: wppa-settings-autosave.php:5597 wppa-settings-autosave.php:5635
#: wppa-settings-autosave.php:5687 wppa-settings-autosave.php:5729
#: wppa-settings-autosave.php:5771 wppa-settings-autosave.php:5812
#: wppa-settings-autosave.php:10771
msgid "no link at all."
msgstr ""

#: wppa-potd-admin.php:617 wppa-settings-autosave.php:5232
#: wppa-settings-autosave.php:5275 wppa-settings-autosave.php:5353
#: wppa-settings-autosave.php:5396 wppa-settings-autosave.php:5446
#: wppa-settings-autosave.php:5495 wppa-settings-autosave.php:5544
#: wppa-settings-autosave.php:5598 wppa-settings-autosave.php:5636
#: wppa-settings-autosave.php:5688 wppa-settings-autosave.php:5730
#: wppa-settings-autosave.php:5772 wppa-settings-autosave.php:5813
#: wppa-settings-autosave.php:10772
msgid "the plain photo (file)."
msgstr ""

#: wppa-potd-admin.php:618 wppa-settings-autosave.php:5235
#: wppa-settings-autosave.php:5278 wppa-settings-autosave.php:5354
#: wppa-settings-autosave.php:5399 wppa-settings-autosave.php:5449
#: wppa-settings-autosave.php:5498 wppa-settings-autosave.php:5547
#: wppa-settings-autosave.php:5637 wppa-settings-autosave.php:5690
#: wppa-settings-autosave.php:5732 wppa-settings-autosave.php:5774
#: wppa-settings-autosave.php:10773
msgid "the full size photo in a slideshow."
msgstr ""

#: wppa-potd-admin.php:619 wppa-settings-autosave.php:5236
#: wppa-settings-autosave.php:5279 wppa-settings-autosave.php:5355
#: wppa-settings-autosave.php:5401 wppa-settings-autosave.php:5451
#: wppa-settings-autosave.php:5500 wppa-settings-autosave.php:5549
#: wppa-settings-autosave.php:5639 wppa-settings-autosave.php:5691
#: wppa-settings-autosave.php:5733 wppa-settings-autosave.php:5775
#: wppa-settings-autosave.php:5814 wppa-settings-autosave.php:10774
msgid "the fullsize photo on its own."
msgstr ""

#: wppa-potd-admin.php:620 wppa-settings-autosave.php:10775
msgid "the photo specific link."
msgstr ""

#: wppa-potd-admin.php:621 wppa-settings-autosave.php:5234
#: wppa-settings-autosave.php:5277 wppa-settings-autosave.php:5689
#: wppa-settings-autosave.php:5731 wppa-settings-autosave.php:5773
#: wppa-settings-autosave.php:10776
msgid "the content of the album."
msgstr ""

#: wppa-potd-admin.php:622 wppa-settings-autosave.php:5276
#: wppa-settings-autosave.php:10777
msgid "defined at widget activation."
msgstr ""

#: wppa-potd-admin.php:623 wppa-settings-autosave.php:5233
#: wppa-settings-autosave.php:10778
msgid "defined on widget admin page."
msgstr ""

#: wppa-potd-admin.php:624 wppa-settings-autosave.php:5599
#: wppa-settings-autosave.php:10779
msgid "same as title."
msgstr ""

#: wppa-potd-admin.php:636 wppa-potd-admin.php:659 wppa-potd-admin.php:680
#: wppa-settings-autosave.php:10430 wppa-settings-autosave.php:10450
#: wppa-settings-autosave.php:10468 wppa-settings-autosave.php:10487
#: wppa-settings-autosave.php:10517 wppa-settings-autosave.php:10534
#: wppa-settings-autosave.php:10558 wppa-settings-autosave.php:10579
#: wppa-settings-autosave.php:10599 wppa-settings-autosave.php:10641
msgid "Slug ="
msgstr ""

#: wppa-potd-admin.php:636 wppa-settings-autosave.php:10534
#: wppa-settings-autosave.php:10558 wppa-settings-autosave.php:10579
#: wppa-settings-autosave.php:10599
msgid "Values = yes, no"
msgstr ""

#: wppa-potd-admin.php:680 wppa-settings-autosave.php:10641
msgid "Values = "
msgstr ""

#: wppa-potd-widget.php:15
msgid "Display Photo Of The Day"
msgstr ""

#: wppa-potd-widget.php:16
msgid "WPPA+ Photo Of The Day"
msgstr ""

#: wppa-potd-widget.php:221
msgid ""
"You can set the content and the sizes in this widget in the <b>Photo Albums -"
"> Photo of the day</b> admin page."
msgstr ""

#: wppa-qr-widget.php:14
msgid "Display the QR code of the current url"
msgstr ""

#: wppa-qr-widget.php:15
msgid "WPPA+ QR Widget"
msgstr ""

#: wppa-qr-widget.php:33 wppa-qr-widget.php:88
msgid "QR Widget"
msgstr ""

#: wppa-qr-widget.php:97
msgid ""
"You can set the sizes and colors in this widget in the <b>Photo Albums -> "
"Settings</b> admin page Table IX-K1.x."
msgstr ""

#: wppa-search-widget.php:15
msgid "Display search photos dialog"
msgstr ""

#: wppa-search-widget.php:17
msgid "WPPA+ Search Photos"
msgstr ""

#: wppa-search-widget.php:39 wppa-search-widget.php:93
msgid "Search Photos"
msgstr ""

#: wppa-search-widget.php:111
msgid "Text above input field"
msgstr ""

#: wppa-search-widget.php:112
msgid ""
"Enter optional text that will appear before the input box. This may contain "
"HTML so you can change font size and color."
msgstr ""

#: wppa-search-widget.php:120
msgid "Enable rootsearch"
msgstr ""

#: wppa-search-widget.php:121
msgid "See Table IX-E17 to change the label text"
msgstr ""

#: wppa-search-widget.php:136
msgid ""
"If you want the search to be limited to a specific album and its "
"(grand)children, select the album here."
msgstr ""

#: wppa-search-widget.php:138
msgid ""
"If you select an album here, it will overrule the previous checkbox using "
"the album as a 'fixed' root."
msgstr ""

#: wppa-search-widget.php:146
msgid "Enable subsearch"
msgstr ""

#: wppa-search-widget.php:147
msgid "See Table IX-E16 to change the label text"
msgstr ""

#: wppa-search-widget.php:155
msgid "Add category selectionbox"
msgstr ""

#: wppa-search-widget.php:159 wppa-settings-autosave.php:4118
#: wppa-settings-autosave.php:4142 wppa-tinymce-shortcodes.php:516
#: wppa-tinymce-shortcodes.php:535 wppa-watermark.php:587
#: wppa-watermark.php:652
msgid "--- default ---"
msgstr ""

#: wppa-search-widget.php:198 wppa-settings-autosave.php:6160
msgid "Landing page"
msgstr ""

#: wppa-search-widget.php:203
msgid "The default page will be created automatically"
msgstr ""

#: wppa-settings-autosave.php:50
msgid "Close!"
msgstr ""

#: wppa-settings-autosave.php:98
msgid "Saved settings restored"
msgstr ""

#: wppa-settings-autosave.php:101
msgid "Unable to restore saved settings"
msgstr ""

#: wppa-settings-autosave.php:107
msgid "Reset to default settings"
msgstr ""

#: wppa-settings-autosave.php:110
msgid "Unable to set defaults"
msgstr ""

#: wppa-settings-autosave.php:115
#, php-format
msgid "Skinfile %s loaded"
msgstr ""

#: wppa-settings-autosave.php:126 wppa-settings-autosave.php:148
#: wppa-settings-autosave.php:169
#, php-format
msgid "Upload error %s"
msgstr ""

#: wppa-settings-autosave.php:131
#, php-format
msgid "Uploaded file %s is not a .png file"
msgstr ""

#: wppa-settings-autosave.php:135 wppa-settings-autosave.php:156
#: wppa-settings-autosave.php:192
#, php-format
msgid "Upload of %s done"
msgstr ""

#: wppa-settings-autosave.php:140 wppa-settings-autosave.php:161
#: wppa-settings-autosave.php:197
msgid "No file selected or error on upload"
msgstr ""

#: wppa-settings-autosave.php:152
#, php-format
msgid "Uploaded file %s is not a .ttf file"
msgstr ""

#: wppa-settings-autosave.php:174
#, php-format
msgid "Uploaded file %s is not a valid image file"
msgstr ""

#: wppa-settings-autosave.php:253
#, php-format
msgid ""
"%s invalid ratings removed. Please run Table VIII-A5: Rerate to fix the "
"averages."
msgstr ""

#: wppa-settings-autosave.php:264
#, php-format
msgid "%s last album used settings removed."
msgstr ""

#: wppa-settings-autosave.php:272
msgid "WP Photo Album Plus Settings"
msgstr ""

#: wppa-settings-autosave.php:272
msgid "Auto Save"
msgstr ""

#: wppa-settings-autosave.php:275
msgid "Database revision:"
msgstr ""

#: wppa-settings-autosave.php:276
msgid "WP Charset:"
msgstr ""

#: wppa-settings-autosave.php:277
msgid "Current PHP version:"
msgstr ""

#: wppa-settings-autosave.php:278
msgid "WPPA+ API Version:"
msgstr ""

#: wppa-settings-autosave.php:282
msgid "Multisite in singlesite mode."
msgstr ""

#: wppa-settings-autosave.php:285
msgid "Multisite enabled."
msgstr ""

#: wppa-settings-autosave.php:287
msgid "Blogid ="
msgstr ""

#: wppa-settings-autosave.php:305
msgid "Please de-activate plugin <i style=\"font-size:14px;\">"
msgstr ""

#: wppa-settings-autosave.php:305
msgid ". </i>This plugin will cause wppa+ to function not properly."
msgstr ""

#: wppa-settings-autosave.php:315
msgid "Please note that plugin <i style=\"font-size:14px;\">"
msgstr ""

#: wppa-settings-autosave.php:315
msgid "</i> can cause wppa+ to function not properly if it is misconfigured."
msgstr ""

#: wppa-settings-autosave.php:320
msgid ""
"There is a serious misconfiguration in your servers PHP config. Function "
"wppa_imagecreatefromjpeg() does not exist. You will encounter problems when "
"uploading photos and not be able to generate thumbnail images. Ask your "
"hosting provider to add GD support with a minimal version 1.8."
msgstr ""

#: wppa-settings-autosave.php:328
msgid "Remove empty albums needs completion. See Table VIII"
msgstr ""

#: wppa-settings-autosave.php:329
msgid "Applying new photo description needs completion. See Table VIII"
msgstr ""

#: wppa-settings-autosave.php:330
msgid "Appending to photo description needs completion. See Table VIII"
msgstr ""

#: wppa-settings-autosave.php:331
msgid "Removing from photo description needs completion. See Table VIII"
msgstr ""

#: wppa-settings-autosave.php:332
msgid "Removing file extensions needs completion. See Table VIII"
msgstr ""

#: wppa-settings-autosave.php:333
msgid "Regenerating the Thumbnails needs completion. See Table VIII"
msgstr ""

#: wppa-settings-autosave.php:334
msgid "Rerating needs completion. See Table VIII"
msgstr ""

#: wppa-settings-autosave.php:342
msgid ""
"A thumbframe width or height should not be smaller than a thumbnail size. "
"Please correct the corresponding setting(s) in Table I-C"
msgstr ""

#: wppa-settings-autosave.php:347
msgid "Show legenda"
msgstr ""

#: wppa-settings-autosave.php:349
msgid "Legenda:"
msgstr ""

#: wppa-settings-autosave.php:350 wppa-settings-autosave.php:352
#: wppa-settings-autosave.php:2101 wppa-settings-autosave.php:5904
msgid "Button"
msgstr ""

#: wppa-settings-autosave.php:350
msgid "action that causes page reload."
msgstr ""

#: wppa-settings-autosave.php:352 wppa-settings-autosave.php:10802
#: wppa-settings-autosave.php:10821
msgid "Are you sure?"
msgstr ""

#: wppa-settings-autosave.php:353
msgid "action that does not cause page reload."
msgstr ""

#: wppa-settings-autosave.php:357
msgid "Update in progress"
msgstr ""

#: wppa-settings-autosave.php:359
msgid "Setting updated"
msgstr ""

#: wppa-settings-autosave.php:361
msgid "Update failed"
msgstr ""

#: wppa-settings-autosave.php:363
msgid "Hide this"
msgstr ""

#: wppa-settings-autosave.php:369
msgid "System"
msgstr ""

#: wppa-settings-autosave.php:370
msgid "Access"
msgstr ""

#: wppa-settings-autosave.php:374
msgid "Counts"
msgstr ""

#: wppa-settings-autosave.php:375
msgid "Covers"
msgstr ""

#: wppa-settings-autosave.php:376
msgid "Layout"
msgstr ""

#: wppa-settings-autosave.php:377 wppa-settings-autosave.php:5102
msgid "Lightbox"
msgstr ""

#: wppa-settings-autosave.php:378
msgid "Links"
msgstr ""

#: wppa-settings-autosave.php:379
msgid "Mail"
msgstr ""

#: wppa-settings-autosave.php:380
msgid "Metadata"
msgstr ""

#: wppa-settings-autosave.php:381
msgid "Navigation"
msgstr ""

#: wppa-settings-autosave.php:383 wppa-settings-autosave.php:9192
#: wppa-settings-autosave.php:9237
msgid "Rating"
msgstr ""

#: wppa-settings-autosave.php:385
msgid "Sizes"
msgstr ""

#: wppa-settings-autosave.php:386
msgid "Slideshows"
msgstr ""

#: wppa-settings-autosave.php:387
msgid "Social Media"
msgstr ""

#: wppa-settings-autosave.php:389
msgid "Uploads"
msgstr ""

#: wppa-settings-autosave.php:390
msgid "Widgets"
msgstr ""

#: wppa-settings-autosave.php:391 wppa-settings-autosave.php:9010
msgid "Watermark"
msgstr ""

#: wppa-settings-autosave.php:399
msgid "Click on the banner of a (sub)table to open/close it, or"
msgstr ""

#: wppa-settings-autosave.php:401
msgid "Show settings related to:"
msgstr ""

#: wppa-settings-autosave.php:407
msgid "and ( optionally ) to:"
msgstr ""

#: wppa-settings-autosave.php:423
msgid "Quick setup"
msgstr ""

#: wppa-settings-autosave.php:425
msgid "Do a quick initial setup"
msgstr ""

#: wppa-settings-autosave.php:426
msgid "Close quick setup"
msgstr ""

#: wppa-settings-autosave.php:468
msgid "--- please select a page ---"
msgstr ""

#: wppa-settings-autosave.php:497
msgid "--- No page to link to (yet) ---"
msgstr ""

#: wppa-settings-autosave.php:502
msgid "--- Will be auto created ---"
msgstr ""

#: wppa-settings-autosave.php:509
msgid "Table O:"
msgstr ""

#: wppa-settings-autosave.php:509
msgid "Quick Setup:"
msgstr ""

#: wppa-settings-autosave.php:510
msgid "This table enables you to quickly do an inital setup."
msgstr ""

#: wppa-settings-autosave.php:532
msgid ""
"To quickly setup WPPA+ please answer the following questions. You can alway "
"change any setting later. <a>Click on me!</a>"
msgstr ""

#: wppa-settings-autosave.php:534
msgid "Is your theme <i>responsive</i>?"
msgstr ""

#: wppa-settings-autosave.php:535
msgid ""
"Responsive themes have a layout that varies with the size of the browser "
"window."
msgstr ""

#: wppa-settings-autosave.php:536
msgid ""
"WPPA+ needs to know this to automatically adept the width of the display to "
"the available width on the page."
msgstr ""

#: wppa-settings-autosave.php:543
msgid "Do you want to downsize photos during upload?"
msgstr ""

#: wppa-settings-autosave.php:544
msgid ""
"Downsizing photos make them load faster to the visitor, without loosing "
"display quality"
msgstr ""

#: wppa-settings-autosave.php:545
msgid ""
"If you answer yes, the photos will be downsized to max 1024 x 768 pixels. "
"You can change this later, if you like"
msgstr ""

#: wppa-settings-autosave.php:552
msgid "Do you want to save the original photofiles?"
msgstr ""

#: wppa-settings-autosave.php:553
msgid "This will require considerable disk space on the server."
msgstr ""

#: wppa-settings-autosave.php:554
msgid ""
"If you answer yes, you will be able to remove watermarks you applied with "
"wppa+ in a later stage, redo downsizing to a larger size afterwards, and "
"supply fullsize images for download."
msgstr ""

#: wppa-settings-autosave.php:561
msgid "May visitors upload photos?"
msgstr ""

#: wppa-settings-autosave.php:562
msgid ""
"It is safe to do so, but i will have to do some settings to keep it safe!"
msgstr ""

#: wppa-settings-autosave.php:563
msgid ""
"If you answer yes, i will assume you want to enable logged in users to "
"upload photos at the front-end of the website and allow them to edit their "
"photos name and descriptions."
msgstr ""

#: wppa-settings-autosave.php:564
msgid ""
"The photos will be hold for moderation, the admin will get notified by email."
msgstr ""

#: wppa-settings-autosave.php:565
msgid ""
"Each user will get his own album to upload to. These settings can be changed "
"later."
msgstr ""

#: wppa-settings-autosave.php:572
msgid "Do you want the rating system active?"
msgstr ""

#: wppa-settings-autosave.php:573
msgid "Enable the rating system and show the votes in the slideshow."
msgstr ""

#: wppa-settings-autosave.php:574
msgid "You can configure the details of the rating system later"
msgstr ""

#: wppa-settings-autosave.php:581
msgid "Do you want the comment system active?"
msgstr ""

#: wppa-settings-autosave.php:582
msgid "Enable the comment system and show the comments in the slideshow."
msgstr ""

#: wppa-settings-autosave.php:583
msgid "You can configure the details of the comment system later"
msgstr ""

#: wppa-settings-autosave.php:590
msgid "Do you want the social media share buttons displayed?"
msgstr ""

#: wppa-settings-autosave.php:591
msgid "Display the social media buttons in the slideshow"
msgstr ""

#: wppa-settings-autosave.php:592
msgid ""
"These buttons share the specific photo rather than the page where it is "
"displayed on"
msgstr ""

#: wppa-settings-autosave.php:599
msgid "Are you going to use IPTC data?"
msgstr ""

#: wppa-settings-autosave.php:600
msgid ""
"IPTC data is information you may have added in a photo manipulation program."
msgstr ""

#: wppa-settings-autosave.php:601 wppa-settings-autosave.php:610
msgid ""
"The information can be displayed in slideshows and in photo descriptions."
msgstr ""

#: wppa-settings-autosave.php:608
msgid "Are you going to use EXIF data?"
msgstr ""

#: wppa-settings-autosave.php:609
msgid ""
"EXIF data is information from the camera like model no, focal distance and "
"aperture used."
msgstr ""

#: wppa-settings-autosave.php:617
msgid "Are you going to use GPX data?"
msgstr ""

#: wppa-settings-autosave.php:618
msgid ""
"Some cameras and mobile devices save the geographic location where the photo "
"is taken."
msgstr ""

#: wppa-settings-autosave.php:619
msgid "A Google map can be displayed in slideshows."
msgstr ""

#: wppa-settings-autosave.php:626
msgid "Are you going to use Fotomoto?"
msgstr ""

#: wppa-settings-autosave.php:627
msgid ""
"<a href=\"http://www.fotomoto.com/\" target=\"_blank\" >Fotomoto</a> is an "
"on-line print service."
msgstr ""

#: wppa-settings-autosave.php:628
msgid "If you answer Yes, you will have to open an account on Fotomoto."
msgstr ""

#: wppa-settings-autosave.php:635
msgid "Are you going to add videofiles?"
msgstr ""

#: wppa-settings-autosave.php:636
msgid "You can mix videos and photos in any album."
msgstr ""

#: wppa-settings-autosave.php:637 wppa-settings-autosave.php:646
#: wppa-settings-autosave.php:655 wppa-settings-autosave.php:664
msgid "You can configure the details later"
msgstr ""

#: wppa-settings-autosave.php:644
msgid "Are you going to add audiofiles?"
msgstr ""

#: wppa-settings-autosave.php:645
msgid "You can add audio to photos in any album."
msgstr ""

#: wppa-settings-autosave.php:653
msgid "Are you going to upload 3D stereo photos?"
msgstr ""

#: wppa-settings-autosave.php:654
msgid "You can add l-r and r-l stereo photo pairs."
msgstr ""

#: wppa-settings-autosave.php:662
msgid "Are you going to upload pdf files?"
msgstr ""

#: wppa-settings-autosave.php:663
msgid "You can add pdf files in any album."
msgstr ""

#: wppa-settings-autosave.php:671
msgid "Done?"
msgstr ""

#: wppa-settings-autosave.php:672
msgid "If you are ready answering these questions, select <b>yes</b>"
msgstr ""

#: wppa-settings-autosave.php:673
msgid ""
"You can change any setting later, and be more specific and add a lot of "
"settings. For now it is enough, go create albums and upload photos!"
msgstr ""

#: wppa-settings-autosave.php:677
msgid ""
"Thank you!. The most important settings are done now. You can refine your "
"settings, the behaviour and appearance of WPPA+ in the Tables below."
msgstr ""

#: wppa-settings-autosave.php:702
msgid "Table I:"
msgstr ""

#: wppa-settings-autosave.php:702
msgid "Sizes:"
msgstr ""

#: wppa-settings-autosave.php:703
msgid ""
"This table describes all the sizes and size options (except fontsizes) for "
"the generation and display of the WPPA+ elements."
msgstr ""

#: wppa-settings-autosave.php:721
msgid "WPPA+ global system related size settings"
msgstr ""

#: wppa-settings-autosave.php:723
msgid "Column Width"
msgstr ""

#: wppa-settings-autosave.php:724
msgid "The width of the main column in your theme's display area."
msgstr ""

#: wppa-settings-autosave.php:725
msgid "Enter the width of the main column in your theme's display area."
msgstr ""

#: wppa-settings-autosave.php:726
msgid ""
"You should set this value correctly to make sure the fullsize images are "
"properly aligned horizontally."
msgstr ""

#: wppa-settings-autosave.php:727
msgid ""
"You may enter 'auto' for use in themes that have a floating content column."
msgstr ""

#: wppa-settings-autosave.php:728
msgid "The use of 'auto' is required for responsive themes."
msgstr ""

#: wppa-settings-autosave.php:736
msgid "Initial Width"
msgstr ""

#: wppa-settings-autosave.php:737
msgid "The most often displayed colun width in responsive theme"
msgstr ""

#: wppa-settings-autosave.php:738
msgid ""
"Change this value only if your responsive theme shows initially a wrong "
"column width."
msgstr ""

#: wppa-settings-autosave.php:745
msgid "Resize on Upload"
msgstr ""

#: wppa-settings-autosave.php:746
msgid "Indicate if the photos should be resized during upload."
msgstr ""

#: wppa-settings-autosave.php:747
msgid ""
"If you check this item, the size of the photos will be reduced to the "
"dimension specified in the next item during the upload/import process."
msgstr ""

#: wppa-settings-autosave.php:748
msgid "The photos will never be stretched during upload if they are smaller."
msgstr ""

#: wppa-settings-autosave.php:756
msgid "Resize to"
msgstr ""

#: wppa-settings-autosave.php:757
msgid "Resize photos to fit within a given area."
msgstr ""

#: wppa-settings-autosave.php:758
msgid "Specify the screensize for the unscaled photos."
msgstr ""

#: wppa-settings-autosave.php:759
msgid ""
"The use of a non-default value is particularly usefull when you make use of "
"lightbox functionality."
msgstr ""

#: wppa-settings-autosave.php:761 wppa-settings-autosave.php:783
#: wppa-settings-autosave.php:794 wppa-settings-autosave.php:803
#: wppa-settings-autosave.php:848 wppa-settings-autosave.php:894
#: wppa-settings-autosave.php:917 wppa-settings-autosave.php:924
#: wppa-settings-autosave.php:943 wppa-settings-autosave.php:964
#: wppa-settings-autosave.php:974 wppa-settings-autosave.php:1068
#: wppa-settings-autosave.php:1102 wppa-settings-autosave.php:1122
#: wppa-settings-autosave.php:1131 wppa-settings-autosave.php:1142
#: wppa-settings-autosave.php:1154 wppa-settings-autosave.php:1175
#: wppa-settings-autosave.php:1195 wppa-settings-autosave.php:1258
#: wppa-settings-autosave.php:1267 wppa-settings-autosave.php:1278
#: wppa-settings-autosave.php:1298 wppa-settings-autosave.php:1318
#: wppa-settings-autosave.php:1338 wppa-settings-autosave.php:1358
#: wppa-settings-autosave.php:1378 wppa-settings-autosave.php:1398
#: wppa-settings-autosave.php:1407 wppa-settings-autosave.php:1416
#: wppa-settings-autosave.php:1449 wppa-settings-autosave.php:1458
#: wppa-settings-autosave.php:1469 wppa-settings-autosave.php:1481
#: wppa-settings-autosave.php:1490 wppa-settings-autosave.php:4975
#: wppa-settings-autosave.php:4992 wppa-settings-autosave.php:5009
#: wppa-settings-autosave.php:5026 wppa-settings-autosave.php:5043
#: wppa-settings-autosave.php:5060 wppa-settings-autosave.php:5077
#: wppa-settings-autosave.php:5094 wppa-settings-autosave.php:5111
#: wppa-settings-autosave.php:5129 wppa-settings-autosave.php:6514
#: wppa-settings-autosave.php:6525 wppa-settings-autosave.php:9555
#: wppa-settings-autosave.php:9760 wppa-settings-autosave.php:9811
#: wppa-settings-autosave.php:9829
msgid "pixels"
msgstr ""

#: wppa-settings-autosave.php:762
msgid "Fit within rectangle as set in Table I-B1,2"
msgstr ""

#: wppa-settings-autosave.php:769
msgid "Photocount threshold"
msgstr ""

#: wppa-settings-autosave.php:770
msgid "Number of photos in an album must exceed."
msgstr ""

#: wppa-settings-autosave.php:771
msgid ""
"Photos do not show up in the album unless there are more than this number of "
"photos in the album. This allows you to have cover photos on an album that "
"contains only sub albums without seeing them in the list of sub albums. "
"Usually set to 0 (always show) or 1 (for one cover photo)."
msgstr ""

#: wppa-settings-autosave.php:773 wppa-settings-autosave.php:812
#: wppa-settings-autosave.php:1287 wppa-settings-autosave.php:1327
#: wppa-settings-autosave.php:1347 wppa-settings-autosave.php:1387
#: wppa-settings-autosave.php:4303 wppa-settings-autosave.php:6415
#: wppa-settings-autosave.php:8521
msgid "photos"
msgstr "照片"

#: wppa-settings-autosave.php:778
msgid "Border thickness"
msgstr ""

#: wppa-settings-autosave.php:779
msgid "Thickness of wppa+ box borders."
msgstr ""

#: wppa-settings-autosave.php:780
msgid ""
"Enter the thickness for the border of the WPPA+ boxes. A number of 0 means: "
"no border."
msgstr ""

#: wppa-settings-autosave.php:781 wppa-settings-autosave.php:791
msgid "WPPA+ boxes are: the navigation bars and the filmstrip."
msgstr ""

#: wppa-settings-autosave.php:789
msgid "Radius of wppa+ box borders."
msgstr ""

#: wppa-settings-autosave.php:790
msgid ""
"Enter the corner radius for the border of the WPPA+ boxes. A number of 0 "
"means: no rounded corners."
msgstr ""

#: wppa-settings-autosave.php:792
msgid "Note that rounded corners are only supported by modern browsers."
msgstr ""

#: wppa-settings-autosave.php:800
msgid "Distance between wppa+ boxes."
msgstr ""

#: wppa-settings-autosave.php:808
msgid "Related count"
msgstr ""

#: wppa-settings-autosave.php:809
msgid "The default maximum number of related photos to find."
msgstr ""

#: wppa-settings-autosave.php:810
msgid ""
"When using shortcodes like [wppa type=\"album\" album=\"#related,desc,23\"][/"
"wppa], the maximum number is 23. Omitting the number gives the maximum of "
"this setting."
msgstr ""

#: wppa-settings-autosave.php:818
msgid "The maximum number of pagelinks to be displayed."
msgstr ""

#: wppa-settings-autosave.php:821
msgid "pages"
msgstr ""

#: wppa-settings-autosave.php:826
msgid "Max file name length"
msgstr ""

#: wppa-settings-autosave.php:827
msgid "The max length of a photo file name excluding the extension."
msgstr ""

#: wppa-settings-autosave.php:828 wppa-settings-autosave.php:837
msgid "A setting of 0 means: unlimited."
msgstr ""

#: wppa-settings-autosave.php:830 wppa-settings-autosave.php:839
msgid "chars"
msgstr ""

#: wppa-settings-autosave.php:835
msgid "Max photo name length"
msgstr ""

#: wppa-settings-autosave.php:836
msgid "The max length of a photo name."
msgstr ""

#: wppa-settings-autosave.php:845
msgid "The height of your sticky header."
msgstr ""

#: wppa-settings-autosave.php:846
msgid "If your theme has a sticky header, enter its height here."
msgstr ""

#: wppa-settings-autosave.php:854
msgid "Slideshow related size settings"
msgstr ""

#: wppa-settings-autosave.php:856
msgid "Maximum Width"
msgstr ""

#: wppa-settings-autosave.php:857
msgid "The maximum width photos will be displayed in slideshows."
msgstr ""

#: wppa-settings-autosave.php:858 wppa-settings-autosave.php:869
msgid ""
"Enter the largest size in pixels as how you want your photos to be displayed."
msgstr ""

#: wppa-settings-autosave.php:859
msgid ""
"This is usually the same as the Column Width (Table I-A1), but it may differ."
msgstr ""

#: wppa-settings-autosave.php:867
msgid "Maximum Height"
msgstr ""

#: wppa-settings-autosave.php:868
msgid "The maximum height photos will be displayed in slideshows."
msgstr ""

#: wppa-settings-autosave.php:870
msgid ""
"This setting defines the height of the space reserved for photos in "
"slideshows."
msgstr ""

#: wppa-settings-autosave.php:871
msgid ""
"If you change the width of a display by the size=\"..\" shortcode attribute, "
"this value changes proportionally to match the aspect ratio as defined by "
"this and the previous setting."
msgstr ""

#: wppa-settings-autosave.php:873 wppa-settings-autosave.php:1046
#: wppa-settings-autosave.php:1057
msgid "pixels high"
msgstr ""

#: wppa-settings-autosave.php:878
msgid "Stretch to fit"
msgstr ""

#: wppa-settings-autosave.php:879
msgid "Stretch photos that are too small."
msgstr ""

#: wppa-settings-autosave.php:880
msgid ""
"Images will be stretched to the Maximum Size at display time if they are "
"smaller. Leaving unchecked is recommended. It is better to upload photos "
"that fit well the sizes you use!"
msgstr ""

#: wppa-settings-autosave.php:887
msgid "Slideshow borderwidth"
msgstr ""

#: wppa-settings-autosave.php:888
msgid "The width of the border around slideshow images."
msgstr ""

#: wppa-settings-autosave.php:889
msgid ""
"The border is made by the image background being larger than the image "
"itsself (padding)."
msgstr ""

#: wppa-settings-autosave.php:890
msgid ""
"Additionally there may be a one pixel outline of a different color. See "
"Table III-A2."
msgstr ""

#: wppa-settings-autosave.php:891
msgid "The number you enter here is exclusive the one pixel outline."
msgstr ""

#: wppa-settings-autosave.php:892
msgid "If you leave this entry empty, there will be no outline either."
msgstr ""

#: wppa-settings-autosave.php:899
msgid "Numbar Max"
msgstr ""

#: wppa-settings-autosave.php:900
msgid "Maximum numbers to display."
msgstr ""

#: wppa-settings-autosave.php:901
msgid ""
"In order to attempt to fit on one line, the numbers will be replaced by dots "
"- except the current - when there are more than this number of photos in a "
"slideshow."
msgstr ""

#: wppa-settings-autosave.php:903
msgid "numbers"
msgstr ""

#: wppa-settings-autosave.php:908
msgid "Share button size"
msgstr ""

#: wppa-settings-autosave.php:909
msgid "The size of the social media icons in the Share box"
msgstr ""

#: wppa-settings-autosave.php:919
msgid "Mini Threshold"
msgstr ""

#: wppa-settings-autosave.php:920
msgid "Show mini text at slideshow smaller than."
msgstr ""

#: wppa-settings-autosave.php:921
msgid ""
"Display Next and Prev. as opposed to Next photo and Previous photo when the "
"cotainer is smaller than this size."
msgstr ""

#: wppa-settings-autosave.php:922
msgid "Special use in responsive themes."
msgstr ""

#: wppa-settings-autosave.php:930
msgid "The maximum number of slides in a certain view. 0 means no pagination"
msgstr ""

#: wppa-settings-autosave.php:933 wppa-settings-autosave.php:952
msgid "slides"
msgstr ""

#: wppa-settings-autosave.php:938
msgid "Filmstrip Thumbnail Size"
msgstr ""

#: wppa-settings-autosave.php:939
msgid "The size of the filmstrip images."
msgstr ""

#: wppa-settings-autosave.php:940 wppa-settings-autosave.php:961
#: wppa-settings-autosave.php:971 wppa-settings-autosave.php:1172
#: wppa-settings-autosave.php:1182 wppa-settings-autosave.php:1192
#: wppa-settings-autosave.php:1202
msgid "This size applies to the width or height, whichever is the largest."
msgstr ""

#: wppa-settings-autosave.php:941 wppa-settings-autosave.php:962
#: wppa-settings-autosave.php:972
msgid ""
"Changing the thumbnail size may result in all thumbnails being regenerated. "
"this may take a while."
msgstr ""

#: wppa-settings-autosave.php:949
msgid "The max number of slides in a slideonly display"
msgstr ""

#: wppa-settings-autosave.php:957
msgid "Thumbnail photos related size settings"
msgstr ""

#: wppa-settings-autosave.php:959
msgid "Thumbnail Size"
msgstr ""

#: wppa-settings-autosave.php:960
msgid "The size of the thumbnail images."
msgstr ""

#: wppa-settings-autosave.php:969
msgid "Thumbnail Size Alt"
msgstr ""

#: wppa-settings-autosave.php:970
msgid "The alternative size of the thumbnail images."
msgstr ""

#: wppa-settings-autosave.php:979
msgid "Thumbnail Aspect"
msgstr ""

#: wppa-settings-autosave.php:980
msgid "Aspect ration of thumbnail image"
msgstr ""

#: wppa-settings-autosave.php:984
msgid "--- same as fullsize ---"
msgstr ""

#: wppa-settings-autosave.php:985
msgid "--- square clipped ---"
msgstr ""

#: wppa-settings-autosave.php:986
msgid "4:5 landscape clipped"
msgstr ""

#: wppa-settings-autosave.php:987
msgid "3:4 landscape clipped"
msgstr ""

#: wppa-settings-autosave.php:988
msgid "2:3 landscape clipped"
msgstr ""

#: wppa-settings-autosave.php:989
msgid "9:16 landscape clipped"
msgstr ""

#: wppa-settings-autosave.php:990
msgid "1:2 landscape clipped"
msgstr ""

#: wppa-settings-autosave.php:991
msgid "--- square padded ---"
msgstr ""

#: wppa-settings-autosave.php:992
msgid "4:5 landscape padded"
msgstr ""

#: wppa-settings-autosave.php:993
msgid "3:4 landscape padded"
msgstr ""

#: wppa-settings-autosave.php:994
msgid "2:3 landscape padded"
msgstr ""

#: wppa-settings-autosave.php:995
msgid "9:16 landscape padded"
msgstr ""

#: wppa-settings-autosave.php:996
msgid "1:2 landscape padded"
msgstr ""

#: wppa-settings-autosave.php:1018
msgid "Thumbframe width"
msgstr ""

#: wppa-settings-autosave.php:1019
msgid "The width of the thumbnail frame."
msgstr ""

#: wppa-settings-autosave.php:1020 wppa-settings-autosave.php:1031
msgid "Set the width of the thumbnail frame."
msgstr ""

#: wppa-settings-autosave.php:1021 wppa-settings-autosave.php:1032
#: wppa-settings-autosave.php:1043 wppa-settings-autosave.php:1054
#: wppa-settings-autosave.php:1065
msgid "Set width, height and spacing for the thumbnail frames."
msgstr ""

#: wppa-settings-autosave.php:1022 wppa-settings-autosave.php:1033
#: wppa-settings-autosave.php:1044 wppa-settings-autosave.php:1055
#: wppa-settings-autosave.php:1066
msgid ""
"These sizes should be large enough for a thumbnail image and - optionally - "
"the text under it."
msgstr ""

#: wppa-settings-autosave.php:1029
msgid "Thumbframe width Alt"
msgstr ""

#: wppa-settings-autosave.php:1030
msgid "The width of the alternative thumbnail frame."
msgstr ""

#: wppa-settings-autosave.php:1040
msgid "Thumbframe height"
msgstr ""

#: wppa-settings-autosave.php:1041
msgid "The height of the thumbnail frame."
msgstr ""

#: wppa-settings-autosave.php:1042 wppa-settings-autosave.php:1053
msgid "Set the height of the thumbnail frame."
msgstr ""

#: wppa-settings-autosave.php:1051
msgid "Thumbframe height Alt"
msgstr ""

#: wppa-settings-autosave.php:1052
msgid "The height of the alternative thumbnail frame."
msgstr ""

#: wppa-settings-autosave.php:1062
msgid "Thumbnail spacing"
msgstr ""

#: wppa-settings-autosave.php:1063
msgid "The spacing between adjacent thumbnail frames."
msgstr ""

#: wppa-settings-autosave.php:1064
msgid "Set the minimal spacing between the adjacent thumbnail frames"
msgstr ""

#: wppa-settings-autosave.php:1073
msgid "Auto spacing"
msgstr ""

#: wppa-settings-autosave.php:1074
msgid "Space the thumbnail frames automatic."
msgstr ""

#: wppa-settings-autosave.php:1075
msgid ""
"If you check this box, the thumbnail images will be evenly distributed over "
"the available width."
msgstr ""

#: wppa-settings-autosave.php:1076
msgid ""
"In this case, the thumbnail spacing value (setting I-9) will be regarded as "
"a minimum value."
msgstr ""

#: wppa-settings-autosave.php:1083 wppa-settings-autosave.php:1221
msgid "Page size"
msgstr ""

#: wppa-settings-autosave.php:1084
msgid "Max number of thumbnails per page."
msgstr ""

#: wppa-settings-autosave.php:1085
msgid ""
"Enter the maximum number of thumbnail images per page. A value of 0 "
"indicates no pagination."
msgstr ""

#: wppa-settings-autosave.php:1093
msgid "The size of the thumbnail popup images."
msgstr ""

#: wppa-settings-autosave.php:1094
msgid ""
"Enter the size of the popup images. This size should be larger than the "
"thumbnail size."
msgstr ""

#: wppa-settings-autosave.php:1095
msgid "This size should also be at least the cover image size."
msgstr ""

#: wppa-settings-autosave.php:1096
msgid ""
"Changing the popup size may result in all thumbnails being regenerated. this "
"may take a while."
msgstr ""

#: wppa-settings-autosave.php:1097
msgid ""
"Although this setting has only visual effect if \"Thumb popup\" (Table IV-"
"C8) is checked,"
msgstr ""

#: wppa-settings-autosave.php:1098
msgid ""
"the value must be right as it is the physical size of the thumbnail and "
"coverphoto images."
msgstr ""

#: wppa-settings-autosave.php:1105
msgid "Use thumbs if fit"
msgstr ""

#: wppa-settings-autosave.php:1106
msgid "Use the thumbnail image files if they are large enough."
msgstr ""

#: wppa-settings-autosave.php:1107
msgid "This setting speeds up page loading for small photos."
msgstr ""

#: wppa-settings-autosave.php:1108
msgid ""
"Do NOT use this when your thumbnails have a forced aspect ratio (when Table "
"I-C2 is set to anything different from --- same as fullsize ---)"
msgstr ""

#: wppa-settings-autosave.php:1115
msgid "Album cover related size settings"
msgstr ""

#: wppa-settings-autosave.php:1118
msgid "Maximum width for a album cover display."
msgstr ""

#: wppa-settings-autosave.php:1119
msgid ""
"Display covers in 2 or more columns if the display area is wider than the "
"given width."
msgstr ""

#: wppa-settings-autosave.php:1120
msgid ""
"This also applies for 'thumbnails as covers', and will NOT apply to single "
"items."
msgstr ""

#: wppa-settings-autosave.php:1127
msgid "Min Cover height"
msgstr ""

#: wppa-settings-autosave.php:1128
msgid "Minimal height of an album cover."
msgstr ""

#: wppa-settings-autosave.php:1129
msgid ""
"If you use this setting to make the albums the same height and you are not "
"satisfied about the lay-out, try increasing the value in the next setting"
msgstr ""

#: wppa-settings-autosave.php:1136
msgid "Min Text frame height"
msgstr ""

#: wppa-settings-autosave.php:1137
msgid "The minimal cover text frame height incl header."
msgstr ""

#: wppa-settings-autosave.php:1138
msgid ""
"The height starting with the album title up to and including the view- and "
"the slideshow- links."
msgstr ""

#: wppa-settings-autosave.php:1139
msgid ""
"This setting enables you to give the album covers the same height while the "
"title does not need to fit on one line."
msgstr ""

#: wppa-settings-autosave.php:1140
msgid "This is the recommended setting to line-up your covers!"
msgstr ""

#: wppa-settings-autosave.php:1147
msgid "Min Description height"
msgstr ""

#: wppa-settings-autosave.php:1148
msgid "The minimal height of the album description text frame."
msgstr ""

#: wppa-settings-autosave.php:1149
msgid "The minimal height of the description field in an album cover display."
msgstr ""

#: wppa-settings-autosave.php:1150
msgid ""
"This setting enables you to give the album covers the same height provided "
"that the cover images are equally sized and the titles fit on one line."
msgstr ""

#: wppa-settings-autosave.php:1151
msgid ""
"To force the coverphotos have equal heights, tick the box in Table I-D7."
msgstr ""

#: wppa-settings-autosave.php:1152
msgid ""
"You may need this setting if changing the previous setting is not sufficient "
"to line-up the covers."
msgstr ""

#: wppa-settings-autosave.php:1159
msgid "Coverphoto responsive"
msgstr ""

#: wppa-settings-autosave.php:1160
msgid "Check this box if you want a responsive coverphoto."
msgstr ""

#: wppa-settings-autosave.php:1170 wppa-settings-autosave.php:1180
msgid "Coverphoto size"
msgstr ""

#: wppa-settings-autosave.php:1171 wppa-settings-autosave.php:1181
msgid "The size of the coverphoto."
msgstr ""

#: wppa-settings-autosave.php:1173 wppa-settings-autosave.php:1183
#: wppa-settings-autosave.php:1193 wppa-settings-autosave.php:1203
msgid ""
"Changing the coverphoto size may result in all thumbnails being regenerated. "
"this may take a while."
msgstr ""

#: wppa-settings-autosave.php:1185 wppa-settings-autosave.php:1205
msgid "percent"
msgstr ""

#: wppa-settings-autosave.php:1190 wppa-settings-autosave.php:1200
msgid "Coverphoto size multi"
msgstr ""

#: wppa-settings-autosave.php:1191 wppa-settings-autosave.php:1201
msgid "The size of coverphotos if more than one."
msgstr ""

#: wppa-settings-autosave.php:1210
msgid "Size is height"
msgstr ""

#: wppa-settings-autosave.php:1211
msgid "The size of the coverphoto is the height of it."
msgstr ""

#: wppa-settings-autosave.php:1212
msgid ""
"If set: the previous setting is the height, if unset: the largest of width "
"and height."
msgstr ""

#: wppa-settings-autosave.php:1213
msgid ""
"This setting applies for coverphoto position top or bottom only (Table IV-"
"D3)."
msgstr ""

#: wppa-settings-autosave.php:1214
msgid "This makes it easyer to make the covers of equal height."
msgstr ""

#: wppa-settings-autosave.php:1222
msgid "Max number of covers per page."
msgstr ""

#: wppa-settings-autosave.php:1223
msgid ""
"Enter the maximum number of album covers per page. A value of 0 indicates no "
"pagination."
msgstr ""

#: wppa-settings-autosave.php:1225
msgid "covers"
msgstr ""

#: wppa-settings-autosave.php:1230
msgid "Rating and comment related size settings"
msgstr ""

#: wppa-settings-autosave.php:1232
msgid "Rating size"
msgstr ""

#: wppa-settings-autosave.php:1233
msgid "Select the number of voting stars."
msgstr ""

#: wppa-settings-autosave.php:1236
msgid "Standard: 5 stars"
msgstr ""

#: wppa-settings-autosave.php:1236
msgid "Extended: 10 stars"
msgstr ""

#: wppa-settings-autosave.php:1236
msgid "One button vote"
msgstr ""

#: wppa-settings-autosave.php:1243
msgid "Display precision"
msgstr ""

#: wppa-settings-autosave.php:1244
msgid "Select the desired rating display precision."
msgstr ""

#: wppa-settings-autosave.php:1247
msgid "decimal places"
msgstr ""

#: wppa-settings-autosave.php:1255
msgid "Size of Avatar images."
msgstr ""

#: wppa-settings-autosave.php:1256
msgid "The size of the square avatar; must be > 0 and < 256"
msgstr ""

#: wppa-settings-autosave.php:1263
msgid "Rating space"
msgstr ""

#: wppa-settings-autosave.php:1264
msgid "Space between avg and my rating stars"
msgstr ""

#: wppa-settings-autosave.php:1272
msgid "Widget related size settings"
msgstr ""

#: wppa-settings-autosave.php:1274
msgid "Widget width"
msgstr ""

#: wppa-settings-autosave.php:1275
msgid "The useable width within widgets."
msgstr ""

#: wppa-settings-autosave.php:1276
msgid ""
"Widget width for photo of the day, general purpose (default), slideshow "
"(default) and upload widgets."
msgstr ""

#: wppa-settings-autosave.php:1283
msgid "TopTen count"
msgstr ""

#: wppa-settings-autosave.php:1284
msgid "Number of photos in TopTen widget."
msgstr ""

#: wppa-settings-autosave.php:1285
msgid "Enter the maximum number of rated photos in the TopTen widget."
msgstr ""

#: wppa-settings-autosave.php:1292
msgid "TopTen size"
msgstr ""

#: wppa-settings-autosave.php:1293
msgid "Size of thumbnails in TopTen widget."
msgstr ""

#: wppa-settings-autosave.php:1294
msgid "Enter the size for the mini photos in the TopTen widget."
msgstr ""

#: wppa-settings-autosave.php:1295 wppa-settings-autosave.php:1315
#: wppa-settings-autosave.php:1335 wppa-settings-autosave.php:1355
#: wppa-settings-autosave.php:1375 wppa-settings-autosave.php:1395
msgid "The size applies to the width or height, whatever is the largest."
msgstr ""

#: wppa-settings-autosave.php:1296 wppa-settings-autosave.php:1316
#: wppa-settings-autosave.php:1336 wppa-settings-autosave.php:1356
#: wppa-settings-autosave.php:1376 wppa-settings-autosave.php:1396
msgid ""
"Recommended values: 86 for a two column and 56 for a three column display."
msgstr ""

#: wppa-settings-autosave.php:1303
msgid "Comment count"
msgstr ""

#: wppa-settings-autosave.php:1304
msgid "Number of entries in Comment widget."
msgstr ""

#: wppa-settings-autosave.php:1305
msgid "Enter the maximum number of entries in the Comment widget."
msgstr ""

#: wppa-settings-autosave.php:1307
msgid "entries"
msgstr ""

#: wppa-settings-autosave.php:1312
msgid "Comment size"
msgstr ""

#: wppa-settings-autosave.php:1313
msgid "Size of thumbnails in Comment widget."
msgstr ""

#: wppa-settings-autosave.php:1314
msgid "Enter the size for the mini photos in the Comment widget."
msgstr ""

#: wppa-settings-autosave.php:1323
msgid "Thumbnail count"
msgstr ""

#: wppa-settings-autosave.php:1324
msgid "Number of photos in Thumbnail widget."
msgstr ""

#: wppa-settings-autosave.php:1325
msgid "Enter the maximum number of rated photos in the Thumbnail widget."
msgstr ""

#: wppa-settings-autosave.php:1332
msgid "Thumbnail widget size"
msgstr ""

#: wppa-settings-autosave.php:1333
msgid "Size of thumbnails in Thumbnail widget."
msgstr ""

#: wppa-settings-autosave.php:1334
msgid "Enter the size for the mini photos in the Thumbnail widget."
msgstr ""

#: wppa-settings-autosave.php:1343
msgid "LasTen count"
msgstr ""

#: wppa-settings-autosave.php:1344
msgid "Number of photos in Last Ten widget."
msgstr ""

#: wppa-settings-autosave.php:1345
msgid "Enter the maximum number of photos in the LasTen widget."
msgstr ""

#: wppa-settings-autosave.php:1352
msgid "LasTen size"
msgstr ""

#: wppa-settings-autosave.php:1353
msgid "Size of thumbnails in Last Ten widget."
msgstr ""

#: wppa-settings-autosave.php:1354
msgid "Enter the size for the mini photos in the LasTen widget."
msgstr ""

#: wppa-settings-autosave.php:1363
msgid "Album widget count"
msgstr ""

#: wppa-settings-autosave.php:1364
msgid "Number of albums in Album widget."
msgstr ""

#: wppa-settings-autosave.php:1365
msgid ""
"Enter the maximum number of thumbnail photos of albums in the Album widget."
msgstr ""

#: wppa-settings-autosave.php:1367 wppa-settings-autosave.php:6433
#: wppa-settings-autosave.php:6570
msgid "albums"
msgstr "相簿"

#: wppa-settings-autosave.php:1372
msgid "Album widget size"
msgstr ""

#: wppa-settings-autosave.php:1373
msgid "Size of thumbnails in Album widget."
msgstr ""

#: wppa-settings-autosave.php:1374
msgid "Enter the size for the mini photos in the Album widget."
msgstr ""

#: wppa-settings-autosave.php:1383
msgid "FeaTen count"
msgstr ""

#: wppa-settings-autosave.php:1384
msgid "Number of photos in Featured Ten widget."
msgstr ""

#: wppa-settings-autosave.php:1385
msgid "Enter the maximum number of photos in the FeaTen widget."
msgstr ""

#: wppa-settings-autosave.php:1392
msgid "FeaTen size"
msgstr ""

#: wppa-settings-autosave.php:1393
msgid "Size of thumbnails in Featured Ten widget."
msgstr ""

#: wppa-settings-autosave.php:1394
msgid "Enter the size for the mini photos in the FeaTen widget."
msgstr ""

#: wppa-settings-autosave.php:1403
msgid "Tagcloud min size"
msgstr ""

#: wppa-settings-autosave.php:1404
msgid "Minimal fontsize in tagclouds"
msgstr ""

#: wppa-settings-autosave.php:1412
msgid "Tagcloud max size"
msgstr ""

#: wppa-settings-autosave.php:1413
msgid "Maximal fontsize in tagclouds"
msgstr ""

#: wppa-settings-autosave.php:1421
msgid ""
"Lightbox related size settings. These settings have effect only when Table "
"IX-J3 is set to wppa"
msgstr ""

#: wppa-settings-autosave.php:1424
msgid ""
"Number of lines on the lightbox description area, exclusive the n/m line."
msgstr ""

#: wppa-settings-autosave.php:1425
msgid "Enter a number in the range from 0 to 24 or auto"
msgstr ""

#: wppa-settings-autosave.php:1427
msgid "lines"
msgstr ""

#: wppa-settings-autosave.php:1432
msgid "Magnifier cursor size"
msgstr ""

#: wppa-settings-autosave.php:1433
msgid "Select the size of the magnifier cursor."
msgstr ""

#: wppa-settings-autosave.php:1436
msgid "small"
msgstr ""

#: wppa-settings-autosave.php:1436 wppa-settings-autosave.php:3074
msgid "medium"
msgstr ""

#: wppa-settings-autosave.php:1436
msgid "large"
msgstr ""

#: wppa-settings-autosave.php:1446
msgid "Border width for lightbox display."
msgstr ""

#: wppa-settings-autosave.php:1455
msgid "Border radius for lightbox display."
msgstr ""

#: wppa-settings-autosave.php:1463
msgid "Fullscreen button size"
msgstr ""

#: wppa-settings-autosave.php:1464
msgid "Fullscreen and exit button size"
msgstr ""

#: wppa-settings-autosave.php:1475
msgid "Video related size settings"
msgstr ""

#: wppa-settings-autosave.php:1477
msgid "Default width"
msgstr ""

#: wppa-settings-autosave.php:1478
msgid "The width of most videos"
msgstr ""

#: wppa-settings-autosave.php:1486
msgid "Default height"
msgstr ""

#: wppa-settings-autosave.php:1487
msgid "The height of most videos"
msgstr ""

#: wppa-settings-autosave.php:1513
msgid "Table II:"
msgstr ""

#: wppa-settings-autosave.php:1513
msgid "Visibility:"
msgstr ""

#: wppa-settings-autosave.php:1514
msgid "This table describes the visibility of certain wppa+ elements."
msgstr ""

#: wppa-settings-autosave.php:1532
msgid "Breadcrumb related visibility settings"
msgstr ""

#: wppa-settings-autosave.php:1534
msgid "Breadcrumb on posts"
msgstr ""

#: wppa-settings-autosave.php:1535 wppa-settings-autosave.php:1545
msgid "Show breadcrumb navigation bars."
msgstr ""

#: wppa-settings-autosave.php:1536 wppa-settings-autosave.php:1546
msgid "Indicate whether a breadcrumb navigation should be displayed"
msgstr ""

#: wppa-settings-autosave.php:1544
msgid "Breadcrumb on pages"
msgstr ""

#: wppa-settings-autosave.php:1554
msgid "Breadcrumb on search results"
msgstr ""

#: wppa-settings-autosave.php:1555
msgid "Show breadcrumb navigation bars on the search results page."
msgstr ""

#: wppa-settings-autosave.php:1556
msgid ""
"Indicate whether a breadcrumb navigation should be displayed above the "
"search results."
msgstr ""

#: wppa-settings-autosave.php:1563
msgid "Breadcrumb on topten displays"
msgstr ""

#: wppa-settings-autosave.php:1564
msgid "Show breadcrumb navigation bars on topten displays."
msgstr ""

#: wppa-settings-autosave.php:1565
msgid ""
"Indicate whether a breadcrumb navigation should be displayed above the "
"topten displays."
msgstr ""

#: wppa-settings-autosave.php:1572
msgid "Breadcrumb on last ten displays"
msgstr ""

#: wppa-settings-autosave.php:1573
msgid "Show breadcrumb navigation bars on last ten displays."
msgstr ""

#: wppa-settings-autosave.php:1574
msgid ""
"Indicate whether a breadcrumb navigation should be displayed above the last "
"ten displays."
msgstr ""

#: wppa-settings-autosave.php:1581
msgid "Breadcrumb on comment ten displays"
msgstr ""

#: wppa-settings-autosave.php:1582
msgid "Show breadcrumb navigation bars on comment ten displays."
msgstr ""

#: wppa-settings-autosave.php:1583
msgid ""
"Indicate whether a breadcrumb navigation should be displayed above the "
"comment ten displays."
msgstr ""

#: wppa-settings-autosave.php:1590
msgid "Breadcrumb on tag result displays"
msgstr ""

#: wppa-settings-autosave.php:1591
msgid "Show breadcrumb navigation bars on tag result displays."
msgstr ""

#: wppa-settings-autosave.php:1592
msgid ""
"Indicate whether a breadcrumb navigation should be displayed above the tag "
"result displays."
msgstr ""

#: wppa-settings-autosave.php:1599
msgid "Breadcrumb on featured ten displays"
msgstr ""

#: wppa-settings-autosave.php:1600
msgid "Show breadcrumb navigation bars on featured ten displays."
msgstr ""

#: wppa-settings-autosave.php:1601
msgid ""
"Indicate whether a breadcrumb navigation should be displayed above the "
"featured ten displays."
msgstr ""

#: wppa-settings-autosave.php:1608
msgid "Breadcrumb on related photos displays"
msgstr ""

#: wppa-settings-autosave.php:1609
msgid "Show breadcrumb navigation bars on related photos displays."
msgstr ""

#: wppa-settings-autosave.php:1610
msgid ""
"Indicate whether a breadcrumb navigation should be displayed above the "
"related photos displays."
msgstr ""

#: wppa-settings-autosave.php:1617 wppa-setup.php:832
msgid "Home"
msgstr "首页"

#: wppa-settings-autosave.php:1618
msgid "Show \"Home\" in breadcrumb."
msgstr ""

#: wppa-settings-autosave.php:1619
msgid ""
"Indicate whether the breadcrumb navigation should start with a \"Home\"-link"
msgstr ""

#: wppa-settings-autosave.php:1626
msgid "Home text"
msgstr ""

#: wppa-settings-autosave.php:1627
msgid "The text to use as \"Home\""
msgstr ""

#: wppa-settings-autosave.php:1636
msgid "Show the page(s) in breadcrumb."
msgstr ""

#: wppa-settings-autosave.php:1637
msgid ""
"Indicate whether the breadcrumb navigation should show the page(hierarchy)"
msgstr ""

#: wppa-settings-autosave.php:1644
msgid "Separator"
msgstr ""

#: wppa-settings-autosave.php:1645
msgid "Breadcrumb separator symbol."
msgstr ""

#: wppa-settings-autosave.php:1646
msgid "Select the desired breadcrumb separator element."
msgstr ""

#: wppa-settings-autosave.php:1647
msgid "A text string may contain valid html."
msgstr ""

#: wppa-settings-autosave.php:1648
msgid ""
"An image will be scaled automatically if you set the navigation font size."
msgstr ""

#: wppa-settings-autosave.php:1650
msgid "Text (html):"
msgstr ""

#: wppa-settings-autosave.php:1650
msgid "Image (url):"
msgstr ""

#: wppa-settings-autosave.php:1658
msgid "Html"
msgstr ""

#: wppa-settings-autosave.php:1659
msgid "Breadcrumb separator text."
msgstr ""

#: wppa-settings-autosave.php:1660
msgid "Enter the HTML code that produces the separator symbol you want."
msgstr ""

#: wppa-settings-autosave.php:1661
msgid ""
"It may be as simple as '-' (without the quotes) or as complex as a tag like "
"<div>..</div>."
msgstr ""

#: wppa-settings-autosave.php:1668
msgid "Image Url"
msgstr ""

#: wppa-settings-autosave.php:1669
msgid "Full url to separator image."
msgstr ""

#: wppa-settings-autosave.php:1670
msgid ""
"Enter the full url to the image you want to use for the separator symbol."
msgstr ""

#: wppa-settings-autosave.php:1677
msgid "Pagelink position"
msgstr ""

#: wppa-settings-autosave.php:1678
msgid "The location for the pagelinks bar."
msgstr ""

#: wppa-settings-autosave.php:1681 wppa-settings-autosave.php:4248
msgid "Top"
msgstr ""

#: wppa-settings-autosave.php:1681 wppa-settings-autosave.php:4248
msgid "Bottom"
msgstr ""

#: wppa-settings-autosave.php:1681
msgid "Both"
msgstr ""

#: wppa-settings-autosave.php:1688
msgid "Thumblink on slideshow"
msgstr ""

#: wppa-settings-autosave.php:1689
msgid "Show a thumb link on slideshow bc."
msgstr ""

#: wppa-settings-autosave.php:1690
msgid "Show a link to thumbnail display on an breadcrumb above a slideshow"
msgstr ""

#: wppa-settings-autosave.php:1697
msgid "Slideshow related visibility settings"
msgstr ""

#: wppa-settings-autosave.php:1699
msgid "Navigation type"
msgstr ""

#: wppa-settings-autosave.php:1700
msgid "Select the type of navigation you want."
msgstr ""

#: wppa-settings-autosave.php:1703
msgid "Icons"
msgstr ""

#: wppa-settings-autosave.php:1704
msgid "Icons on mobile, text on pc"
msgstr ""

#: wppa-settings-autosave.php:1715
msgid "Start/stop"
msgstr ""

#: wppa-settings-autosave.php:1716
msgid "Show the Start/Stop slideshow bar."
msgstr ""

#: wppa-settings-autosave.php:1717
msgid ""
"If checked: display the start/stop slideshow navigation bar above the full-"
"size images and slideshow"
msgstr ""

#: wppa-settings-autosave.php:1724
msgid "Browse bar"
msgstr ""

#: wppa-settings-autosave.php:1725
msgid "Show Browse photos bar."
msgstr ""

#: wppa-settings-autosave.php:1726
msgid ""
"If checked: display the preveous/next navigation bar under the full-size "
"images and slideshow"
msgstr ""

#: wppa-settings-autosave.php:1733
msgid "Filmstrip"
msgstr ""

#: wppa-settings-autosave.php:1734
msgid "Show Filmstrip navigation bar."
msgstr ""

#: wppa-settings-autosave.php:1735
msgid ""
"If checked: display the filmstrip navigation bar under the full_size images "
"and slideshow"
msgstr ""

#: wppa-settings-autosave.php:1742
msgid "Film seam"
msgstr ""

#: wppa-settings-autosave.php:1743
msgid "Show seam between end and start of film."
msgstr ""

#: wppa-settings-autosave.php:1744
msgid "If checked: display the wrap-around point in the filmstrip"
msgstr ""

#: wppa-settings-autosave.php:1752
msgid "Display photo name."
msgstr ""

#: wppa-settings-autosave.php:1753
msgid "If checked: display the name of the photo under the slideshow image."
msgstr ""

#: wppa-settings-autosave.php:1760 wppa-settings-autosave.php:2165
msgid "Add (Owner)"
msgstr ""

#: wppa-settings-autosave.php:1761 wppa-settings-autosave.php:2166
msgid "Add the uploaders display name in parenthesis to the name."
msgstr ""

#: wppa-settings-autosave.php:1769
msgid "Photo desc"
msgstr ""

#: wppa-settings-autosave.php:1770
msgid "Display Photo description."
msgstr ""

#: wppa-settings-autosave.php:1771
msgid ""
"If checked: display the description of the photo under the slideshow image."
msgstr ""

#: wppa-settings-autosave.php:1778
msgid "Hide when empty"
msgstr ""

#: wppa-settings-autosave.php:1779
msgid "Hide the descriptionbox when empty."
msgstr ""

#: wppa-settings-autosave.php:1787
msgid "Rating system"
msgstr ""

#: wppa-settings-autosave.php:1788
msgid "Enable the rating system."
msgstr ""

#: wppa-settings-autosave.php:1789
msgid "If checked, the photo rating system will be enabled."
msgstr ""

#: wppa-settings-autosave.php:1797
msgid "Comments system"
msgstr ""

#: wppa-settings-autosave.php:1798
msgid "Enable the comments system."
msgstr ""

#: wppa-settings-autosave.php:1799
msgid ""
"Display the comments box under the fullsize images and let users enter their "
"comments on individual photos."
msgstr ""

#: wppa-settings-autosave.php:1807
msgid "Comment Avatar default"
msgstr ""

#: wppa-settings-autosave.php:1808
msgid "Show Avatars with the comments if not --- none ---"
msgstr ""

#: wppa-settings-autosave.php:1813
msgid "mystery man"
msgstr ""

#: wppa-settings-autosave.php:1814
msgid "identicon"
msgstr ""

#: wppa-settings-autosave.php:1815
msgid "monsterid"
msgstr ""

#: wppa-settings-autosave.php:1816
msgid "wavatar"
msgstr ""

#: wppa-settings-autosave.php:1817
msgid "retro"
msgstr ""

#: wppa-settings-autosave.php:1818
msgid "--- url ---"
msgstr ""

#: wppa-settings-autosave.php:1833
msgid "Comment Avatar url"
msgstr ""

#: wppa-settings-autosave.php:1834
msgid "Comment Avatar default url."
msgstr ""

#: wppa-settings-autosave.php:1842
msgid "Big Browse Buttons"
msgstr ""

#: wppa-settings-autosave.php:1843
msgid "Enable invisible browsing buttons."
msgstr ""

#: wppa-settings-autosave.php:1844 wppa-settings-autosave.php:2448
msgid ""
"If checked, the fullsize image is covered by two invisible areas that act as "
"browse buttons."
msgstr ""

#: wppa-settings-autosave.php:1845 wppa-settings-autosave.php:2449
#: wppa-settings-autosave.php:2459
msgid ""
"Make sure the Full height (Table I-B2) is properly configured to prevent "
"these areas to overlap unwanted space."
msgstr ""

#: wppa-settings-autosave.php:1852
msgid "Ugly Browse Buttons"
msgstr ""

#: wppa-settings-autosave.php:1853
msgid "Enable the ugly browsing buttons."
msgstr ""

#: wppa-settings-autosave.php:1854
msgid "If checked, the fullsize image is covered by two browse buttons."
msgstr ""

#: wppa-settings-autosave.php:1865
msgid "Start/stop icons"
msgstr ""

#: wppa-settings-autosave.php:1866
msgid "Show start and stop icons at the center of the slide"
msgstr ""

#: wppa-settings-autosave.php:1878
msgid "Show custom box"
msgstr ""

#: wppa-settings-autosave.php:1879
msgid "Display the custom box in the slideshow"
msgstr ""

#: wppa-settings-autosave.php:1880 wppa-settings-autosave.php:1891
msgid ""
"You can fill the custom box with any html you like. It will not be checked, "
"so it is your own responsibility to close tags properly."
msgstr ""

#: wppa-settings-autosave.php:1881 wppa-settings-autosave.php:1892
msgid "The position of the box can be defined in Table IX-E."
msgstr ""

#: wppa-settings-autosave.php:1889
msgid "Custom content"
msgstr ""

#: wppa-settings-autosave.php:1890
msgid "The content (html) of the custom box."
msgstr ""

#: wppa-settings-autosave.php:1899
msgid "Slideshow/Number bar"
msgstr ""

#: wppa-settings-autosave.php:1900
msgid "Display the Slideshow / Number bar."
msgstr ""

#: wppa-settings-autosave.php:1901
msgid "If checked: display the number boxes on slideshow"
msgstr ""

#: wppa-settings-autosave.php:1909
msgid "IPTC system"
msgstr ""

#: wppa-settings-autosave.php:1910
msgid "Enable the iptc system."
msgstr ""

#: wppa-settings-autosave.php:1911
msgid "Display the iptc box under the fullsize images."
msgstr ""

#: wppa-settings-autosave.php:1919
msgid "IPTC open"
msgstr ""

#: wppa-settings-autosave.php:1920
msgid "Display the iptc box initially opened."
msgstr ""

#: wppa-settings-autosave.php:1921
msgid "Display the iptc box under the fullsize images initially open."
msgstr ""

#: wppa-settings-autosave.php:1929
msgid "EXIF system"
msgstr ""

#: wppa-settings-autosave.php:1930
msgid "Enable the exif system."
msgstr ""

#: wppa-settings-autosave.php:1931
msgid "Display the exif box under the fullsize images."
msgstr ""

#: wppa-settings-autosave.php:1939
msgid "EXIF open"
msgstr ""

#: wppa-settings-autosave.php:1940
msgid "Display the exif box initially opened."
msgstr ""

#: wppa-settings-autosave.php:1941
msgid "Display the exif box under the fullsize images initially open."
msgstr ""

#: wppa-settings-autosave.php:1949
msgid "Social media share box related visibility settings"
msgstr ""

#: wppa-settings-autosave.php:1951
msgid "Show Share Box"
msgstr ""

#: wppa-settings-autosave.php:1952
msgid "Display the share social media buttons box."
msgstr ""

#: wppa-settings-autosave.php:1961 wppa-settings-autosave.php:9816
msgid "Hide when running"
msgstr ""

#: wppa-settings-autosave.php:1962
msgid "Hide the SM box when slideshow runs."
msgstr ""

#: wppa-settings-autosave.php:1970
msgid "Show Share Box Widget"
msgstr ""

#: wppa-settings-autosave.php:1971
msgid "Display the share social media buttons box in widgets."
msgstr ""

#: wppa-settings-autosave.php:1972
msgid ""
"This setting applies to normal slideshows in widgets, not to the "
"slideshowwidget as that is a slideonly display."
msgstr ""

#: wppa-settings-autosave.php:1980
msgid "Show Share Buttons Thumbs"
msgstr ""

#: wppa-settings-autosave.php:1981
msgid "Display the share social media buttons under thumbnails."
msgstr ""

#: wppa-settings-autosave.php:1990
msgid "Show Share Buttons Lightbox"
msgstr ""

#: wppa-settings-autosave.php:1991
msgid "Display the share social media buttons on lightbox displays."
msgstr ""

#: wppa-settings-autosave.php:2000
msgid "Show Share Buttons Mphoto"
msgstr ""

#: wppa-settings-autosave.php:2001
msgid "Display the share social media buttons on mphoto displays."
msgstr ""

#: wppa-settings-autosave.php:2010
msgid "Void pages share"
msgstr ""

#: wppa-settings-autosave.php:2011
msgid "Do not show share on these pages"
msgstr ""

#: wppa-settings-autosave.php:2012
msgid "Use this for pages that require the user is logged in"
msgstr ""

#: wppa-settings-autosave.php:2016
msgid "--- Select one or more pages ---"
msgstr ""

#: wppa-settings-autosave.php:2025
msgid "Show QR Code"
msgstr ""

#: wppa-settings-autosave.php:2026
msgid "Display the QR code in the share box."
msgstr ""

#: wppa-settings-autosave.php:2034
msgid "Show Twitter button"
msgstr ""

#: wppa-settings-autosave.php:2035
msgid "Display the Twitter button in the share box."
msgstr ""

#: wppa-settings-autosave.php:2043
msgid "The creator's Twitter account"
msgstr ""

#: wppa-settings-autosave.php:2044
msgid "The Twitter @username a twitter card should be attributed to."
msgstr ""

#: wppa-settings-autosave.php:2045
msgid ""
"If you want to share the image directly - by a so called twitter card - you "
"must enter your twitter account name here"
msgstr ""

#: wppa-settings-autosave.php:2052
msgid "Show Google+ button"
msgstr ""

#: wppa-settings-autosave.php:2053
msgid "Display the Google+ button in the share box."
msgstr ""

#: wppa-settings-autosave.php:2061
msgid "Show Pinterest button"
msgstr ""

#: wppa-settings-autosave.php:2062
msgid "Display the Pintrest button in the share box."
msgstr ""

#: wppa-settings-autosave.php:2070
msgid "Show LinkedIn button"
msgstr ""

#: wppa-settings-autosave.php:2071
msgid "Display the LinkedIn button in the share box."
msgstr ""

#: wppa-settings-autosave.php:2079
msgid "Show Facebook share button"
msgstr ""

#: wppa-settings-autosave.php:2080 wppa-settings-autosave.php:2089
msgid "Display the Facebook button in the share box."
msgstr ""

#: wppa-settings-autosave.php:2088
msgid "Show Facebook like button"
msgstr ""

#: wppa-settings-autosave.php:2098
msgid "Select the Facebook button display type."
msgstr ""

#: wppa-settings-autosave.php:2101
msgid "Button with counter"
msgstr ""

#: wppa-settings-autosave.php:2101
msgid "Box with counter"
msgstr ""

#: wppa-settings-autosave.php:2108
msgid "Show Facebook comment box"
msgstr ""

#: wppa-settings-autosave.php:2109
msgid "Display the Facebook comment dialog box in the share box."
msgstr ""

#: wppa-settings-autosave.php:2117
msgid "Facebook User Id"
msgstr ""

#: wppa-settings-autosave.php:2118
msgid "Enter your facebook user id to be able to moderate comments and sends"
msgstr ""

#: wppa-settings-autosave.php:2126
msgid "Facebook App Id"
msgstr ""

#: wppa-settings-autosave.php:2127
msgid "Enter your facebook app id to be able to moderate comments and sends"
msgstr ""

#: wppa-settings-autosave.php:2135
msgid "Facebook js SDK"
msgstr ""

#: wppa-settings-autosave.php:2136
msgid "Load Facebook js SDK"
msgstr ""

#: wppa-settings-autosave.php:2137
msgid ""
"Uncheck this box only when there is a conflict with an other plugin that "
"also loads the Facebook js SDK."
msgstr ""

#: wppa-settings-autosave.php:2144
msgid "Share single image"
msgstr ""

#: wppa-settings-autosave.php:2145
msgid "Share a link to a single image, not the slideshow."
msgstr ""

#: wppa-settings-autosave.php:2146
msgid ""
"The sharelink points to a page with a single image rather than to the page "
"with the photo in the slideshow."
msgstr ""

#: wppa-settings-autosave.php:2154
msgid "Thumbnail display related visibility settings"
msgstr ""

#: wppa-settings-autosave.php:2156
msgid "Thumbnail name"
msgstr ""

#: wppa-settings-autosave.php:2157
msgid "Display Thumbnail name."
msgstr ""

#: wppa-settings-autosave.php:2158
msgid "Display photo name under thumbnail images."
msgstr ""

#: wppa-settings-autosave.php:2174
msgid "Thumbnail desc"
msgstr ""

#: wppa-settings-autosave.php:2175
msgid "Display Thumbnail description."
msgstr ""

#: wppa-settings-autosave.php:2176
msgid "Display description of the photo under thumbnail images."
msgstr ""

#: wppa-settings-autosave.php:2183
msgid "Thumbnail rating"
msgstr ""

#: wppa-settings-autosave.php:2184
msgid "Display Thumbnail Rating."
msgstr ""

#: wppa-settings-autosave.php:2185
msgid "Display the rating of the photo under the thumbnail image."
msgstr ""

#: wppa-settings-autosave.php:2192
msgid "Thumbnail comcount"
msgstr ""

#: wppa-settings-autosave.php:2193
msgid "Display Thumbnail Comment count."
msgstr ""

#: wppa-settings-autosave.php:2194
msgid "Display the number of comments to the photo under the thumbnail image."
msgstr ""

#: wppa-settings-autosave.php:2201
msgid "Thumbnail viewcount"
msgstr ""

#: wppa-settings-autosave.php:2202
msgid "Display the number of views."
msgstr ""

#: wppa-settings-autosave.php:2203
msgid "Display the number of views under the thumbnail image."
msgstr ""

#: wppa-settings-autosave.php:2210
msgid "Thumbnail virt album"
msgstr ""

#: wppa-settings-autosave.php:2211
msgid "Display the real album name on virtual album display."
msgstr ""

#: wppa-settings-autosave.php:2212
msgid ""
"Display the album name of the photo in parenthesis under the thumbnail on "
"virtual album displays like search results etc."
msgstr ""

#: wppa-settings-autosave.php:2219
msgid "Thumbnail video"
msgstr ""

#: wppa-settings-autosave.php:2220
msgid "Show video controls on thumbnail displays."
msgstr ""

#: wppa-settings-autosave.php:2221
msgid ""
"Works on default thumbnail type only. You can play the video only when the "
"link is set to no link at all."
msgstr ""

#: wppa-settings-autosave.php:2228
msgid "Thumbnail audio"
msgstr ""

#: wppa-settings-autosave.php:2229
msgid "Show audio controls on thumbnail displays."
msgstr ""

#: wppa-settings-autosave.php:2237
msgid "Popup name"
msgstr ""

#: wppa-settings-autosave.php:2238
msgid "Display Thumbnail name on popup."
msgstr ""

#: wppa-settings-autosave.php:2239
msgid "Display photo name under thumbnail images on the popup."
msgstr ""

#: wppa-settings-autosave.php:2246
msgid "Popup (owner)"
msgstr ""

#: wppa-settings-autosave.php:2247
msgid "Display owner on popup."
msgstr ""

#: wppa-settings-autosave.php:2248
msgid "Display photo owner under thumbnail images on the popup."
msgstr ""

#: wppa-settings-autosave.php:2255
msgid "Popup desc"
msgstr ""

#: wppa-settings-autosave.php:2256
msgid "Display Thumbnail description on popup."
msgstr ""

#: wppa-settings-autosave.php:2257
msgid "Display description of the photo under thumbnail images on the popup."
msgstr ""

#: wppa-settings-autosave.php:2264
msgid "Popup desc no links"
msgstr ""

#: wppa-settings-autosave.php:2265
msgid "Strip html anchor tags from descriptions on popups"
msgstr ""

#: wppa-settings-autosave.php:2266
msgid ""
"Use this option to prevent the display of links that cannot be activated."
msgstr ""

#: wppa-settings-autosave.php:2273
msgid "Popup rating"
msgstr ""

#: wppa-settings-autosave.php:2274
msgid "Display Thumbnail Rating on popup."
msgstr ""

#: wppa-settings-autosave.php:2275
msgid "Display the rating of the photo under the thumbnail image on the popup."
msgstr ""

#: wppa-settings-autosave.php:2282
msgid "Popup comcount"
msgstr ""

#: wppa-settings-autosave.php:2283
msgid "Display Thumbnail Comment count on popup."
msgstr ""

#: wppa-settings-autosave.php:2284
msgid ""
"Display the number of comments of the photo under the thumbnail image on the "
"popup."
msgstr ""

#: wppa-settings-autosave.php:2291
msgid "Show rating count"
msgstr ""

#: wppa-settings-autosave.php:2292
msgid "Display the number of votes along with average ratings."
msgstr ""

#: wppa-settings-autosave.php:2293
msgid ""
"If checked, the number of votes is displayed along with average rating "
"displays on thumbnail and popup displays."
msgstr ""

#: wppa-settings-autosave.php:2300
msgid "Show name on thumb area"
msgstr ""

#: wppa-settings-autosave.php:2301
msgid "Select if and where to display the album name on the thumbnail display."
msgstr ""

#: wppa-settings-autosave.php:2304 wppa-settings-autosave.php:2315
#: wppa-settings-autosave.php:2344 wppa-settings-autosave.php:4641
msgid "None"
msgstr ""

#: wppa-settings-autosave.php:2304 wppa-settings-autosave.php:2315
#: wppa-settings-autosave.php:2344 wppa-settings-autosave.php:3683
msgid "At the top"
msgstr ""

#: wppa-settings-autosave.php:2304 wppa-settings-autosave.php:2315
#: wppa-settings-autosave.php:2344 wppa-settings-autosave.php:3683
msgid "At the bottom"
msgstr ""

#: wppa-settings-autosave.php:2311
msgid "Show desc on thumb area"
msgstr ""

#: wppa-settings-autosave.php:2312
msgid ""
"Select if and where to display the album description on the thumbnail "
"display."
msgstr ""

#: wppa-settings-autosave.php:2322
msgid "Show Edit/Delete links"
msgstr ""

#: wppa-settings-autosave.php:2323
msgid "Show these links under default thumbnails for owner and admin."
msgstr ""

#: wppa-settings-autosave.php:2331
msgid "Show empty thumbnail area"
msgstr ""

#: wppa-settings-autosave.php:2332
msgid "Display thumbnail areas with upload link only for empty albums."
msgstr ""

#: wppa-settings-autosave.php:2340
msgid "Upload/create link on thumbnail area"
msgstr ""

#: wppa-settings-autosave.php:2341
msgid "Select the location of the upload and crete links."
msgstr ""

#: wppa-settings-autosave.php:2352
msgid "Album cover related visibility settings"
msgstr ""

#: wppa-settings-autosave.php:2354
msgid "Covertext"
msgstr ""

#: wppa-settings-autosave.php:2355
msgid "Show the text on the album cover."
msgstr ""

#: wppa-settings-autosave.php:2356
msgid "Display the album decription on the album cover"
msgstr ""

#: wppa-settings-autosave.php:2364
msgid "Enable the slideshow."
msgstr ""

#: wppa-settings-autosave.php:2365
msgid ""
"If you do not want slideshows: uncheck this box. Browsing full size images "
"will remain possible."
msgstr ""

#: wppa-settings-autosave.php:2372
msgid "Slideshow/Browse"
msgstr ""

#: wppa-settings-autosave.php:2373
msgid "Display the Slideshow / Browse photos link on album covers"
msgstr ""

#: wppa-settings-autosave.php:2374
msgid ""
"This setting causes the Slideshow link to be displayed on the album cover."
msgstr ""

#: wppa-settings-autosave.php:2375
msgid ""
"If slideshows are disabled in item 2 in this table, you will see a browse "
"link to fullsize images."
msgstr ""

#: wppa-settings-autosave.php:2376
msgid "If you do not want the browse link either, uncheck this item."
msgstr ""

#: wppa-settings-autosave.php:2383
msgid "View ..."
msgstr ""

#: wppa-settings-autosave.php:2384
msgid "Display the View xx albums and yy photos link on album covers"
msgstr ""

#: wppa-settings-autosave.php:2392
msgid "Treecount"
msgstr ""

#: wppa-settings-autosave.php:2393
msgid "Display the total number of (sub)albums and photos in subalbums"
msgstr ""

#: wppa-settings-autosave.php:2394
msgid ""
"Displays the total number of sub albums and photos in the entire album tree "
"in parenthesis if the numbers differ from the direct content of the album."
msgstr ""

#: wppa-settings-autosave.php:2396
msgid "detailed"
msgstr ""

#: wppa-settings-autosave.php:2396
msgid "totals only"
msgstr ""

#: wppa-settings-autosave.php:2403
msgid "Show categories"
msgstr ""

#: wppa-settings-autosave.php:2404
msgid "Display the album categories on the covers."
msgstr ""

#: wppa-settings-autosave.php:2412
msgid "Skip empty albums"
msgstr ""

#: wppa-settings-autosave.php:2413
msgid "Do not show empty albums, except for admin and owner."
msgstr ""

#: wppa-settings-autosave.php:2421
msgid "Count on title"
msgstr ""

#: wppa-settings-autosave.php:2422
msgid "Show photocount along with album title. "
msgstr ""

#: wppa-settings-autosave.php:2425 wppa-settings-autosave.php:2436
msgid "top album only"
msgstr ""

#: wppa-settings-autosave.php:2425 wppa-settings-autosave.php:2436
msgid "total tree"
msgstr ""

#: wppa-settings-autosave.php:2432
msgid "Viewcount on cover"
msgstr ""

#: wppa-settings-autosave.php:2433
msgid "Show total photo viewcount on album covers."
msgstr ""

#: wppa-settings-autosave.php:2444
msgid "Widget related visibility settings"
msgstr ""

#: wppa-settings-autosave.php:2446
msgid "Big Browse Buttons in widget"
msgstr ""

#: wppa-settings-autosave.php:2447
msgid "Enable invisible browsing buttons in widget slideshows."
msgstr ""

#: wppa-settings-autosave.php:2456
msgid "Ugly Browse Buttons in widget"
msgstr ""

#: wppa-settings-autosave.php:2457
msgid "Enable ugly browsing buttons in widget slideshows."
msgstr ""

#: wppa-settings-autosave.php:2458
msgid "If checked, the fullsize image is covered by browse buttons."
msgstr ""

#: wppa-settings-autosave.php:2466
msgid "Album widget tooltip"
msgstr ""

#: wppa-settings-autosave.php:2467
msgid "Show the album description on hoovering thumbnail in album widget"
msgstr ""

#: wppa-settings-autosave.php:2475 wppa-settings-autosave.php:4782
msgid ""
"Lightbox related settings. These settings have effect only when Table IX-J3 "
"is set to wppa"
msgstr ""

#: wppa-settings-autosave.php:2488
msgid "Overlay theme color"
msgstr ""

#: wppa-settings-autosave.php:2489
msgid "The color of the image border and text background."
msgstr ""

#: wppa-settings-autosave.php:2492 wppa-settings-autosave.php:2503
msgid "Black"
msgstr ""

#: wppa-settings-autosave.php:2492 wppa-settings-autosave.php:2503
msgid "White"
msgstr ""

#: wppa-settings-autosave.php:2499
msgid "Overlay background color"
msgstr ""

#: wppa-settings-autosave.php:2500
msgid "The color of the outer background."
msgstr ""

#: wppa-settings-autosave.php:2510
msgid "Overlay slide name"
msgstr ""

#: wppa-settings-autosave.php:2511
msgid "Show name if from slide."
msgstr ""

#: wppa-settings-autosave.php:2512
msgid ""
"Shows the photos name on a lightbox display when initiated from a slide."
msgstr ""

#: wppa-settings-autosave.php:2513 wppa-settings-autosave.php:2523
msgid ""
"This setting also applies to film thumbnails if Table VI-B6a is set to "
"lightbox overlay."
msgstr ""

#: wppa-settings-autosave.php:2520
msgid "Overlay slide desc"
msgstr ""

#: wppa-settings-autosave.php:2521
msgid "Show description if from slide."
msgstr ""

#: wppa-settings-autosave.php:2522
msgid ""
"Shows the photos description on a lightbox display when initiated from a "
"slide."
msgstr ""

#: wppa-settings-autosave.php:2530
msgid "Overlay thumb name"
msgstr ""

#: wppa-settings-autosave.php:2531
msgid "Show the photos name if from thumb."
msgstr ""

#: wppa-settings-autosave.php:2532
msgid ""
"Shows the name on a lightbox display when initiated from a standard "
"thumbnail or a widget thumbnail."
msgstr ""

#: wppa-settings-autosave.php:2533 wppa-settings-autosave.php:2543
msgid ""
"This setting applies to standard thumbnails, thumbnail-, comment-, topten- "
"and lasten-widget."
msgstr ""

#: wppa-settings-autosave.php:2540
msgid "Overlay thumb desc"
msgstr ""

#: wppa-settings-autosave.php:2541
msgid "Show description if from thumb."
msgstr ""

#: wppa-settings-autosave.php:2542
msgid ""
"Shows the photos description on a lightbox display when initiated from a "
"standard thumbnail or a widget thumbnail."
msgstr ""

#: wppa-settings-autosave.php:2550
msgid "Overlay potd name"
msgstr ""

#: wppa-settings-autosave.php:2551
msgid "Show the photos name if from photo of the day."
msgstr ""

#: wppa-settings-autosave.php:2552
msgid ""
"Shows the name on a lightbox display when initiated from the photo of the "
"day."
msgstr ""

#: wppa-settings-autosave.php:2559
msgid "Overlay potd desc"
msgstr ""

#: wppa-settings-autosave.php:2560
msgid "Show description if from from photo of the day."
msgstr ""

#: wppa-settings-autosave.php:2561
msgid ""
"Shows the photos description on a lightbox display when initiated from the "
"photo of the day."
msgstr ""

#: wppa-settings-autosave.php:2568
msgid "Overlay sphoto name"
msgstr ""

#: wppa-settings-autosave.php:2569
msgid "Show the photos name if from a single photo."
msgstr ""

#: wppa-settings-autosave.php:2570
msgid ""
"Shows the name on a lightbox display when initiated from a single photo."
msgstr ""

#: wppa-settings-autosave.php:2577
msgid "Overlay sphoto desc"
msgstr ""

#: wppa-settings-autosave.php:2578
msgid "Show description if from from a single photo."
msgstr ""

#: wppa-settings-autosave.php:2579
msgid ""
"Shows the photos description on a lightbox display when initiated from a "
"single photo."
msgstr ""

#: wppa-settings-autosave.php:2586
msgid "Overlay mphoto name"
msgstr ""

#: wppa-settings-autosave.php:2587
msgid "Show the photos name if from a single media style photo."
msgstr ""

#: wppa-settings-autosave.php:2588
msgid ""
"Shows the name on a lightbox display when initiated from a single media "
"style photo."
msgstr ""

#: wppa-settings-autosave.php:2595
msgid "Overlay mphoto desc"
msgstr ""

#: wppa-settings-autosave.php:2596
msgid "Show description if from from a media style photo."
msgstr ""

#: wppa-settings-autosave.php:2597
msgid ""
"Shows the photos description on a lightbox display when initiated from a "
"single media style photo."
msgstr ""

#: wppa-settings-autosave.php:2604
msgid "Overlay albumwidget name"
msgstr ""

#: wppa-settings-autosave.php:2605
msgid "Show the photos name if from the album widget."
msgstr ""

#: wppa-settings-autosave.php:2606
msgid ""
"Shows the name on a lightbox display when initiated from the album widget."
msgstr ""

#: wppa-settings-autosave.php:2613
msgid "Overlay albumwidget desc"
msgstr ""

#: wppa-settings-autosave.php:2614
msgid "Show description if from from the album widget."
msgstr ""

#: wppa-settings-autosave.php:2615
msgid ""
"Shows the photos description on a lightbox display when initiated from the "
"album widget."
msgstr ""

#: wppa-settings-autosave.php:2622
msgid "Overlay coverphoto name"
msgstr ""

#: wppa-settings-autosave.php:2623
msgid "Show the photos name if from the album cover."
msgstr ""

#: wppa-settings-autosave.php:2624
msgid ""
"Shows the name on a lightbox display when initiated from the album "
"coverphoto."
msgstr ""

#: wppa-settings-autosave.php:2631
msgid "Overlay coverphoto desc"
msgstr ""

#: wppa-settings-autosave.php:2632
msgid "Show description if from from the album cover."
msgstr ""

#: wppa-settings-autosave.php:2633
msgid ""
"Shows the photos description on a lightbox display when initiated from the "
"album coverphoto."
msgstr ""

#: wppa-settings-autosave.php:2640
msgid "Overlay add owner"
msgstr ""

#: wppa-settings-autosave.php:2641
msgid "Add the owner to the photo name on lightbox displays."
msgstr ""

#: wppa-settings-autosave.php:2642
msgid ""
"This setting is independant of the show name switches and is a global "
"setting."
msgstr ""

#: wppa-settings-autosave.php:2649
msgid "Overlay show start/stop"
msgstr ""

#: wppa-settings-autosave.php:2650
msgid "Show Start and Stop for running slideshow on lightbox."
msgstr ""

#: wppa-settings-autosave.php:2658
msgid "Overlay show legenda"
msgstr ""

#: wppa-settings-autosave.php:2659
msgid "Show \"Press f for fullsize\" etc. on lightbox."
msgstr ""

#: wppa-settings-autosave.php:2660
msgid "Independant of this setting, it will not show up on mobile devices."
msgstr ""

#: wppa-settings-autosave.php:2667
msgid "Show fullscreen icons"
msgstr ""

#: wppa-settings-autosave.php:2668
msgid "Shows fullscreen and back to normal icon buttons on upper right corner"
msgstr ""

#: wppa-settings-autosave.php:2676
msgid "Show rating"
msgstr ""

#: wppa-settings-autosave.php:2677
msgid "Shows and enables rating on lightbox."
msgstr ""

#: wppa-settings-autosave.php:2678
msgid ""
"This works for 5 and 10 stars only, not for single votes or numerical display"
msgstr ""

#: wppa-settings-autosave.php:2685
msgid "Overlay show counter"
msgstr ""

#: wppa-settings-autosave.php:2686
msgid "Show the x/y counter below the image."
msgstr ""

#: wppa-settings-autosave.php:2694
msgid "Show Zoom in"
msgstr ""

#: wppa-settings-autosave.php:2695
msgid "Display tooltip \"Zoom in\" along with the magnifier cursor."
msgstr ""

#: wppa-settings-autosave.php:2696
msgid ""
"If you select ---none--- in Table I-G2 for magnifier size, the tooltop "
"contains the photo name."
msgstr ""

#: wppa-settings-autosave.php:2704
msgid "Frontend upload configuration settings"
msgstr ""

#: wppa-settings-autosave.php:2706
msgid "User upload Photos"
msgstr ""

#: wppa-settings-autosave.php:2707
msgid "Enable frontend upload."
msgstr ""

#: wppa-settings-autosave.php:2708
msgid ""
"If you check this item, frontend upload will be enabled according to the "
"rules set in the following items of this table."
msgstr ""

#: wppa-settings-autosave.php:2716
msgid "User upload Video"
msgstr ""

#: wppa-settings-autosave.php:2717
msgid "Enable frontend upload of video."
msgstr ""

#: wppa-settings-autosave.php:2718 wppa-settings-autosave.php:2728
msgid "Requires Table II-H1 to be ticked."
msgstr ""

#: wppa-settings-autosave.php:2726
msgid "User upload Audio"
msgstr ""

#: wppa-settings-autosave.php:2727
msgid "Enable frontend upload of audio."
msgstr ""

#: wppa-settings-autosave.php:2736
msgid "User upload Photos login"
msgstr ""

#: wppa-settings-autosave.php:2737
msgid "Frontend upload requires the user is logged in."
msgstr ""

#: wppa-settings-autosave.php:2738
msgid ""
"If you uncheck this box, make sure you check the item Owners only in Table "
"VII-D1."
msgstr ""

#: wppa-settings-autosave.php:2739
msgid ""
"Also: set the owner to ---public--- of the albums that are allowed to be "
"uploaded to."
msgstr ""

#: wppa-settings-autosave.php:2746
msgid "User upload Ajax"
msgstr ""

#: wppa-settings-autosave.php:2747
msgid "Shows the upload progression bar."
msgstr ""

#: wppa-settings-autosave.php:2755
msgid "Show Copyright"
msgstr ""

#: wppa-settings-autosave.php:2756
msgid "Show a copyright warning on frontend upload locations."
msgstr ""

#: wppa-settings-autosave.php:2765
msgid "Copyright notice"
msgstr ""

#: wppa-settings-autosave.php:2766
msgid "The message to be displayed."
msgstr ""

#: wppa-settings-autosave.php:2774
msgid "User Watermark"
msgstr ""

#: wppa-settings-autosave.php:2775
msgid "Uploading users may select watermark settings"
msgstr ""

#: wppa-settings-autosave.php:2776
msgid ""
"If checked, anyone who can upload and/or import photos can overrule the "
"default watermark settings."
msgstr ""

#: wppa-settings-autosave.php:2783
msgid "User name"
msgstr ""

#: wppa-settings-autosave.php:2784
msgid "Uploading users may overrule the default name."
msgstr ""

#: wppa-settings-autosave.php:2785
msgid ""
"If checked, the default photo name as defined in Table IX-D13 may be "
"overruled by the user."
msgstr ""

#: wppa-settings-autosave.php:2792
msgid "Apply Newphoto desc user"
msgstr ""

#: wppa-settings-autosave.php:2793
msgid "Give each new frontend uploaded photo a standard description."
msgstr ""

#: wppa-settings-autosave.php:2794
msgid ""
"If checked, each new photo will get the description (template) as specified "
"in Table IX-D5."
msgstr ""

#: wppa-settings-autosave.php:2795
msgid "Note: If the next item is checked, the user can overwrite this"
msgstr ""

#: wppa-settings-autosave.php:2802
msgid "User desc"
msgstr ""

#: wppa-settings-autosave.php:2803
msgid "Uploading users may overrule the default description."
msgstr ""

#: wppa-settings-autosave.php:2811
msgid "User upload custom"
msgstr ""

#: wppa-settings-autosave.php:2812
msgid "Frontend upload can fill in custom data fields."
msgstr ""

#: wppa-settings-autosave.php:2820
msgid "User upload tags"
msgstr ""

#: wppa-settings-autosave.php:2821
msgid "Frontend upload can add tags."
msgstr ""

#: wppa-settings-autosave.php:2822
msgid "You can configure the details of tag addition in Table IX-D18.x"
msgstr ""

#: wppa-settings-autosave.php:2830 wppa-settings-autosave.php:2858
#: wppa-settings-autosave.php:2886
msgid "Tag selection box"
msgstr ""

#: wppa-settings-autosave.php:2831 wppa-settings-autosave.php:2859
#: wppa-settings-autosave.php:2887
msgid "Front-end upload tags selecion box."
msgstr ""

#: wppa-settings-autosave.php:2835 wppa-settings-autosave.php:2863
#: wppa-settings-autosave.php:2891
msgid "On:"
msgstr ""

#: wppa-settings-autosave.php:2835 wppa-settings-autosave.php:2863
#: wppa-settings-autosave.php:2891
msgid "Multi:"
msgstr ""

#: wppa-settings-autosave.php:2840 wppa-settings-autosave.php:2868
#: wppa-settings-autosave.php:2896
msgid "Caption box"
msgstr ""

#: wppa-settings-autosave.php:2841 wppa-settings-autosave.php:2869
#: wppa-settings-autosave.php:2897
msgid "The title of the tag selection box."
msgstr ""

#: wppa-settings-autosave.php:2849 wppa-settings-autosave.php:2877
#: wppa-settings-autosave.php:2905
msgid "Tags box"
msgstr ""

#: wppa-settings-autosave.php:2850 wppa-settings-autosave.php:2878
#: wppa-settings-autosave.php:2906
msgid "The tags in the selection box."
msgstr ""

#: wppa-settings-autosave.php:2851 wppa-settings-autosave.php:2879
#: wppa-settings-autosave.php:2907
msgid ""
"Enter the tags you want to appear in the selection box. Empty means: all "
"existing tags"
msgstr ""

#: wppa-settings-autosave.php:2914
msgid "New tags"
msgstr ""

#: wppa-settings-autosave.php:2915
msgid "Input field for any user defined tags."
msgstr ""

#: wppa-settings-autosave.php:2923
msgid "New tags caption"
msgstr ""

#: wppa-settings-autosave.php:2924
msgid "The caption above the tags input field."
msgstr ""

#: wppa-settings-autosave.php:2932
msgid "Tags box New"
msgstr ""

#: wppa-settings-autosave.php:2933
msgid "The tags in the New tags input box."
msgstr ""

#: wppa-settings-autosave.php:2941
msgid "Preview tags"
msgstr ""

#: wppa-settings-autosave.php:2942
msgid "Show a preview of all tags that will be added to the photo info."
msgstr ""

#: wppa-settings-autosave.php:2950
msgid "Camera connect"
msgstr ""

#: wppa-settings-autosave.php:2951
msgid "Connect frontend upload to camara on mobile devices with camera"
msgstr ""

#: wppa-settings-autosave.php:2959
msgid "Blog It!"
msgstr ""

#: wppa-settings-autosave.php:2960
msgid "Enable blogging photos."
msgstr ""

#: wppa-settings-autosave.php:2961
msgid "Users need the capability edit_posts to directly blog photos."
msgstr ""

#: wppa-settings-autosave.php:2963
msgid "disabled"
msgstr ""

#: wppa-settings-autosave.php:2964
msgid "optional"
msgstr ""

#: wppa-settings-autosave.php:2965
msgid "always"
msgstr ""

#: wppa-settings-autosave.php:2976
msgid "Blog It need moderation"
msgstr ""

#: wppa-settings-autosave.php:2977
msgid "Posts with blogged photos need moderation."
msgstr ""

#: wppa-settings-autosave.php:2985
msgid "Blog It shortcode"
msgstr ""

#: wppa-settings-autosave.php:2986
msgid "Shortcode to be used on the blog post"
msgstr ""

#: wppa-settings-autosave.php:2987
msgid "Make sure it contains photo=\"#id\""
msgstr ""

#: wppa-settings-autosave.php:2995
msgid "Miscellaneous visibility settings"
msgstr ""

#: wppa-settings-autosave.php:2997
msgid "Frontend ending label"
msgstr ""

#: wppa-settings-autosave.php:2998
msgid "Frontend upload / create / edit dialog closing label text."
msgstr ""

#: wppa-settings-autosave.php:3001
msgid "Abort"
msgstr ""

#: wppa-settings-autosave.php:3001
msgid "Close"
msgstr ""

#: wppa-settings-autosave.php:3001
msgid "Exit"
msgstr ""

#: wppa-settings-autosave.php:3001
msgid "Quit"
msgstr ""

#: wppa-settings-autosave.php:3008
msgid "Owner on new line"
msgstr ""

#: wppa-settings-autosave.php:3009
msgid "Place the (owner) text on a new line."
msgstr ""

#: wppa-settings-autosave.php:3017
msgid "Custom datafields albums"
msgstr ""

#: wppa-settings-autosave.php:3018
msgid "Define up to 10 custom data fields for albums."
msgstr ""

#: wppa-settings-autosave.php:3028 wppa-settings-autosave.php:3054
#, php-format
msgid "Name, vis, edit %s"
msgstr ""

#: wppa-settings-autosave.php:3029 wppa-settings-autosave.php:3055
#, php-format
msgid "The caption for field %s, visibility and editability at frontend."
msgstr ""

#: wppa-settings-autosave.php:3030 wppa-settings-autosave.php:3056
#, php-format
msgid ""
"If you check the first box, the value of this field is displayable in photo "
"descriptions at the frontend with keyword w#c%s"
msgstr ""

#: wppa-settings-autosave.php:3031 wppa-settings-autosave.php:3057
msgid ""
"If you check the second box, the value of this field is editable at the "
"frontend new style dialog."
msgstr ""

#: wppa-settings-autosave.php:3043
msgid "Custom datafields photos"
msgstr ""

#: wppa-settings-autosave.php:3044
msgid "Define up to 10 custom data fields for photos."
msgstr ""

#: wppa-settings-autosave.php:3070
msgid "Navigation symbols style"
msgstr ""

#: wppa-settings-autosave.php:3071
msgid "The corner rounding size of navigation icons."
msgstr ""

#: wppa-settings-autosave.php:3072
msgid ""
"Use gif/png if you have excessive pageload times due to many slideshows on a "
"page"
msgstr ""

#: wppa-settings-autosave.php:3074
msgid "light"
msgstr ""

#: wppa-settings-autosave.php:3074
msgid "heavy"
msgstr ""

#: wppa-settings-autosave.php:3074
msgid "use gif/png, no svg"
msgstr ""

#: wppa-settings-autosave.php:3098
msgid "Table III:"
msgstr ""

#: wppa-settings-autosave.php:3098
msgid "Backgrounds:"
msgstr ""

#: wppa-settings-autosave.php:3099
msgid "This table describes the backgrounds of wppa+ elements."
msgstr ""

#: wppa-settings-autosave.php:3109 wppa-settings-autosave.php:3494
#: wppa-settings-autosave.php:9131
msgid "Background color"
msgstr ""

#: wppa-settings-autosave.php:3110 wppa-settings-autosave.php:3112
#: wppa-settings-autosave.php:3495 wppa-settings-autosave.php:3497
msgid "Sample"
msgstr ""

#: wppa-settings-autosave.php:3111 wppa-settings-autosave.php:3496
msgid "Border color"
msgstr ""

#: wppa-settings-autosave.php:3120
msgid "Slideshow elements backgrounds"
msgstr ""

#: wppa-settings-autosave.php:3122
msgid "Nav"
msgstr ""

#: wppa-settings-autosave.php:3123
msgid "Navigation bars."
msgstr ""

#: wppa-settings-autosave.php:3124
msgid "Enter valid CSS colors for navigation backgrounds and borders."
msgstr ""

#: wppa-settings-autosave.php:3135
msgid "SlideImg"
msgstr ""

#: wppa-settings-autosave.php:3136
msgid "Fullsize Slideshow Photos."
msgstr ""

#: wppa-settings-autosave.php:3137
msgid "Enter valid CSS colors for fullsize photo backgrounds and borders."
msgstr ""

#: wppa-settings-autosave.php:3138
msgid "The colors may be equal or \"transparent\""
msgstr ""

#: wppa-settings-autosave.php:3139
msgid ""
"For more information about slideshow image borders see the help on Table I-B4"
msgstr ""

#: wppa-settings-autosave.php:3150 wppa-settings-autosave.php:5068
msgid "Numbar"
msgstr ""

#: wppa-settings-autosave.php:3151
msgid "Number bar box background."
msgstr ""

#: wppa-settings-autosave.php:3152
msgid "Enter valid CSS colors for numbar box backgrounds and borders."
msgstr ""

#: wppa-settings-autosave.php:3163
msgid "Numbar active"
msgstr ""

#: wppa-settings-autosave.php:3164
msgid "Number bar active box background."
msgstr ""

#: wppa-settings-autosave.php:3165
msgid "Enter valid CSS colors for numbar active box backgrounds and borders."
msgstr ""

#: wppa-settings-autosave.php:3176
msgid "Name/desc"
msgstr ""

#: wppa-settings-autosave.php:3177
msgid "Name and Description bars."
msgstr ""

#: wppa-settings-autosave.php:3178
msgid ""
"Enter valid CSS colors for name and description box backgrounds and borders."
msgstr ""

#: wppa-settings-autosave.php:3190
msgid "Comment input and display areas."
msgstr ""

#: wppa-settings-autosave.php:3191
msgid "Enter valid CSS colors for comment box backgrounds and borders."
msgstr ""

#: wppa-settings-autosave.php:3202 wppa-settings-autosave.php:9191
#: wppa-settings-autosave.php:9236
msgid "Custom"
msgstr ""

#: wppa-settings-autosave.php:3203
msgid "Custom box background."
msgstr ""

#: wppa-settings-autosave.php:3204
msgid "Enter valid CSS colors for custom box backgrounds and borders."
msgstr ""

#: wppa-settings-autosave.php:3215
msgid "IPTC"
msgstr ""

#: wppa-settings-autosave.php:3216
msgid "IPTC display box background."
msgstr ""

#: wppa-settings-autosave.php:3217
msgid "Enter valid CSS colors for iptc box backgrounds and borders."
msgstr ""

#: wppa-settings-autosave.php:3228
msgid "EXIF"
msgstr ""

#: wppa-settings-autosave.php:3229
msgid "EXIF display box background."
msgstr ""

#: wppa-settings-autosave.php:3230
msgid "Enter valid CSS colors for exif box backgrounds and borders."
msgstr ""

#: wppa-settings-autosave.php:3241
msgid "Share"
msgstr ""

#: wppa-settings-autosave.php:3242
msgid "Share box display background."
msgstr ""

#: wppa-settings-autosave.php:3243
msgid "Enter valid CSS colors for share box backgrounds and borders."
msgstr ""

#: wppa-settings-autosave.php:3254
msgid "Other backgrounds and colors"
msgstr ""

#: wppa-settings-autosave.php:3256
msgid "Even"
msgstr ""

#: wppa-settings-autosave.php:3257
msgid "Even background."
msgstr ""

#: wppa-settings-autosave.php:3258
msgid ""
"Enter valid CSS colors for even numbered backgrounds and borders of album "
"covers and thumbnail displays 'As covers'."
msgstr ""

#: wppa-settings-autosave.php:3269
msgid "Odd"
msgstr ""

#: wppa-settings-autosave.php:3270
msgid "Odd background."
msgstr ""

#: wppa-settings-autosave.php:3271
msgid ""
"Enter valid CSS colors for odd numbered backgrounds and borders of album "
"covers and thumbnail displays 'As covers'."
msgstr ""

#: wppa-settings-autosave.php:3282
msgid "Thumbnail padding"
msgstr ""

#: wppa-settings-autosave.php:3283
msgid "Thumbnail padding color if thumbnail aspect is a padded setting."
msgstr ""

#: wppa-settings-autosave.php:3284
msgid ""
"Enter valid CSS color hexadecimal like #000000 for black or #ffffff for "
"white for the padded thumbnails."
msgstr ""

#: wppa-settings-autosave.php:3295
msgid "Img"
msgstr ""

#: wppa-settings-autosave.php:3296
msgid "Cover Photos and popups."
msgstr ""

#: wppa-settings-autosave.php:3297
msgid ""
"Enter valid CSS colors for Cover photo and popup backgrounds and borders."
msgstr ""

#: wppa-settings-autosave.php:3309
msgid "Upload box background."
msgstr ""

#: wppa-settings-autosave.php:3310
msgid "Enter valid CSS colors for upload box backgrounds and borders."
msgstr ""

#: wppa-settings-autosave.php:3311
msgid ""
"See the Upload box, created by the shortcode [wppa type=\"upload\"][/wppa]"
msgstr ""

#: wppa-settings-autosave.php:3322
msgid "Multitag"
msgstr ""

#: wppa-settings-autosave.php:3323
msgid "Multitag box background."
msgstr ""

#: wppa-settings-autosave.php:3324
msgid "Enter valid CSS colors for multitag box backgrounds and borders."
msgstr ""

#: wppa-settings-autosave.php:3325
msgid ""
"See the Multitag search box, created by the shortcode [wppa type=\"multitag"
"\"][/wppa]"
msgstr ""

#: wppa-settings-autosave.php:3336
msgid "Tagcloud"
msgstr ""

#: wppa-settings-autosave.php:3337
msgid "Tagcloud box background."
msgstr ""

#: wppa-settings-autosave.php:3338
msgid "Enter valid CSS colors for tagcloud box backgrounds and borders."
msgstr ""

#: wppa-settings-autosave.php:3339
msgid ""
"See the Tagcloud search box, created by the shortcode [wppa type=\"tagcloud"
"\"][/wppa]"
msgstr ""

#: wppa-settings-autosave.php:3350
msgid "Superview"
msgstr ""

#: wppa-settings-autosave.php:3351
msgid "Superview box background."
msgstr ""

#: wppa-settings-autosave.php:3352
msgid "Enter valid CSS colors for superview box backgrounds and borders."
msgstr ""

#: wppa-settings-autosave.php:3353
msgid ""
"See the Superview search box, created by the shortcode [wppa type=\"superview"
"\"][/wppa]"
msgstr ""

#: wppa-settings-autosave.php:3365
msgid "Search box background."
msgstr ""

#: wppa-settings-autosave.php:3366
msgid "Enter valid CSS colors for search box backgrounds and borders."
msgstr ""

#: wppa-settings-autosave.php:3367
msgid ""
"See the Search box, created by the shortcode [wppa type=\"search\"][/wppa]"
msgstr ""

#: wppa-settings-autosave.php:3378
msgid "BestOf"
msgstr ""

#: wppa-settings-autosave.php:3379
msgid "BestOf box background."
msgstr ""

#: wppa-settings-autosave.php:3380
msgid "Enter valid CSS colors for bestof box backgrounds and borders."
msgstr ""

#: wppa-settings-autosave.php:3381
msgid ""
"See the Best of box, created by the shortcode [wppa type=\"bestof\"][/wppa]"
msgstr ""

#: wppa-settings-autosave.php:3392
msgid "Calendar"
msgstr ""

#: wppa-settings-autosave.php:3393
msgid "Calendar box background."
msgstr ""

#: wppa-settings-autosave.php:3394
msgid "Enter valid CSS colors for calendar box backgrounds and borders."
msgstr ""

#: wppa-settings-autosave.php:3395
msgid ""
"See the Calendar box, created by the shortcode [wppa type=\"calendar\"][/"
"wppa]"
msgstr ""

#: wppa-settings-autosave.php:3406
msgid "Stereo"
msgstr ""

#: wppa-settings-autosave.php:3407
msgid "Stereo mode selection box background."
msgstr ""

#: wppa-settings-autosave.php:3408
msgid ""
"Enter valid CSS colors for stereo mode selection box backgrounds and borders."
msgstr ""

#: wppa-settings-autosave.php:3409
msgid ""
"See the Stereo type selection box, created by the shortcode [wppa type="
"\"stereo\"][/wppa]"
msgstr ""

#: wppa-settings-autosave.php:3421
msgid "Admins choice box background."
msgstr ""

#: wppa-settings-autosave.php:3422
msgid "Enter valid CSS colors for admins choice box backgrounds and borders."
msgstr ""

#: wppa-settings-autosave.php:3423
msgid ""
"See the Admins choice box, created by the shortcode [wppa type=\"choice\"][/"
"wppa]"
msgstr ""

#: wppa-settings-autosave.php:3434
msgid "Modal render box"
msgstr ""

#: wppa-settings-autosave.php:3435
msgid "The background for the Ajax modal rendering box."
msgstr ""

#: wppa-settings-autosave.php:3436
msgid "Recommended color: your theme background color."
msgstr ""

#: wppa-settings-autosave.php:3447
msgid "Navigation symbols"
msgstr ""

#: wppa-settings-autosave.php:3448
msgid "Navigation symbol background and fill colors."
msgstr ""

#: wppa-settings-autosave.php:3460
msgid "Navigation symbols Lightbox"
msgstr ""

#: wppa-settings-autosave.php:3461
msgid "Navigation symbol background and fill colors Lightbox."
msgstr ""

#: wppa-settings-autosave.php:3507
msgid "Table IV:"
msgstr ""

#: wppa-settings-autosave.php:3507
msgid "Behaviour:"
msgstr ""

#: wppa-settings-autosave.php:3508
msgid "This table describes the dynamic behaviour of certain wppa+ elements."
msgstr ""

#: wppa-settings-autosave.php:3526
msgid "System related settings"
msgstr ""

#: wppa-settings-autosave.php:3528
msgid "Use Ajax"
msgstr ""

#: wppa-settings-autosave.php:3529
msgid "Use Ajax as much as is possible and implemented."
msgstr ""

#: wppa-settings-autosave.php:3530
msgid ""
"If this box is ticked, page content updates from within wppa+ displays will "
"be Ajax based as much as possible."
msgstr ""

#: wppa-settings-autosave.php:3538
msgid "Ajax NON Admin"
msgstr ""

#: wppa-settings-autosave.php:3539
msgid "Frontend ajax use no admin files."
msgstr ""

#: wppa-settings-autosave.php:3540
msgid "If you want to password protect wp-admin, check this box."
msgstr ""

#: wppa-settings-autosave.php:3541
msgid ""
"In rare cases changing page content does not work when this box is checked. "
"Verify the functionality!"
msgstr ""

#: wppa-settings-autosave.php:3548
msgid "Modal boxes"
msgstr ""

#: wppa-settings-autosave.php:3549
msgid "Place Ajax rendered content in modal boxes"
msgstr ""

#: wppa-settings-autosave.php:3557
msgid "Photo names in urls"
msgstr ""

#: wppa-settings-autosave.php:3558
msgid "Display photo names in urls."
msgstr ""

#: wppa-settings-autosave.php:3559
msgid "Urls to wppa+ displays will contain photonames instead of numbers."
msgstr ""

#: wppa-settings-autosave.php:3560
msgid ""
"It is your responsibility to avoid duplicate names of photos in the same "
"album."
msgstr ""

#: wppa-settings-autosave.php:3567
msgid "Album names in urls"
msgstr ""

#: wppa-settings-autosave.php:3568
msgid "Display album names in urls."
msgstr ""

#: wppa-settings-autosave.php:3569
msgid "Urls to wppa+ displays will contain albumnames instead of numbers."
msgstr ""

#: wppa-settings-autosave.php:3570
msgid ""
"It is your responsibility to avoid duplicate names of albums in the system."
msgstr ""

#: wppa-settings-autosave.php:3577
msgid "Use short query args"
msgstr ""

#: wppa-settings-autosave.php:3578
msgid "Use &album=... &photo=..."
msgstr ""

#: wppa-settings-autosave.php:3579
msgid ""
"Urls to wppa+ displays will contain &album=... &photo=... instead of &wppa-"
"album=... &wppa-photo=..."
msgstr ""

#: wppa-settings-autosave.php:3580
msgid ""
"Use this setting only when there are no conflicts with other plugins that "
"may interprete arguments like &album= etc."
msgstr ""

#: wppa-settings-autosave.php:3587
msgid "Enable pretty links"
msgstr ""

#: wppa-settings-autosave.php:3588
msgid "Enable the generation and understanding of pretty links."
msgstr ""

#: wppa-settings-autosave.php:3589
msgid ""
"If checked, links to social media and the qr code will have \"/token1/token2/"
"\" etc instead of \"&arg1=..&arg2=..\" etc."
msgstr ""

#: wppa-settings-autosave.php:3590
msgid ""
"These types of links will be interpreted and cause a redirection on entering."
msgstr ""

#: wppa-settings-autosave.php:3591
msgid ""
"It is recommended to check this box. It shortens links dramatically and "
"simplifies qr codes."
msgstr ""

#: wppa-settings-autosave.php:3592
msgid ""
"However, you may encounter conflicts with themes and/or other plugins, so "
"test it throughly!"
msgstr ""

#: wppa-settings-autosave.php:3593
msgid ""
"Table IV-A2 (Photo names in urls) must be UNchecked for this setting to work!"
msgstr ""

#: wppa-settings-autosave.php:3600
msgid "Enable encrypted links"
msgstr ""

#: wppa-settings-autosave.php:3601
msgid "Encrypt album and photo ids in links."
msgstr ""

#: wppa-settings-autosave.php:3604
msgid "The page will be reloaded."
msgstr ""

#: wppa-settings-autosave.php:3610
msgid "Refuse unencrypted"
msgstr ""

#: wppa-settings-autosave.php:3611
msgid "When encrypted is enabled, refuse unencrypted urls."
msgstr ""

#: wppa-settings-autosave.php:3619
msgid "Update addressline"
msgstr ""

#: wppa-settings-autosave.php:3620
msgid "Update the addressline after an ajax action or next slide."
msgstr ""

#: wppa-settings-autosave.php:3621
msgid ""
"If checked, refreshing the page will show the current content and the "
"browsers back and forth arrows will browse the history on the page."
msgstr ""

#: wppa-settings-autosave.php:3622
msgid ""
"If unchecked, refreshing the page will re-display the content of the "
"original page."
msgstr ""

#: wppa-settings-autosave.php:3623
msgid ""
"This will only work on browsers that support history.pushState() and "
"therefor NOT in IE"
msgstr ""

#: wppa-settings-autosave.php:3630
msgid "Render shortcode always"
msgstr ""

#: wppa-settings-autosave.php:3631
msgid "This will skip the check on proper initialisation."
msgstr ""

#: wppa-settings-autosave.php:3632
msgid ""
"This setting is required for certain themes like Gantry to prevent the "
"display of wppa placeholders like [WPPA+ Photo display]."
msgstr ""

#: wppa-settings-autosave.php:3639
msgid "Track viewcounts"
msgstr ""

#: wppa-settings-autosave.php:3640
msgid "Register number of views of albums and photos."
msgstr ""

#: wppa-settings-autosave.php:3648
msgid "Track clickcounts"
msgstr ""

#: wppa-settings-autosave.php:3649
msgid "Register number of clicks on photos that link to an url."
msgstr ""

#: wppa-settings-autosave.php:3657
msgid "Auto page"
msgstr ""

#: wppa-settings-autosave.php:3658
msgid "Create a wp page for every fullsize image."
msgstr ""

#: wppa-settings-autosave.php:3662
msgid "Please reload this page after changing!"
msgstr ""

#: wppa-settings-autosave.php:3668
msgid "Auto page display"
msgstr ""

#: wppa-settings-autosave.php:3669
msgid "The type of display on the autopage pages."
msgstr ""

#: wppa-settings-autosave.php:3672
msgid "Single photo"
msgstr ""

#: wppa-settings-autosave.php:3672
msgid "Media type photo"
msgstr ""

#: wppa-settings-autosave.php:3672
msgid "In the style of a slideshow"
msgstr ""

#: wppa-settings-autosave.php:3679
msgid "Auto page links"
msgstr ""

#: wppa-settings-autosave.php:3680
msgid "The location for the pagelinks."
msgstr ""

#: wppa-settings-autosave.php:3683
msgid "At top and bottom"
msgstr ""

#: wppa-settings-autosave.php:3690
msgid "Defer javascript"
msgstr ""

#: wppa-settings-autosave.php:3691
msgid "Put javascript near the end of the page."
msgstr ""

#: wppa-settings-autosave.php:3692
msgid ""
"If checkd: May fix layout problems and broken slideshows. May speed up or "
"slow down page appearing."
msgstr ""

#: wppa-settings-autosave.php:3699
msgid "Inline styles"
msgstr ""

#: wppa-settings-autosave.php:3700
msgid "Set style specifications inline."
msgstr ""

#: wppa-settings-autosave.php:3701
msgid "If checked: May fix layout problems, but slows down page appearing."
msgstr ""

#: wppa-settings-autosave.php:3708
msgid "Custom style"
msgstr ""

#: wppa-settings-autosave.php:3709
msgid "Enter custom style specs here."
msgstr ""

#: wppa-settings-autosave.php:3717
msgid "Use customized style file"
msgstr ""

#: wppa-settings-autosave.php:3718 wppa-settings-autosave.php:3727
msgid "This feature is highly discouraged."
msgstr ""

#: wppa-settings-autosave.php:3726
msgid "Use customized theme file"
msgstr ""

#: wppa-settings-autosave.php:3735
msgid "Enable photo html access"
msgstr ""

#: wppa-settings-autosave.php:3736
msgid ""
"Creates .htaccess files in .../uploads/wppa/ and .../uploads/wppa/thumbs/"
msgstr ""

#: wppa-settings-autosave.php:3739
msgid "create 'all access' .htaccess files"
msgstr ""

#: wppa-settings-autosave.php:3740
msgid "remove .htaccess files"
msgstr ""

#: wppa-settings-autosave.php:3741
msgid "create 'no hotlinking' .htaccess files"
msgstr ""

#: wppa-settings-autosave.php:3742
msgid "do not change existing .htaccess file(s)"
msgstr ""

#: wppa-settings-autosave.php:3754
msgid "Lazy or HTML comp"
msgstr ""

#: wppa-settings-autosave.php:3755
msgid "Tick this box when you use lazy load or html compression."
msgstr ""

#: wppa-settings-autosave.php:3756
msgid ""
"If the filmstrip images do not show up and you have a lazy load or html "
"optimizing plugin active: Check this box"
msgstr ""

#: wppa-settings-autosave.php:3763
msgid "Thumbs first"
msgstr ""

#: wppa-settings-autosave.php:3764
msgid "When displaying album content: thumbnails before subalbums."
msgstr ""

#: wppa-settings-autosave.php:3772
msgid "Login links"
msgstr ""

#: wppa-settings-autosave.php:3773
msgid "You must login to... links to login page."
msgstr ""

#: wppa-settings-autosave.php:3781
msgid "Enable Video"
msgstr ""

#: wppa-settings-autosave.php:3782
msgid "Enables video support."
msgstr ""

#: wppa-settings-autosave.php:3791
msgid "Enable Audio"
msgstr ""

#: wppa-settings-autosave.php:3792
msgid "Enables audio support."
msgstr ""

#: wppa-settings-autosave.php:3800
msgid "Enable 3D Stereo"
msgstr ""

#: wppa-settings-autosave.php:3801
msgid "Enables 3D stereo photo support."
msgstr ""

#: wppa-settings-autosave.php:3809
msgid "Relative urls"
msgstr ""

#: wppa-settings-autosave.php:3810
msgid "Use relative urls only."
msgstr ""

#: wppa-settings-autosave.php:3818
msgid "Capitalize tags and cats"
msgstr ""

#: wppa-settings-autosave.php:3819
msgid "Format tags and cats to start with one capital character"
msgstr ""

#: wppa-settings-autosave.php:3827
msgid "Enable Admins Choice"
msgstr ""

#: wppa-settings-autosave.php:3828
msgid "Enable the creation of zipfiles with selected photos."
msgstr ""

#: wppa-settings-autosave.php:3829
msgid "Activate the Admins Choice widget to make the zipfiles downloadable."
msgstr ""

#: wppa-settings-autosave.php:3836
msgid "Make owner like photoname"
msgstr ""

#: wppa-settings-autosave.php:3837
msgid "Change the owner to the user who's display name equals photoname."
msgstr ""

#: wppa-settings-autosave.php:3845
msgid "JS and CSS when needed"
msgstr ""

#: wppa-settings-autosave.php:3846
msgid "Loads .js and .css files only when they are used on the page."
msgstr ""

#: wppa-settings-autosave.php:3847
msgid ""
"This is a self learning system. The first time a page is loaded that "
"requires wppa .css or .js files, the page will reload."
msgstr ""

#: wppa-settings-autosave.php:3854
msgid "Enable pdf"
msgstr ""

#: wppa-settings-autosave.php:3855
msgid "Enable the support of pdf files"
msgstr ""

#: wppa-settings-autosave.php:3856
msgid "This feature requires the activation of ImageMagick. See Table IX-K7"
msgstr ""

#: wppa-settings-autosave.php:3864
msgid "Slideshow related settings"
msgstr ""

#: wppa-settings-autosave.php:3866
msgid "V align"
msgstr ""

#: wppa-settings-autosave.php:3867
msgid "Vertical alignment of slideshow images."
msgstr ""

#: wppa-settings-autosave.php:3868
msgid "Specify the vertical alignment of slideshow images."
msgstr ""

#: wppa-settings-autosave.php:3869
msgid ""
"If you select --- none ---, the photos will not be centered horizontally "
"either."
msgstr ""

#: wppa-settings-autosave.php:3871 wppa-settings-autosave.php:4142
#: wppa-slideshow-widget.php:242
msgid "top"
msgstr ""

#: wppa-settings-autosave.php:3871 wppa-settings-autosave.php:4142
#: wppa-slideshow-widget.php:244
msgid "bottom"
msgstr ""

#: wppa-settings-autosave.php:3871 wppa-slideshow-widget.php:245
msgid "fit"
msgstr ""

#: wppa-settings-autosave.php:3879
msgid "H align"
msgstr ""

#: wppa-settings-autosave.php:3880
msgid "Horizontal alignment of slideshow images."
msgstr ""

#: wppa-settings-autosave.php:3881
msgid ""
"Specify the horizontal alignment of slideshow images. If you specify --- "
"none --- , no horizontal alignment will take place."
msgstr ""

#: wppa-settings-autosave.php:3882
msgid ""
"This setting is only usefull when the Column Width differs from the Maximum "
"Width."
msgstr ""

#: wppa-settings-autosave.php:3883
msgid "(Settings I-A1 and I-B1)"
msgstr ""

#: wppa-settings-autosave.php:3893
msgid "Start slideshow running."
msgstr ""

#: wppa-settings-autosave.php:3894
msgid ""
"If you select \"running\", the slideshow will start running immediately, if "
"you select \"still at first photo\", the first photo will be displayed in "
"browse mode."
msgstr ""

#: wppa-settings-autosave.php:3895
msgid ""
"If you select \"still at first norated\", the first photo that the visitor "
"did not gave a rating will be displayed in browse mode."
msgstr ""

#: wppa-settings-autosave.php:3897
msgid "running"
msgstr ""

#: wppa-settings-autosave.php:3898
msgid "still at first photo"
msgstr ""

#: wppa-settings-autosave.php:3899
msgid "still at first norated"
msgstr ""

#: wppa-settings-autosave.php:3910
msgid "Start slideonly"
msgstr ""

#: wppa-settings-autosave.php:3911
msgid "Start slideonly slideshow running."
msgstr ""

#: wppa-settings-autosave.php:3919 wppa-settings-autosave.php:4862
msgid "Video autostart"
msgstr ""

#: wppa-settings-autosave.php:3920
msgid "Autoplay videos in slideshows."
msgstr ""

#: wppa-settings-autosave.php:3929 wppa-settings-autosave.php:4871
msgid "Audio autostart"
msgstr ""

#: wppa-settings-autosave.php:3930
msgid "Autoplay audios in slideshows."
msgstr ""

#: wppa-settings-autosave.php:3938
msgid "Animation type"
msgstr ""

#: wppa-settings-autosave.php:3939
msgid "The way successive slides appear."
msgstr ""

#: wppa-settings-autosave.php:3940
msgid ""
"Select the way the old slide is to be replaced by the new one in the "
"slideshow/browse fullsize display."
msgstr ""

#: wppa-settings-autosave.php:3942
msgid "Fade out and in simultaneous"
msgstr ""

#: wppa-settings-autosave.php:3943
msgid "Fade in after fade out"
msgstr ""

#: wppa-settings-autosave.php:3944
msgid "Shift adjacent"
msgstr ""

#: wppa-settings-autosave.php:3945
msgid "Stack on"
msgstr ""

#: wppa-settings-autosave.php:3946
msgid "Stack off"
msgstr ""

#: wppa-settings-autosave.php:3947
msgid "Turn over"
msgstr ""

#: wppa-settings-autosave.php:3961
msgid "Timeout"
msgstr ""

#: wppa-settings-autosave.php:3962
msgid "Slideshow timeout."
msgstr ""

#: wppa-settings-autosave.php:3963
msgid ""
"Select the time a single slide will be visible when the slideshow is started."
msgstr ""

#: wppa-settings-autosave.php:3972
msgid "Speed"
msgstr ""

#: wppa-settings-autosave.php:3973
msgid "Slideshow animation speed."
msgstr ""

#: wppa-settings-autosave.php:3974
msgid "Specify the animation speed to be used in slideshows."
msgstr ""

#: wppa-settings-autosave.php:3975
msgid "This is the time it takes a photo to fade in or out."
msgstr ""

#: wppa-settings-autosave.php:3977 wppa-settings-autosave.php:4369
#: wppa-settings-autosave.php:4808 wppa-settings-autosave.php:6839
#: wppa-settings-autosave.php:8127 wppa-settings-autosave.php:8138
#: wppa-settings-autosave.php:8149 wppa-settings-autosave.php:8160
#: wppa-settings-autosave.php:8329
msgid "--- off ---"
msgstr ""

#: wppa-settings-autosave.php:3984
msgid "Slide hover pause"
msgstr ""

#: wppa-settings-autosave.php:3985
msgid "Running Slideshow suspends during mouse hover."
msgstr ""

#: wppa-settings-autosave.php:3993
msgid "Slideshow wrap around"
msgstr ""

#: wppa-settings-autosave.php:3994
msgid "The slideshow wraps around the start and end"
msgstr ""

#: wppa-settings-autosave.php:4002
msgid "Full desc align"
msgstr ""

#: wppa-settings-autosave.php:4003
msgid "The alignment of the descriptions under fullsize images and slideshows."
msgstr ""

#: wppa-settings-autosave.php:4006 wppa-settings-autosave.php:4130
#: wppa-settings-autosave.php:4248
msgid "Left"
msgstr ""

#: wppa-settings-autosave.php:4006
msgid "Center"
msgstr ""

#: wppa-settings-autosave.php:4006 wppa-settings-autosave.php:4130
#: wppa-settings-autosave.php:4248
msgid "Right"
msgstr ""

#: wppa-settings-autosave.php:4013
msgid "Remove redundant space"
msgstr ""

#: wppa-settings-autosave.php:4014
msgid "Removes unwanted &lt;p> and &lt;br> tags in fullsize descriptions."
msgstr ""

#: wppa-settings-autosave.php:4015
msgid ""
"This setting has only effect when Table IX-A7 (foreign shortcodes) is "
"checked."
msgstr ""

#: wppa-settings-autosave.php:4022 wppa-settings-autosave.php:4187
#: wppa-settings-autosave.php:4317
msgid "Run nl2br or wpautop on description"
msgstr ""

#: wppa-settings-autosave.php:4023
msgid "Adds &lt;br> or &lt;p> and &lt;br> tags in fullsize descriptions."
msgstr ""

#: wppa-settings-autosave.php:4027 wppa-settings-autosave.php:4192
#: wppa-settings-autosave.php:4322
msgid "Linebreaks only"
msgstr ""

#: wppa-settings-autosave.php:4028 wppa-settings-autosave.php:4193
#: wppa-settings-autosave.php:4323
msgid "Linebreaks and paragraphs"
msgstr ""

#: wppa-settings-autosave.php:4036
msgid "Auto open comments"
msgstr ""

#: wppa-settings-autosave.php:4037
msgid "Automatic opens comments box when slideshow does not run."
msgstr ""

#: wppa-settings-autosave.php:4045
msgid "Film hover goto"
msgstr ""

#: wppa-settings-autosave.php:4046
msgid "Go to slide when hovering filmstrip thumbnail."
msgstr ""

#: wppa-settings-autosave.php:4047
msgid "Do not use this setting when slides have different aspect ratios!"
msgstr ""

#: wppa-settings-autosave.php:4054
msgid "Slide swipe"
msgstr ""

#: wppa-settings-autosave.php:4055
msgid "Enable touch events swipe left-right on slides on touch screens."
msgstr ""

#: wppa-settings-autosave.php:4063
msgid "Slide page Ajax"
msgstr ""

#: wppa-settings-autosave.php:4064
msgid "Pagelinks slideshow use Ajax"
msgstr ""

#: wppa-settings-autosave.php:4065
msgid "On some systems you need to disable ajax here."
msgstr ""

#: wppa-settings-autosave.php:4073
msgid "Thumbnail related settings"
msgstr ""

#: wppa-settings-autosave.php:4076
msgid "Photo ordering sequence method."
msgstr ""

#: wppa-settings-autosave.php:4077
msgid ""
"Specify the way the photos should be ordered. This is the default setting. "
"You can overrule the default sorting order on a per album basis."
msgstr ""

#: wppa-settings-autosave.php:4114
msgid "Thumbnail type"
msgstr ""

#: wppa-settings-autosave.php:4115
msgid "The way the thumbnail images are displayed."
msgstr ""

#: wppa-settings-autosave.php:4116
msgid ""
"You may select an altenative display method for thumbnails. Note that some "
"of the thumbnail settings do not apply to all available display methods."
msgstr ""

#: wppa-settings-autosave.php:4118
msgid "like album covers"
msgstr ""

#: wppa-settings-autosave.php:4118
msgid "like album covers mcr"
msgstr ""

#: wppa-settings-autosave.php:4118
msgid "masonry style columns"
msgstr ""

#: wppa-settings-autosave.php:4118
msgid "masonry style rows"
msgstr ""

#: wppa-settings-autosave.php:4126 wppa-settings-autosave.php:4242
msgid "Placement"
msgstr ""

#: wppa-settings-autosave.php:4127
msgid "Thumbnail image left or right."
msgstr ""

#: wppa-settings-autosave.php:4128
msgid "Indicate the placement position of the thumbnailphoto you wish."
msgstr ""

#: wppa-settings-autosave.php:4137 wppa-slideshow-widget.php:256
msgid "Vertical alignment"
msgstr ""

#: wppa-settings-autosave.php:4138
msgid "Vertical alignment of thumbnails."
msgstr ""

#: wppa-settings-autosave.php:4139
msgid ""
"Specify the vertical alignment of thumbnail images. Use this setting when "
"albums contain both portrait and landscape photos."
msgstr ""

#: wppa-settings-autosave.php:4140
msgid ""
"It is NOT recommended to use the value --- default ---; it will affect the "
"horizontal alignment also and is meant to be used with custom css."
msgstr ""

#: wppa-settings-autosave.php:4149
msgid "Thumb mouseover"
msgstr ""

#: wppa-settings-autosave.php:4150
msgid "Apply thumbnail mouseover effect."
msgstr ""

#: wppa-settings-autosave.php:4151
msgid "Check this box to use mouseover effect on thumbnail images."
msgstr ""

#: wppa-settings-autosave.php:4159
msgid "Thumb opacity"
msgstr ""

#: wppa-settings-autosave.php:4160 wppa-settings-autosave.php:4267
msgid "Initial opacity value."
msgstr ""

#: wppa-settings-autosave.php:4161 wppa-settings-autosave.php:4268
#: wppa-settings-autosave.php:4408
msgid "Enter percentage of opacity. 100% is opaque, 0% is transparant"
msgstr ""

#: wppa-settings-autosave.php:4163 wppa-settings-autosave.php:4270
#: wppa-settings-autosave.php:4411 wppa-settings-autosave.php:4788
msgid "%"
msgstr ""

#: wppa-settings-autosave.php:4168
msgid "Thumb popup"
msgstr ""

#: wppa-settings-autosave.php:4169
msgid "Use popup effect on thumbnail images."
msgstr ""

#: wppa-settings-autosave.php:4170
msgid "Thumbnails pop-up to a larger image when hovered."
msgstr ""

#: wppa-settings-autosave.php:4178
msgid "Align subtext"
msgstr ""

#: wppa-settings-autosave.php:4179
msgid "Set thumbnail subtext on equal height."
msgstr ""

#: wppa-settings-autosave.php:4188
msgid "Adds &lt;br> or &lt;p> and &lt;br> tags in thumbnail descriptions."
msgstr ""

#: wppa-settings-autosave.php:4202
msgid "Album and covers related settings"
msgstr ""

#: wppa-settings-autosave.php:4204
msgid "Album order"
msgstr ""

#: wppa-settings-autosave.php:4205
msgid "Album ordering sequence method."
msgstr ""

#: wppa-settings-autosave.php:4206
msgid "Specify the way the albums should be ordered."
msgstr ""

#: wppa-settings-autosave.php:4231
msgid "Default coverphoto selection"
msgstr ""

#: wppa-settings-autosave.php:4232
msgid "Default select cover photo method."
msgstr ""

#: wppa-settings-autosave.php:4233
msgid ""
"This is the initial value on album creation only. It can be overruled on the "
"edit album page."
msgstr ""

#: wppa-settings-autosave.php:4234
msgid "Random from album"
msgstr ""

#: wppa-settings-autosave.php:4234
msgid "Random featured from album"
msgstr ""

#: wppa-settings-autosave.php:4234
msgid "Most recently added to album"
msgstr ""

#: wppa-settings-autosave.php:4234
msgid "Random from album or any sub album"
msgstr ""

#: wppa-settings-autosave.php:4243
msgid "Cover image position."
msgstr ""

#: wppa-settings-autosave.php:4244
msgid ""
"Enter the position that you want to be used for the default album cover "
"selected in Table IV-D6."
msgstr ""

#: wppa-settings-autosave.php:4245
msgid ""
"For covertype Image Factory: left will be treated as top and right will be "
"treted as bottom."
msgstr ""

#: wppa-settings-autosave.php:4246
msgid ""
"For covertype Long Descriptions: top will be treated as left and bottom will "
"be treted as right."
msgstr ""

#: wppa-settings-autosave.php:4256
msgid "Cover mouseover"
msgstr ""

#: wppa-settings-autosave.php:4257
msgid "Apply coverphoto mouseover effect."
msgstr ""

#: wppa-settings-autosave.php:4258
msgid "Check this box to use mouseover effect on cover images."
msgstr ""

#: wppa-settings-autosave.php:4266
msgid "Cover opacity"
msgstr ""

#: wppa-settings-autosave.php:4275
msgid "Cover type"
msgstr ""

#: wppa-settings-autosave.php:4276
msgid "Select the default cover type."
msgstr ""

#: wppa-settings-autosave.php:4277
msgid ""
"Types with the addition mcr are suitable for Multi Column in a Responsive "
"theme"
msgstr ""

#: wppa-settings-autosave.php:4300
msgid "The umber of coverphotos. Must be > 1 and < 25."
msgstr ""

#: wppa-settings-autosave.php:4308
msgid "Cats include subs"
msgstr ""

#: wppa-settings-autosave.php:4309
msgid "Child albums are included in Category based shortcodes."
msgstr ""

#: wppa-settings-autosave.php:4310
msgid ""
"When you use album=\"#cat,...\", in a shortcode, the child albums will be "
"included."
msgstr ""

#: wppa-settings-autosave.php:4318
msgid "Adds &lt;br> or &lt;p> and &lt;br> tags in album descriptions."
msgstr ""

#: wppa-settings-autosave.php:4332
msgid "Rating related settings"
msgstr ""

#: wppa-settings-autosave.php:4334
msgid "Rating login"
msgstr ""

#: wppa-settings-autosave.php:4335
msgid "Users must login to rate photos."
msgstr ""

#: wppa-settings-autosave.php:4336
msgid ""
"If users want to vote for a photo (rating 1..5 stars) the must login first. "
"The avarage rating will always be displayed as long as the rating system is "
"enabled."
msgstr ""

#: wppa-settings-autosave.php:4343
msgid "Rating change"
msgstr ""

#: wppa-settings-autosave.php:4344 wppa-settings-autosave.php:4345
msgid "Users may change their ratings."
msgstr ""

#: wppa-settings-autosave.php:4346 wppa-settings-autosave.php:4409
#: wppa-settings-autosave.php:4422 wppa-settings-autosave.php:4432
#: wppa-settings-autosave.php:4442 wppa-settings-autosave.php:4452
#: wppa-settings-autosave.php:4462
msgid ""
"If \"One button vote\" is selected in Table I-E1, this setting has no meaning"
msgstr ""

#: wppa-settings-autosave.php:4354
msgid "Rating multi"
msgstr ""

#: wppa-settings-autosave.php:4355
msgid "Users may give multiple votes."
msgstr ""

#: wppa-settings-autosave.php:4356
msgid ""
"Users may give multiple votes. (This has no effect when users may change "
"their votes.)"
msgstr ""

#: wppa-settings-autosave.php:4365
msgid "Rating daily"
msgstr ""

#: wppa-settings-autosave.php:4366
msgid "Users may rate only once per period"
msgstr ""

#: wppa-settings-autosave.php:4369
msgid "Week"
msgstr ""

#: wppa-settings-autosave.php:4369
msgid "Day"
msgstr ""

#: wppa-settings-autosave.php:4369
msgid "Hour"
msgstr ""

#: wppa-settings-autosave.php:4377
msgid "Rate own photos"
msgstr ""

#: wppa-settings-autosave.php:4378
msgid "It is allowed to rate photos by the uploader himself."
msgstr ""

#: wppa-settings-autosave.php:4387
msgid "Rating requires comment"
msgstr ""

#: wppa-settings-autosave.php:4388
msgid "Users must clarify their vote in a comment."
msgstr ""

#: wppa-settings-autosave.php:4397
msgid "Next after vote"
msgstr ""

#: wppa-settings-autosave.php:4398
msgid "Goto next slide after voting"
msgstr ""

#: wppa-settings-autosave.php:4399
msgid ""
"If checked, the visitor goes straight to the slide following the slide he "
"voted. This will speed up mass voting."
msgstr ""

#: wppa-settings-autosave.php:4406
msgid "Star off opacity"
msgstr ""

#: wppa-settings-autosave.php:4407
msgid "Rating star off state opacity value."
msgstr ""

#: wppa-settings-autosave.php:4417
msgid "Notify admin every x times."
msgstr ""

#: wppa-settings-autosave.php:4418
msgid ""
"If this number is positive, there will be a thumb down icon in the rating "
"bar."
msgstr ""

#: wppa-settings-autosave.php:4419
msgid "Clicking the thumbdown icon indicates a user dislikes a photo."
msgstr ""

#: wppa-settings-autosave.php:4420
msgid "Admin will be notified by email after every x dislikes."
msgstr ""

#: wppa-settings-autosave.php:4421 wppa-settings-autosave.php:4441
#: wppa-settings-autosave.php:4451
msgid "A value of 0 disables this feature."
msgstr ""

#: wppa-settings-autosave.php:4424 wppa-settings-autosave.php:4444
#: wppa-settings-autosave.php:4454
msgid "reports"
msgstr ""

#: wppa-settings-autosave.php:4430
msgid "This value counts dislike rating."
msgstr ""

#: wppa-settings-autosave.php:4431
msgid ""
"This value will be used for a dislike rating on calculation of avarage "
"ratings."
msgstr ""

#: wppa-settings-autosave.php:4434
msgid "points"
msgstr ""

#: wppa-settings-autosave.php:4439
msgid "Pending after"
msgstr ""

#: wppa-settings-autosave.php:4440
msgid "Set status to pending after xx dislike votes."
msgstr ""

#: wppa-settings-autosave.php:4449
msgid "Delete after"
msgstr ""

#: wppa-settings-autosave.php:4450
msgid "Delete photo after xx dislike votes."
msgstr ""

#: wppa-settings-autosave.php:4459
msgid "Show dislike count"
msgstr ""

#: wppa-settings-autosave.php:4460
msgid "Show the number of dislikes in the rating bar."
msgstr ""

#: wppa-settings-autosave.php:4461
msgid "Displayes the total number of dislike votes for the current photo."
msgstr ""

#: wppa-settings-autosave.php:4469
msgid "Rating display type"
msgstr ""

#: wppa-settings-autosave.php:4470
msgid "Specify the type of the rating display."
msgstr ""

#: wppa-settings-autosave.php:4471
msgid ""
"If you select \"Likes\" you must also select \"One button vote\" in Table I-"
"E1"
msgstr ""

#: wppa-settings-autosave.php:4473
msgid "Graphic"
msgstr ""

#: wppa-settings-autosave.php:4473
msgid "Numeric"
msgstr ""

#: wppa-settings-autosave.php:4473
msgid "Likes"
msgstr ""

#: wppa-settings-autosave.php:4481
msgid "Show average rating"
msgstr ""

#: wppa-settings-autosave.php:4482
msgid "Display the avarage rating and/or vote count on the rating bar"
msgstr ""

#: wppa-settings-autosave.php:4483
msgid ""
"If checked, the average rating as well as the current users rating is "
"displayed in max 5 or 10 stars."
msgstr ""

#: wppa-settings-autosave.php:4484
msgid "If unchecked, only the current users rating is displayed (if any)."
msgstr ""

#: wppa-settings-autosave.php:4485
msgid ""
"If \"One button vote\" is selected in Table I-E1, this box checked will "
"display the vote count."
msgstr ""

#: wppa-settings-autosave.php:4492
msgid "Avg and Mine on 2 lines"
msgstr ""

#: wppa-settings-autosave.php:4493
msgid "Display avarage and my rating on different lines"
msgstr ""

#: wppa-settings-autosave.php:4501
msgid "Single vote button text"
msgstr ""

#: wppa-settings-autosave.php:4502
msgid "The text on the voting button."
msgstr ""

#: wppa-settings-autosave.php:4503 wppa-settings-autosave.php:4512
msgid "This text may contain qTranslate compatible language tags."
msgstr ""

#: wppa-settings-autosave.php:4510
msgid "Single vote button text voted"
msgstr ""

#: wppa-settings-autosave.php:4511
msgid "The text on the voting button when voted."
msgstr ""

#: wppa-settings-autosave.php:4519
msgid "Single vote button thumbnail"
msgstr ""

#: wppa-settings-autosave.php:4520
msgid "Display single vote button below thumbnails."
msgstr ""

#: wppa-settings-autosave.php:4521
msgid ""
"This works only in single vote mode: Table I-E1 set to \"one button vote\""
msgstr ""

#: wppa-settings-autosave.php:4528
msgid "Medal bronze when"
msgstr ""

#: wppa-settings-autosave.php:4529
msgid "Photo gets medal bronze when number of top-scores ( 5 or 10 )."
msgstr ""

#: wppa-settings-autosave.php:4530 wppa-settings-autosave.php:4539
#: wppa-settings-autosave.php:4548
msgid ""
"When the photo has this number of topscores ( 5 or 10 stars ), it will get a "
"medal. A value of 0 indicates that you do not want this feature."
msgstr ""

#: wppa-settings-autosave.php:4532 wppa-settings-autosave.php:4541
#: wppa-settings-autosave.php:4550
msgid "Topscores"
msgstr ""

#: wppa-settings-autosave.php:4537
msgid "Medal silver when"
msgstr ""

#: wppa-settings-autosave.php:4538
msgid "Photo gets medal silver when number of top-scores ( 5 or 10 )."
msgstr ""

#: wppa-settings-autosave.php:4546
msgid "Medal gold when"
msgstr ""

#: wppa-settings-autosave.php:4547
msgid "Photo gets medal gold when number of top-scores ( 5 or 10 )."
msgstr ""

#: wppa-settings-autosave.php:4555
msgid "Medal tag color"
msgstr ""

#: wppa-settings-autosave.php:4556
msgid "The color of the tag on the medal."
msgstr ""

#: wppa-settings-autosave.php:4559 wppa-settings-autosave.php:8412
msgid "Red"
msgstr ""

#: wppa-settings-autosave.php:4559 wppa-settings-autosave.php:8415
msgid "Green"
msgstr ""

#: wppa-settings-autosave.php:4559 wppa-settings-autosave.php:8416
msgid "Blue"
msgstr ""

#: wppa-settings-autosave.php:4566
msgid "Medal position"
msgstr ""

#: wppa-settings-autosave.php:4567
msgid "The position of the medal on the image."
msgstr ""

#: wppa-settings-autosave.php:4570
msgid "Top left"
msgstr ""

#: wppa-settings-autosave.php:4570
msgid "Top right"
msgstr ""

#: wppa-settings-autosave.php:4570
msgid "Bottom left"
msgstr ""

#: wppa-settings-autosave.php:4570
msgid "Bottom right"
msgstr ""

#: wppa-settings-autosave.php:4577
msgid "Top criterium"
msgstr ""

#: wppa-settings-autosave.php:4578
msgid "The top sort item used for topten results from shortcodes."
msgstr ""

#: wppa-settings-autosave.php:4581
msgid "Mean raiting"
msgstr ""

#: wppa-settings-autosave.php:4581 wppa-topten-widget.php:378
msgid "Rating count"
msgstr ""

#: wppa-settings-autosave.php:4581
msgid "Viewcount"
msgstr ""

#: wppa-settings-autosave.php:4588
msgid "Comments related settings"
msgstr ""

#: wppa-settings-autosave.php:4590
msgid "Commenting login"
msgstr ""

#: wppa-settings-autosave.php:4591
msgid "Users must be logged in to comment on photos."
msgstr ""

#: wppa-settings-autosave.php:4592
msgid ""
"Check this box if you want users to be logged in to be able to enter "
"comments on individual photos."
msgstr ""

#: wppa-settings-autosave.php:4599
msgid "Comments view login"
msgstr ""

#: wppa-settings-autosave.php:4600
msgid "Users must be logged in to see comments on photos."
msgstr ""

#: wppa-settings-autosave.php:4601
msgid ""
"Check this box if you want users to be logged in to be able to see existing "
"comments on individual photos."
msgstr ""

#: wppa-settings-autosave.php:4608
msgid "Last comment first"
msgstr ""

#: wppa-settings-autosave.php:4609
msgid "Display the newest comment on top."
msgstr ""

#: wppa-settings-autosave.php:4610
msgid "If checked: Display the newest comment on top."
msgstr ""

#: wppa-settings-autosave.php:4611
msgid "If unchecked, the comments are listed in the ordere they were entered."
msgstr ""

#: wppa-settings-autosave.php:4618
msgid "Comment moderation"
msgstr ""

#: wppa-settings-autosave.php:4619
msgid "Comments from what users need approval."
msgstr ""

#: wppa-settings-autosave.php:4620
msgid "Select the desired users of which the comments need approval."
msgstr ""

#: wppa-settings-autosave.php:4622 wppa-settings-autosave.php:6825
msgid "All users"
msgstr ""

#: wppa-settings-autosave.php:4623 wppa-settings-autosave.php:6825
msgid "Logged out users"
msgstr ""

#: wppa-settings-autosave.php:4624 wppa-settings-autosave.php:6825
msgid "No users"
msgstr ""

#: wppa-settings-autosave.php:4625
msgid "Use WP Discussion rules"
msgstr ""

#: wppa-settings-autosave.php:4637
msgid "Comment email required"
msgstr ""

#: wppa-settings-autosave.php:4638
msgid "Commenting users must enter their email addresses."
msgstr ""

#: wppa-settings-autosave.php:4642 wppa-settings-autosave.php:9956
#: wppa-settings-autosave.php:10024
msgid "Optional"
msgstr ""

#: wppa-settings-autosave.php:4654
msgid "Comment notify"
msgstr ""

#: wppa-settings-autosave.php:4655
msgid "Select who must receive an e-mail notification of a new comment."
msgstr ""

#: wppa-settings-autosave.php:4659
msgid "--- Admin ---"
msgstr ""

#: wppa-settings-autosave.php:4660
msgid "--- Album owner ---"
msgstr ""

#: wppa-settings-autosave.php:4661
msgid "--- Admin & Owner ---"
msgstr ""

#: wppa-settings-autosave.php:4662
msgid "--- Uploader ---"
msgstr ""

#: wppa-settings-autosave.php:4663
msgid "--- Up & admin ---"
msgstr ""

#: wppa-settings-autosave.php:4664
msgid "--- Up & Owner ---"
msgstr ""

#: wppa-settings-autosave.php:4687
msgid "Comment notify previous"
msgstr ""

#: wppa-settings-autosave.php:4688
msgid "Notify users who has commented this photo earlier."
msgstr ""

#: wppa-settings-autosave.php:4696
msgid "Comment notify approved"
msgstr ""

#: wppa-settings-autosave.php:4697
msgid "Notify photo owner of approved comment."
msgstr ""

#: wppa-settings-autosave.php:4705
msgid "Com ntfy appr email content"
msgstr ""

#: wppa-settings-autosave.php:4706
msgid "The content of the email."
msgstr ""

#: wppa-settings-autosave.php:4707
msgid "If you leave this blank, the default content will be used"
msgstr ""

#: wppa-settings-autosave.php:4708
msgid "The content may contain html."
msgstr ""

#: wppa-settings-autosave.php:4709
msgid ""
"You may use the following keywords: w#comment for the comment content, "
"w#user for the commenters name and the standard photo description keywords "
"w#name, w#filename, w#owner, w#id, w#tags, w#timestamp, w#modified, w#views, "
"w#amx, w#amy, w#amfs, w#url, w#hrurl, w#tnurl, w#cc0..w#cc9, w#cd0..w#cd9."
msgstr ""

#: wppa-settings-autosave.php:4716
msgid "Com ntfy appr email subject"
msgstr ""

#: wppa-settings-autosave.php:4717
msgid "The subject of the email."
msgstr ""

#: wppa-settings-autosave.php:4726
msgid "Comment ntfy added"
msgstr ""

#: wppa-settings-autosave.php:4727
msgid "Show \"Comment added\" after successfull adding a comment."
msgstr ""

#: wppa-settings-autosave.php:4735
msgid "ComTen alt display"
msgstr ""

#: wppa-settings-autosave.php:4736
msgid "Display comments at comten thumbnails."
msgstr ""

#: wppa-settings-autosave.php:4744
msgid "Comten Thumbnail width"
msgstr ""

#: wppa-settings-autosave.php:4745
msgid "The width of the thumbnail in the alt comment display."
msgstr ""

#: wppa-settings-autosave.php:4748
msgid "Pixels"
msgstr ""

#: wppa-settings-autosave.php:4753
msgid "Show smiley picker"
msgstr ""

#: wppa-settings-autosave.php:4754
msgid "Display a clickable row of smileys."
msgstr ""

#: wppa-settings-autosave.php:4762
msgid "Show commenter email"
msgstr ""

#: wppa-settings-autosave.php:4763
msgid "Show the commenter's email in the notify emails."
msgstr ""

#: wppa-settings-autosave.php:4764
msgid "Shows the email address of the commenter in all notify emails."
msgstr ""

#: wppa-settings-autosave.php:4765
msgid ""
"If switched off, admin will still receive the senders email in the "
"notification mail"
msgstr ""

#: wppa-settings-autosave.php:4772
msgid "Allow clickable links"
msgstr ""

#: wppa-settings-autosave.php:4773
msgid "Make links in comments clickable"
msgstr ""

#: wppa-settings-autosave.php:4785
msgid "The opacity of the lightbox overlay background."
msgstr ""

#: wppa-settings-autosave.php:4793
msgid "Click on background"
msgstr ""

#: wppa-settings-autosave.php:4794
msgid "Select the action to be taken on click on background."
msgstr ""

#: wppa-settings-autosave.php:4797
msgid "Nothing"
msgstr ""

#: wppa-settings-autosave.php:4797
msgid "Exit (close)"
msgstr ""

#: wppa-settings-autosave.php:4797
msgid "Browse (left/right)"
msgstr ""

#: wppa-settings-autosave.php:4804
msgid "Overlay animation speed"
msgstr ""

#: wppa-settings-autosave.php:4805
msgid "The fade-in time of the lightbox images"
msgstr ""

#: wppa-settings-autosave.php:4815
msgid "Overlay slideshow speed"
msgstr ""

#: wppa-settings-autosave.php:4816
msgid "The time the lightbox images stay"
msgstr ""

#: wppa-settings-autosave.php:4835
msgid "WPPA+ Lightbox global"
msgstr ""

#: wppa-settings-autosave.php:4836
msgid "Use the wppa+ lightbox also for non-wppa images."
msgstr ""

#: wppa-settings-autosave.php:4844
msgid "WPPA+ Lightbox global is a set"
msgstr ""

#: wppa-settings-autosave.php:4845
msgid "Treat the other images as a set."
msgstr ""

#: wppa-settings-autosave.php:4846
msgid ""
"If checked, you can scroll through the images in the lightbox view. Requires "
"item 5 to be checked."
msgstr ""

#: wppa-settings-autosave.php:4853
msgid "Use hires files"
msgstr ""

#: wppa-settings-autosave.php:4854
msgid "Use the highest resolution available for lightbox."
msgstr ""

#: wppa-settings-autosave.php:4855
msgid "Ticking this box is recommended for lightbox fullscreen modes."
msgstr ""

#: wppa-settings-autosave.php:4863
msgid "Videos on lightbox start automatically."
msgstr ""

#: wppa-settings-autosave.php:4872
msgid "Audio on lightbox start automatically."
msgstr ""

#: wppa-settings-autosave.php:4880
msgid "Lightbox start mode"
msgstr ""

#: wppa-settings-autosave.php:4881
msgid "The mode lightbox starts in."
msgstr ""

#: wppa-settings-autosave.php:4884 wppa-settings-autosave.php:4905
msgid "Normal"
msgstr ""

#: wppa-settings-autosave.php:4885 wppa-settings-autosave.php:4906
msgid "Padded"
msgstr ""

#: wppa-settings-autosave.php:4886 wppa-settings-autosave.php:4907
msgid "Stretched"
msgstr ""

#: wppa-settings-autosave.php:4887 wppa-settings-autosave.php:4908
msgid "Clipped"
msgstr ""

#: wppa-settings-autosave.php:4888 wppa-settings-autosave.php:4909
msgid "Real size"
msgstr ""

#: wppa-settings-autosave.php:4901
msgid "Lightbox start mode mobile"
msgstr ""

#: wppa-settings-autosave.php:4902
msgid "The mode lightbox starts in on mobile devices."
msgstr ""

#: wppa-settings-autosave.php:4939
msgid "Table V:"
msgstr ""

#: wppa-settings-autosave.php:4939
msgid "Fonts:"
msgstr ""

#: wppa-settings-autosave.php:4940
msgid "This table describes the Fonts used for the wppa+ elements."
msgstr ""

#: wppa-settings-autosave.php:4950 wppa-settings-autosave.php:5145
msgid "Font family"
msgstr ""

#: wppa-settings-autosave.php:4951 wppa-settings-autosave.php:5146
msgid "Font size"
msgstr ""

#: wppa-settings-autosave.php:4952 wppa-settings-autosave.php:5147
msgid "Font color"
msgstr ""

#: wppa-settings-autosave.php:4953 wppa-settings-autosave.php:5148
msgid "Font weight"
msgstr ""

#: wppa-settings-autosave.php:4963
msgid "normal"
msgstr ""

#: wppa-settings-autosave.php:4963
msgid "bold"
msgstr ""

#: wppa-settings-autosave.php:4963
msgid "bolder"
msgstr ""

#: wppa-settings-autosave.php:4963
msgid "lighter"
msgstr ""

#: wppa-settings-autosave.php:4966
msgid "Album titles"
msgstr ""

#: wppa-settings-autosave.php:4967
msgid "Font used for Album titles."
msgstr ""

#: wppa-settings-autosave.php:4968
msgid "Enter font name, size, color and weight for album cover titles."
msgstr ""

#: wppa-settings-autosave.php:4983
msgid "Slideshow desc"
msgstr ""

#: wppa-settings-autosave.php:4984
msgid "Font for slideshow photo descriptions."
msgstr ""

#: wppa-settings-autosave.php:4985
msgid ""
"Enter font name, size, color and weight for slideshow photo descriptions."
msgstr ""

#: wppa-settings-autosave.php:5000
msgid "Slideshow name"
msgstr ""

#: wppa-settings-autosave.php:5001
msgid "Font for slideshow photo names."
msgstr ""

#: wppa-settings-autosave.php:5002
msgid "Enter font name, size, color and weight for slideshow photo names."
msgstr ""

#: wppa-settings-autosave.php:5017
msgid "Navigations"
msgstr ""

#: wppa-settings-autosave.php:5018
msgid "Font for navigations."
msgstr ""

#: wppa-settings-autosave.php:5019
msgid "Enter font name, size, color and weight for navigation items."
msgstr ""

#: wppa-settings-autosave.php:5035
msgid "Font for text under thumbnails."
msgstr ""

#: wppa-settings-autosave.php:5036
msgid ""
"Enter font name, size, color and weight for text under thumbnail images."
msgstr ""

#: wppa-settings-autosave.php:5052
msgid "General font in wppa boxes."
msgstr ""

#: wppa-settings-autosave.php:5053
msgid "Enter font name, size, color and weight for all other items."
msgstr ""

#: wppa-settings-autosave.php:5069
msgid "Font in wppa number bars."
msgstr ""

#: wppa-settings-autosave.php:5070 wppa-settings-autosave.php:5087
msgid "Enter font name, size, color and weight for numberbar navigation."
msgstr ""

#: wppa-settings-autosave.php:5085
msgid "Numbar Active"
msgstr ""

#: wppa-settings-autosave.php:5086
msgid "Font in wppa number bars, active item."
msgstr ""

#: wppa-settings-autosave.php:5103
msgid "Font in wppa lightbox overlays."
msgstr ""

#: wppa-settings-autosave.php:5104
msgid "Enter font name, size, color and weight for wppa lightbox overlays."
msgstr ""

#: wppa-settings-autosave.php:5119
msgid "Widget thumbs fontsize"
msgstr ""

#: wppa-settings-autosave.php:5120
msgid "Font size for thumbnail subtext in widgets."
msgstr ""

#: wppa-settings-autosave.php:5158
msgid "Table VI:"
msgstr ""

#: wppa-settings-autosave.php:5158
msgid "Links:"
msgstr ""

#: wppa-settings-autosave.php:5159
msgid "This table defines the link types and pages."
msgstr ""

#: wppa-settings-autosave.php:5170 wppa-settings-autosave.php:6233
msgid "Link page"
msgstr ""

#: wppa-settings-autosave.php:5172 wppa-settings-autosave.php:6235
msgid "Photo specific link overrules"
msgstr ""

#: wppa-settings-autosave.php:5172 wppa-settings-autosave.php:6235
msgid "PSO"
msgstr ""

#: wppa-settings-autosave.php:5217
msgid "Links from images in WPPA+ Widgets"
msgstr ""

#: wppa-settings-autosave.php:5219
msgid "PotdWidget"
msgstr ""

#: wppa-settings-autosave.php:5220
msgid "Photo Of The Day widget link."
msgstr ""

#: wppa-settings-autosave.php:5221
msgid "Select the type of link the photo of the day points to."
msgstr ""

#: wppa-settings-autosave.php:5222
msgid ""
"If you select 'defined on widget admin page' you can manually enter a link "
"and title on the Photo of the day Widget Admin page."
msgstr ""

#: wppa-settings-autosave.php:5237 wppa-settings-autosave.php:5280
#: wppa-settings-autosave.php:5319 wppa-settings-autosave.php:5358
#: wppa-settings-autosave.php:5404 wppa-settings-autosave.php:5454
#: wppa-settings-autosave.php:5503 wppa-settings-autosave.php:5552
#: wppa-settings-autosave.php:5642
msgid "a plain page without a querystring."
msgstr ""

#: wppa-settings-autosave.php:5238 wppa-settings-autosave.php:5281
#: wppa-settings-autosave.php:5320 wppa-settings-autosave.php:5359
#: wppa-settings-autosave.php:5405 wppa-settings-autosave.php:5455
#: wppa-settings-autosave.php:5504 wppa-settings-autosave.php:5553
#: wppa-settings-autosave.php:5600 wppa-settings-autosave.php:5643
#: wppa-settings-autosave.php:5692 wppa-settings-autosave.php:5734
#: wppa-settings-autosave.php:5776 wppa-settings-autosave.php:5815
msgid "lightbox."
msgstr ""

#: wppa-settings-autosave.php:5263
msgid "SlideWidget"
msgstr ""

#: wppa-settings-autosave.php:5264
msgid "Slideshow widget photo link."
msgstr ""

#: wppa-settings-autosave.php:5265
msgid "Select the type of link the slideshow photos point to."
msgstr ""

#: wppa-settings-autosave.php:5306
msgid "Album widget"
msgstr ""

#: wppa-settings-autosave.php:5307
msgid "Album widget thumbnail link"
msgstr ""

#: wppa-settings-autosave.php:5308
msgid "Select the type of link the album widget photos point to."
msgstr ""

#: wppa-settings-autosave.php:5317
msgid "subalbums and thumbnails."
msgstr ""

#: wppa-settings-autosave.php:5318
msgid "slideshow."
msgstr ""

#: wppa-settings-autosave.php:5341
msgid "ThumbnailWidget"
msgstr ""

#: wppa-settings-autosave.php:5342
msgid "Thumbnail widget photo link."
msgstr ""

#: wppa-settings-autosave.php:5343
msgid "Select the type of link the thumbnail photos point to."
msgstr ""

#: wppa-settings-autosave.php:5356 wppa-settings-autosave.php:5402
#: wppa-settings-autosave.php:5452 wppa-settings-autosave.php:5501
#: wppa-settings-autosave.php:5550 wppa-settings-autosave.php:5640
msgid "the single photo in the style of a slideshow."
msgstr ""

#: wppa-settings-autosave.php:5357 wppa-settings-autosave.php:5403
#: wppa-settings-autosave.php:5453 wppa-settings-autosave.php:5502
#: wppa-settings-autosave.php:5551 wppa-settings-autosave.php:5641
#: wppa-settings-autosave.php:5817
msgid "the fs photo with download and print buttons."
msgstr ""

#: wppa-settings-autosave.php:5384
msgid "TopTenWidget"
msgstr ""

#: wppa-settings-autosave.php:5385
msgid "TopTen widget photo link."
msgstr ""

#: wppa-settings-autosave.php:5386
msgid "Select the type of link the top ten photos point to."
msgstr ""

#: wppa-settings-autosave.php:5397
msgid "the content of the virtual topten album."
msgstr ""

#: wppa-settings-autosave.php:5398 wppa-settings-autosave.php:5448
#: wppa-settings-autosave.php:5497 wppa-settings-autosave.php:5546
msgid "the content of the thumbnails album."
msgstr ""

#: wppa-settings-autosave.php:5400 wppa-settings-autosave.php:5450
#: wppa-settings-autosave.php:5499 wppa-settings-autosave.php:5548
#: wppa-settings-autosave.php:5638
msgid "the thumbnails album in a slideshow."
msgstr ""

#: wppa-settings-autosave.php:5434
msgid "LasTenWidget"
msgstr ""

#: wppa-settings-autosave.php:5435
msgid "Last Ten widget photo link."
msgstr ""

#: wppa-settings-autosave.php:5436
msgid "Select the type of link the last ten photos point to."
msgstr ""

#: wppa-settings-autosave.php:5447
msgid "the content of the virtual lasten album."
msgstr ""

#: wppa-settings-autosave.php:5483
msgid "CommentWidget"
msgstr ""

#: wppa-settings-autosave.php:5484
msgid "Comment widget photo link."
msgstr ""

#: wppa-settings-autosave.php:5485
msgid "Select the type of link the comment widget photos point to."
msgstr ""

#: wppa-settings-autosave.php:5496
msgid "the content of the virtual comten album."
msgstr ""

#: wppa-settings-autosave.php:5532
msgid "FeaTenWidget"
msgstr ""

#: wppa-settings-autosave.php:5533
msgid "FeaTen widget photo link."
msgstr ""

#: wppa-settings-autosave.php:5534
msgid "Select the type of link the featured ten photos point to."
msgstr ""

#: wppa-settings-autosave.php:5545
msgid "the content of the virtual featen album."
msgstr ""

#: wppa-settings-autosave.php:5580
msgid "Links from other WPPA+ images"
msgstr ""

#: wppa-settings-autosave.php:5582
msgid "Cover Image"
msgstr ""

#: wppa-settings-autosave.php:5583
msgid "The link from the cover image of an album."
msgstr ""

#: wppa-settings-autosave.php:5584
msgid "Select the type of link the coverphoto points to."
msgstr ""

#: wppa-settings-autosave.php:5585
msgid "The link from the album title can be configured on the Edit Album page."
msgstr ""

#: wppa-settings-autosave.php:5586
msgid "This link will be used for the photo also if you select: same as title."
msgstr ""

#: wppa-settings-autosave.php:5587
msgid ""
"If you specify New Tab on this line, all links from the cover will open a "
"new tab,"
msgstr ""

#: wppa-settings-autosave.php:5588
msgid "except when Ajax is activated on Table IV-A1."
msgstr ""

#: wppa-settings-autosave.php:5601
msgid "a slideshow starting at the photo"
msgstr ""

#: wppa-settings-autosave.php:5622
msgid "Thumbnail"
msgstr ""

#: wppa-settings-autosave.php:5623
msgid "Thumbnail link."
msgstr ""

#: wppa-settings-autosave.php:5624 wppa-settings-autosave.php:5676
#: wppa-settings-autosave.php:5718
msgid "Select the type of link you want, or no link at all."
msgstr ""

#: wppa-settings-autosave.php:5625 wppa-settings-autosave.php:5677
#: wppa-settings-autosave.php:5719 wppa-settings-autosave.php:5761
msgid ""
"If you select the fullsize photo on its own, it will be stretched to fit, "
"regardless of that setting."
msgstr ""

#: wppa-settings-autosave.php:5626 wppa-settings-autosave.php:5678
#: wppa-settings-autosave.php:5720 wppa-settings-autosave.php:5762
msgid ""
"Note that a page must have at least [wppa][/wppa] in its content to show up "
"the photo(s)."
msgstr ""

#: wppa-settings-autosave.php:5657
msgid "Auto Page"
msgstr ""

#: wppa-settings-autosave.php:5674
msgid "Sphoto"
msgstr ""

#: wppa-settings-autosave.php:5675
msgid "Single photo link."
msgstr ""

#: wppa-settings-autosave.php:5716
msgid "Mphoto"
msgstr ""

#: wppa-settings-autosave.php:5717
msgid "Media-like (like WP photo with caption) photo link."
msgstr ""

#: wppa-settings-autosave.php:5758
msgid "Xphoto"
msgstr ""

#: wppa-settings-autosave.php:5759
msgid "Media-like (like WP photo with - extended - caption) photo link."
msgstr ""

#: wppa-settings-autosave.php:5760
msgid ""
"Select the type of link you want, or no link at all, to act on a photo in "
"the style of s wp photo with - an extended - caption."
msgstr ""

#: wppa-settings-autosave.php:5801
msgid "Slideshow fullsize link"
msgstr ""

#: wppa-settings-autosave.php:5802
msgid ""
"You can overrule lightbox but not big browse buttons with the photo specifc "
"link."
msgstr ""

#: wppa-settings-autosave.php:5803
msgid "fullsize slideshow can only be set by the WPPA_SET shortcode."
msgstr ""

#: wppa-settings-autosave.php:5816
msgid "lightbox single photos."
msgstr ""

#: wppa-settings-autosave.php:5818
msgid "the thumbnails."
msgstr ""

#: wppa-settings-autosave.php:5819
msgid "fullsize slideshow"
msgstr ""

#: wppa-settings-autosave.php:5844
msgid "Film linktype"
msgstr ""

#: wppa-settings-autosave.php:5845
msgid "Direct access goto image in:"
msgstr ""

#: wppa-settings-autosave.php:5846
msgid ""
"Select the action to be taken when the user clicks on a filmstrip image."
msgstr ""

#: wppa-settings-autosave.php:5851
msgid "slideshow window"
msgstr ""

#: wppa-settings-autosave.php:5852
msgid "lightbox overlay"
msgstr ""

#: wppa-settings-autosave.php:5867
msgid "Other links"
msgstr ""

#: wppa-settings-autosave.php:5869
msgid "Download Link (aka Art Monkey link)"
msgstr ""

#: wppa-settings-autosave.php:5870
msgid "Makes the photo name a download button."
msgstr ""

#: wppa-settings-autosave.php:5871
msgid "Link Photo name in slideshow to file or zip with photoname as filename."
msgstr ""

#: wppa-settings-autosave.php:5875 wppa-settings-autosave.php:5922
msgid "image file"
msgstr ""

#: wppa-settings-autosave.php:5876 wppa-settings-autosave.php:5923
msgid "zipped image"
msgstr ""

#: wppa-settings-autosave.php:5889
msgid "Art Monkey Source"
msgstr ""

#: wppa-settings-autosave.php:5890
msgid "Use Source file for art monkey link if available."
msgstr ""

#: wppa-settings-autosave.php:5899
msgid "Art Monkey Display"
msgstr ""

#: wppa-settings-autosave.php:5900
msgid "Select button or link ( text )."
msgstr ""

#: wppa-settings-autosave.php:5905
msgid "Textlink"
msgstr ""

#: wppa-settings-autosave.php:5917
msgid "Popup Download Link"
msgstr ""

#: wppa-settings-autosave.php:5918
msgid "Configure the download link on fullsize popups."
msgstr ""

#: wppa-settings-autosave.php:5919
msgid "Link fullsize popup download button to either image or zip file."
msgstr ""

#: wppa-settings-autosave.php:5935
msgid "Download link on lightbox"
msgstr ""

#: wppa-settings-autosave.php:5936
msgid "Art monkey link on lightbox photo names."
msgstr ""

#: wppa-settings-autosave.php:5945
msgid "Album download link"
msgstr ""

#: wppa-settings-autosave.php:5946
msgid "Place an album download link on the album covers"
msgstr ""

#: wppa-settings-autosave.php:5947
msgid "Creates a download zipfile containing the photos of the album"
msgstr ""

#: wppa-settings-autosave.php:5955
msgid "Album download Source"
msgstr ""

#: wppa-settings-autosave.php:5956
msgid "Use Source file for album download link if available."
msgstr ""

#: wppa-settings-autosave.php:5965
msgid "Tagcloud Link"
msgstr ""

#: wppa-settings-autosave.php:5966
msgid "Configure the link from the tags in the tag cloud."
msgstr ""

#: wppa-settings-autosave.php:5967
msgid "Link the tag words to either the thumbnails or the slideshow."
msgstr ""

#: wppa-settings-autosave.php:5968 wppa-settings-autosave.php:6002
#: wppa-settings-autosave.php:6151
msgid ""
"The Occur(rance) indicates the sequence number of the [wppa][/wppa] "
"shortcode on the landing page to be used."
msgstr ""

#: wppa-settings-autosave.php:5992 wppa-settings-autosave.php:6026
#: wppa-settings-autosave.php:6174 wppa-settings-autosave.php:8810
msgid "Occur"
msgstr ""

#: wppa-settings-autosave.php:5999
msgid "Multitag Link"
msgstr ""

#: wppa-settings-autosave.php:6000
msgid "Configure the link from the multitag selection."
msgstr ""

#: wppa-settings-autosave.php:6001
msgid "Link to either the thumbnails or the slideshow."
msgstr ""

#: wppa-settings-autosave.php:6033
msgid "Super View Landing"
msgstr ""

#: wppa-settings-autosave.php:6034
msgid "The landing page for the Super View widget."
msgstr ""

#: wppa-settings-autosave.php:6042
msgid "Defined by the visitor"
msgstr ""

#: wppa-settings-autosave.php:6055
msgid "Uploader Landing"
msgstr ""

#: wppa-settings-autosave.php:6056
msgid "Select the landing page for the Uploader Widget"
msgstr ""

#: wppa-settings-autosave.php:6076
msgid "Bestof Landing"
msgstr ""

#: wppa-settings-autosave.php:6077
msgid "Select the landing page for the BestOf Widget / Box"
msgstr ""

#: wppa-settings-autosave.php:6097
msgid "Album navigator Link"
msgstr ""

#: wppa-settings-autosave.php:6098
msgid "Select link type and page for the Album navigator Widget"
msgstr ""

#: wppa-settings-autosave.php:6126
msgid "Supersearch Landing"
msgstr ""

#: wppa-settings-autosave.php:6127
msgid "Select the landing page for the Supersearch Box"
msgstr ""

#: wppa-settings-autosave.php:6147
msgid "SM widget return"
msgstr ""

#: wppa-settings-autosave.php:6148
msgid "Select the return link for social media from widgets"
msgstr ""

#: wppa-settings-autosave.php:6149
msgid ""
"If you select Landing page, and it wont work, it may be required to set the "
"Occur to the sequence number of the landing shortcode on the page."
msgstr ""

#: wppa-settings-autosave.php:6150
msgid ""
"Normally it is 1, but you can try 2 etc. Always create a new shared link to "
"test a setting."
msgstr ""

#: wppa-settings-autosave.php:6159
msgid "Home page"
msgstr ""

#: wppa-settings-autosave.php:6182
msgid "Album cover subalbums link"
msgstr ""

#: wppa-settings-autosave.php:6183
msgid ""
"Select the linktype and display type for sub-albums on parent album covers."
msgstr ""

#: wppa-settings-autosave.php:6191
msgid "No link at all"
msgstr ""

#: wppa-settings-autosave.php:6192
msgid "Thumbnails and covers"
msgstr ""

#: wppa-settings-autosave.php:6193
msgid "Slideshow or covers"
msgstr ""

#: wppa-settings-autosave.php:6203
msgid "No display at all"
msgstr ""

#: wppa-settings-autosave.php:6204
msgid "A list with sub(sub) albums"
msgstr ""

#: wppa-settings-autosave.php:6205
msgid "A list of children only"
msgstr ""

#: wppa-settings-autosave.php:6206
msgid "An enumeration of names"
msgstr ""

#: wppa-settings-autosave.php:6207
msgid "Micro thumbnails"
msgstr ""

#: wppa-settings-autosave.php:6245
msgid "Table VII:"
msgstr ""

#: wppa-settings-autosave.php:6245
msgid "Permissions and Restrictions:"
msgstr ""

#: wppa-settings-autosave.php:6246
msgid ""
"This table describes the access settings for admin and front-end activities."
msgstr ""

#: wppa-settings-autosave.php:6274
msgid ""
"User can add/edit his own or all albums, depending on VII-D1.1. The "
"administrator and wppa superuser can do anything"
msgstr ""

#: wppa-settings-autosave.php:6275
msgid "Enables the Upload Photos admin screen"
msgstr ""

#: wppa-settings-autosave.php:6276
msgid "Enables the Import Photos amin screen"
msgstr ""

#: wppa-settings-autosave.php:6277
msgid ""
"Enables the capability to change status and edit new photos and approve "
"comments"
msgstr ""

#: wppa-settings-autosave.php:6278
msgid "Enables the Export Photos admin screen"
msgstr ""

#: wppa-settings-autosave.php:6279
msgid "Enables this settings screen"
msgstr ""

#: wppa-settings-autosave.php:6280
msgid "Enables the photo of the day settings screen"
msgstr ""

#: wppa-settings-autosave.php:6281
msgid "Enables the Comment admin screen"
msgstr ""

#: wppa-settings-autosave.php:6282
msgid "Enables the Documentation screen"
msgstr ""

#: wppa-settings-autosave.php:6284
msgid "Role"
msgstr ""

#: wppa-settings-autosave.php:6293
msgid ""
"Admin settings per user role. These settings define the display of the Photo "
"Albums sub-menu items."
msgstr ""

#: wppa-settings-autosave.php:6320
msgid "Frontend create Albums and upload Photos enabling and limiting settings"
msgstr ""

#: wppa-settings-autosave.php:6322
msgid "User create Albums"
msgstr ""

#: wppa-settings-autosave.php:6323
msgid "Enable frontend album creation."
msgstr ""

#: wppa-settings-autosave.php:6324
msgid "If you check this item, frontend album creation will be enabled."
msgstr ""

#: wppa-settings-autosave.php:6334
msgid "User edit album"
msgstr ""

#: wppa-settings-autosave.php:6335
msgid "Enable frontend edit album name and description."
msgstr ""

#: wppa-settings-autosave.php:6345
msgid "User delete Albums"
msgstr ""

#: wppa-settings-autosave.php:6346
msgid "Enable frontend album deletion"
msgstr ""

#: wppa-settings-autosave.php:6347
msgid "If you check this item, frontend album deletion will be enabled."
msgstr ""

#: wppa-settings-autosave.php:6357
msgid "User create notify"
msgstr ""

#: wppa-settings-autosave.php:6358
msgid "Notify these users when an album is created at the front-end"
msgstr ""

#: wppa-settings-autosave.php:6359
msgid "Enter login names seperated by comma's (,)"
msgstr ""

#: wppa-settings-autosave.php:6368
msgid "User create Albums login"
msgstr ""

#: wppa-settings-autosave.php:6369
msgid "Frontend album creation requires the user is logged in."
msgstr ""

#: wppa-settings-autosave.php:6380
msgid "User create Albums Captcha"
msgstr ""

#: wppa-settings-autosave.php:6381
msgid "User must answer security question."
msgstr ""

#: wppa-settings-autosave.php:6402
msgid "Logged out"
msgstr ""

#: wppa-settings-autosave.php:6408
#, php-format
msgid "Upload limit %s"
msgstr ""

#: wppa-settings-autosave.php:6409
msgid "Limit upload capacity for logged out users."
msgstr ""

#: wppa-settings-autosave.php:6410
#, php-format
msgid "Limit upload capacity for the user role %s."
msgstr ""

#: wppa-settings-autosave.php:6411
msgid "This setting has only effect when Table VII-B2 is unchecked."
msgstr ""

#: wppa-settings-autosave.php:6412
msgid ""
"This limitation only applies to frontend uploads when the same userrole does "
"not have the Upload checkbox checked in Table VII-A."
msgstr ""

#: wppa-settings-autosave.php:6413 wppa-settings-autosave.php:6431
#: wppa-settings-autosave.php:8519
msgid "A value of 0 means: no limit."
msgstr ""

#: wppa-settings-autosave.php:6427
#, php-format
msgid "Album limit %s"
msgstr ""

#: wppa-settings-autosave.php:6428
msgid "Limit number of albums for logged out users."
msgstr ""

#: wppa-settings-autosave.php:6429
#, php-format
msgid "Limit number of albums for the user role %s."
msgstr ""

#: wppa-settings-autosave.php:6430
msgid ""
"This limitation only applies to frontend create albums when the same "
"userrole does not have the Album admin checkbox checked in Table VII-A."
msgstr ""

#: wppa-settings-autosave.php:6442
msgid "Upload one only"
msgstr ""

#: wppa-settings-autosave.php:6443
msgid "Non admin users can upload only one photo at a time."
msgstr ""

#: wppa-settings-autosave.php:6453
msgid "Upload moderation"
msgstr ""

#: wppa-settings-autosave.php:6454
msgid "Uploaded photos need moderation."
msgstr ""

#: wppa-settings-autosave.php:6455
msgid ""
"If checked, photos uploaded by users who do not have photo album admin "
"access rights need moderation."
msgstr ""

#: wppa-settings-autosave.php:6456
msgid ""
"Users who have photo album admin access rights can change the photo status "
"to publish or featured."
msgstr ""

#: wppa-settings-autosave.php:6457
msgid "You can set the album admin access rights in Table VII-A."
msgstr ""

#: wppa-settings-autosave.php:6466
msgid "FE Upload private"
msgstr ""

#: wppa-settings-autosave.php:6467
msgid "Front-end uploaded photos status is set to private."
msgstr ""

#: wppa-settings-autosave.php:6468
msgid "This setting overrules VI-B7.0."
msgstr ""

#: wppa-settings-autosave.php:6477
msgid "Notify approve photo"
msgstr ""

#: wppa-settings-autosave.php:6478
msgid "Send an email to the owner when a photo is approved"
msgstr ""

#: wppa-settings-autosave.php:6488
msgid "Upload notify"
msgstr ""

#: wppa-settings-autosave.php:6489
msgid "Notify admin at frontend upload."
msgstr ""

#: wppa-settings-autosave.php:6490 wppa-settings-autosave.php:6501
msgid "If checked, admin will receive a notification by email."
msgstr ""

#: wppa-settings-autosave.php:6499
msgid "Upload backend notify"
msgstr ""

#: wppa-settings-autosave.php:6500
msgid "Notify admin at backend upload."
msgstr ""

#: wppa-settings-autosave.php:6510
msgid "Min size in pixels"
msgstr ""

#: wppa-settings-autosave.php:6511
msgid "Min size for height and width for front-end uploads."
msgstr ""

#: wppa-settings-autosave.php:6512
msgid "Enter the minimum size."
msgstr ""

#: wppa-settings-autosave.php:6521
msgid "Max size in pixels"
msgstr ""

#: wppa-settings-autosave.php:6522
msgid "Max size for height and width for front-end uploads."
msgstr ""

#: wppa-settings-autosave.php:6523
msgid "Enter the maximum size. 0 is unlimited"
msgstr ""

#: wppa-settings-autosave.php:6532
msgid "Home after Upload"
msgstr ""

#: wppa-settings-autosave.php:6533
msgid "After successfull front-end upload, go to the home page."
msgstr ""

#: wppa-settings-autosave.php:6543
msgid "Fe alert"
msgstr ""

#: wppa-settings-autosave.php:6544
msgid "Show alertbox on front-end."
msgstr ""

#: wppa-settings-autosave.php:6545
msgid ""
"Errors are always reported, credit points only when --- none --- is not "
"selected"
msgstr ""

#: wppa-settings-autosave.php:6548
msgid "uploads and create albums"
msgstr ""

#: wppa-settings-autosave.php:6549
msgid "blog it"
msgstr ""

#: wppa-settings-autosave.php:6564
msgid "Max fe upload albums"
msgstr ""

#: wppa-settings-autosave.php:6565
msgid "Max number of albums in frontend upload selection box."
msgstr ""

#: wppa-settings-autosave.php:6577
msgid "Admin Functionality restrictions for non administrators"
msgstr ""

#: wppa-settings-autosave.php:6579
msgid "Alt thumb is restricted"
msgstr ""

#: wppa-settings-autosave.php:6580
msgid "Using <b>alt thumbsize</b> is a restricted action."
msgstr ""

#: wppa-settings-autosave.php:6581
msgid ""
"If checked: alt thumbsize can not be set in album admin by users not having "
"admin rights."
msgstr ""

#: wppa-settings-autosave.php:6590
msgid "Link is restricted"
msgstr ""

#: wppa-settings-autosave.php:6591
msgid "Using <b>Link to</b> is a restricted action."
msgstr ""

#: wppa-settings-autosave.php:6592
msgid ""
"If checked: Link to: can not be set in album admin by users not having admin "
"rights."
msgstr ""

#: wppa-settings-autosave.php:6601
msgid "CoverType is restricted"
msgstr ""

#: wppa-settings-autosave.php:6602
msgid "Changing <b>Cover Type</b> is a restricted action."
msgstr ""

#: wppa-settings-autosave.php:6603
msgid ""
"If checked: Cover Type: can not be set in album admin by users not having "
"admin rights."
msgstr ""

#: wppa-settings-autosave.php:6612
msgid "Photo order# is restricted"
msgstr ""

#: wppa-settings-autosave.php:6613
msgid "Changing <b>Photo sort order #</b> is a restricted action."
msgstr ""

#: wppa-settings-autosave.php:6614
msgid ""
"If checked: Photo sort order #: can not be set in photo admin by users not "
"having admin rights."
msgstr ""

#: wppa-settings-autosave.php:6623
msgid "Change source restricted"
msgstr ""

#: wppa-settings-autosave.php:6624
msgid "Changing the import source dir requires admin rights."
msgstr ""

#: wppa-settings-autosave.php:6625
msgid ""
"If checked, the imput source for importing photos and albums is restricted "
"to user role administrator."
msgstr ""

#: wppa-settings-autosave.php:6634
msgid "Extended status restricted"
msgstr ""

#: wppa-settings-autosave.php:6635
msgid "Setting status other than pending or publish requires admin rights."
msgstr ""

#: wppa-settings-autosave.php:6645
msgid "Photo description restricted"
msgstr ""

#: wppa-settings-autosave.php:6646
msgid "Edit photo description requires admin rights."
msgstr ""

#: wppa-settings-autosave.php:6656
msgid "Update photofiles restricted"
msgstr ""

#: wppa-settings-autosave.php:6657
msgid "Re-upload files requires admin rights"
msgstr ""

#: wppa-settings-autosave.php:6667
msgid "New tags restricted"
msgstr ""

#: wppa-settings-autosave.php:6668
msgid "Creating new tags requires admin rights"
msgstr ""

#: wppa-settings-autosave.php:6669
msgid "If ticked, users can ony use existing tags"
msgstr ""

#: wppa-settings-autosave.php:6678
msgid "Miscellaneous limiting settings"
msgstr ""

#: wppa-settings-autosave.php:6680
msgid "Owners only"
msgstr ""

#: wppa-settings-autosave.php:6681
msgid "Limit edit album access to the album owners only."
msgstr ""

#: wppa-settings-autosave.php:6682
msgid "If checked, non-admin users can edit their own albums only."
msgstr ""

#: wppa-settings-autosave.php:6691
msgid "Upload Owners only"
msgstr ""

#: wppa-settings-autosave.php:6692
msgid "Limit uploads to the album owners only."
msgstr ""

#: wppa-settings-autosave.php:6693
msgid ""
"If checked, users can upload to their own albums and --- public --- only."
msgstr ""

#: wppa-settings-autosave.php:6702
msgid "Frontend Edit"
msgstr ""

#: wppa-settings-autosave.php:6703
msgid "Allow the uploader to edit the photo info"
msgstr ""

#: wppa-settings-autosave.php:6704
msgid ""
"If selected, any logged in user who meets the criteria has the capability to "
"edit the photo information."
msgstr ""

#: wppa-settings-autosave.php:6705
msgid "Note: This may be AFTER moderation!!"
msgstr ""

#: wppa-settings-autosave.php:6707
msgid "Classic"
msgstr ""

#: wppa-settings-autosave.php:6707
msgid "New style"
msgstr ""

#: wppa-settings-autosave.php:6716
msgid "Fe Edit users"
msgstr ""

#: wppa-settings-autosave.php:6717
msgid "The criteria the user must meet to edit photo info"
msgstr ""

#: wppa-settings-autosave.php:6720
msgid "Admin and superuser"
msgstr ""

#: wppa-settings-autosave.php:6720
msgid "Owner, admin and superuser"
msgstr ""

#: wppa-settings-autosave.php:6729
msgid "Fe Edit Theme CSS"
msgstr ""

#: wppa-settings-autosave.php:6730
msgid "The front-end edit photo dialog uses the theme CSS."
msgstr ""

#: wppa-settings-autosave.php:6731
msgid "This setting has effect when Table VII D2.1 is set to 'classic' only."
msgstr ""

#: wppa-settings-autosave.php:6740
msgid "Fe Edit New Items"
msgstr ""

#: wppa-settings-autosave.php:6741
msgid "The items that are fe editable"
msgstr ""

#: wppa-settings-autosave.php:6742
msgid "See also Table II-J10!"
msgstr ""

#: wppa-settings-autosave.php:6755
msgid "Fe Edit Button text"
msgstr ""

#: wppa-settings-autosave.php:6756
msgid "The text on the Edit button."
msgstr ""

#: wppa-settings-autosave.php:6766
msgid "Fe Edit Dialog caption"
msgstr ""

#: wppa-settings-autosave.php:6767
msgid "The text on the header of the popup."
msgstr ""

#: wppa-settings-autosave.php:6777
msgid "Frontend Delete"
msgstr ""

#: wppa-settings-autosave.php:6778
msgid "Allow the uploader to delete the photo"
msgstr ""

#: wppa-settings-autosave.php:6788
msgid "Uploader Moderate Comment"
msgstr ""

#: wppa-settings-autosave.php:6789
msgid "The owner of the photo can moderate the photos comments."
msgstr ""

#: wppa-settings-autosave.php:6790
msgid "This setting requires \"Uploader edit\" to be enabled also."
msgstr ""

#: wppa-settings-autosave.php:6799
msgid "Upload memory check frontend"
msgstr ""

#: wppa-settings-autosave.php:6800 wppa-settings-autosave.php:6811
msgid "Disable uploading photos that are too large."
msgstr ""

#: wppa-settings-autosave.php:6801 wppa-settings-autosave.php:6812
msgid ""
"To prevent out of memory crashes during upload and possible database "
"inconsistencies, uploads can be prevented if the photos are too big."
msgstr ""

#: wppa-settings-autosave.php:6810
msgid "Upload memory check admin"
msgstr ""

#: wppa-settings-autosave.php:6821
msgid "Comment captcha"
msgstr ""

#: wppa-settings-autosave.php:6822
msgid "Use a simple calculate captcha on comments form."
msgstr ""

#: wppa-settings-autosave.php:6835
msgid "Spam lifetime"
msgstr ""

#: wppa-settings-autosave.php:6836
msgid "Delete spam comments when older than."
msgstr ""

#: wppa-settings-autosave.php:6863
msgid "Avoid duplicates"
msgstr ""

#: wppa-settings-autosave.php:6864
msgid "Prevent the creation of duplicate photos."
msgstr ""

#: wppa-settings-autosave.php:6865
msgid ""
"If checked: uploading, importing, copying or moving photos to other albums "
"will be prevented when the desitation album already contains a photo with "
"the same filename."
msgstr ""

#: wppa-settings-autosave.php:6874
msgid "Blacklist user"
msgstr ""

#: wppa-settings-autosave.php:6875
msgid "Set the status of all the users photos to 'pending'."
msgstr ""

#: wppa-settings-autosave.php:6876
msgid "Also inhibits further uploads."
msgstr ""

#: wppa-settings-autosave.php:6882
msgid "--- select a user to blacklist ---"
msgstr ""

#: wppa-settings-autosave.php:6892 wppa-settings-autosave.php:6897
#: wppa-settings-autosave.php:6918 wppa-settings-autosave.php:6956
#: wppa-settings-autosave.php:6961 wppa-settings-autosave.php:6982
#: wppa-settings-autosave.php:9750 wppa-settings-autosave.php:9801
msgid "The page will be reloaded after the action has taken place."
msgstr ""

#: wppa-settings-autosave.php:6898 wppa-settings-autosave.php:6962
msgid "User login name <b>( case sensitive! )</b>:"
msgstr ""

#: wppa-settings-autosave.php:6906
msgid "Unblacklist user"
msgstr ""

#: wppa-settings-autosave.php:6907
msgid "Set the status of all the users photos to 'publish'."
msgstr ""

#: wppa-settings-autosave.php:6911
msgid "--- select a user to unblacklist ---"
msgstr ""

#: wppa-settings-autosave.php:6926
msgid "Photo owner change"
msgstr ""

#: wppa-settings-autosave.php:6927
msgid "Administrators can change photo owner"
msgstr ""

#: wppa-settings-autosave.php:6937
msgid "Super user"
msgstr ""

#: wppa-settings-autosave.php:6938
msgid "Give these users all rights in wppa."
msgstr ""

#: wppa-settings-autosave.php:6939
msgid "This gives the user all the administrator privileges within wppa."
msgstr ""

#: wppa-settings-autosave.php:6940
msgid ""
"Make sure the user also has a role that has all the boxes ticked in Table "
"VII-A"
msgstr ""

#: wppa-settings-autosave.php:6946
msgid "--- select a user to make superuser ---"
msgstr ""

#: wppa-settings-autosave.php:6970
msgid "Unsuper user"
msgstr ""

#: wppa-settings-autosave.php:6971
msgid "Remove user from super user list."
msgstr ""

#: wppa-settings-autosave.php:6975
msgid "--- select a user to unmake superuser ---"
msgstr ""

#: wppa-settings-autosave.php:7008
msgid "Table VIII:"
msgstr ""

#: wppa-settings-autosave.php:7008
msgid "Actions:"
msgstr ""

#: wppa-settings-autosave.php:7009
msgid "This table lists all actions that can be taken to the wppa+ system"
msgstr ""

#: wppa-settings-autosave.php:7019 wppa-settings-autosave.php:7870
msgid "Specification"
msgstr ""

#: wppa-settings-autosave.php:7020 wppa-settings-autosave.php:7871
#: wppa-settings-autosave.php:10795 wppa-settings-autosave.php:10817
msgid "Do it!"
msgstr ""

#: wppa-settings-autosave.php:7022 wppa-settings-autosave.php:7873
msgid "To Go"
msgstr ""

#: wppa-settings-autosave.php:7030
msgid "Harmless and reverseable actions"
msgstr ""

#: wppa-settings-autosave.php:7032
msgid "Ignore concurrency"
msgstr ""

#: wppa-settings-autosave.php:7033
msgid "Ignore the prevention of concurrent actions."
msgstr ""

#: wppa-settings-autosave.php:7034
msgid ""
"This setting is meant to recover from deadlock situations only. Use with "
"care!"
msgstr ""

#: wppa-settings-autosave.php:7045
msgid "Postpone cron"
msgstr ""

#: wppa-settings-autosave.php:7046
msgid "Temporary do no background processes."
msgstr ""

#: wppa-settings-autosave.php:7047
msgid ""
"This setting is meant to be used a.o. during bulk import/upload. Use with "
"care!"
msgstr ""

#: wppa-settings-autosave.php:7058
msgid "Setup"
msgstr ""

#: wppa-settings-autosave.php:7059
msgid "Re-initialize plugin."
msgstr ""

#: wppa-settings-autosave.php:7060
msgid ""
"Re-initilizes the plugin, (re)creates database tables and sets up default "
"settings and directories if required."
msgstr ""

#: wppa-settings-autosave.php:7061
msgid ""
"This action may be required to setup blogs in a multiblog (network) site as "
"well as in rare cases to correct initilization errors."
msgstr ""

#: wppa-settings-autosave.php:7072
msgid "Backup settings"
msgstr ""

#: wppa-settings-autosave.php:7073
msgid "Save all settings into a backup file."
msgstr ""

#: wppa-settings-autosave.php:7074
msgid "Saves all the settings into a backup file"
msgstr ""

#: wppa-settings-autosave.php:7085
msgid "Load settings"
msgstr ""

#: wppa-settings-autosave.php:7086
msgid "Restore all settings from defaults, a backup or skin file."
msgstr ""

#: wppa-settings-autosave.php:7087
msgid ""
"Restores all the settings from the factory supplied defaults, the backup you "
"created or from a skin file."
msgstr ""

#: wppa-settings-autosave.php:7093
msgid "--- set to defaults ---"
msgstr ""

#: wppa-settings-autosave.php:7096
msgid "--- restore backup ---"
msgstr ""

#: wppa-settings-autosave.php:7118
msgid "Regenerate"
msgstr ""

#: wppa-settings-autosave.php:7119 wppa-settings-autosave.php:7120
msgid "Regenerate all thumbnails."
msgstr ""

#: wppa-settings-autosave.php:7123 wppa-settings-autosave.php:7240
#: wppa-settings-autosave.php:7321 wppa-settings-autosave.php:7579
msgid "Skip one"
msgstr ""

#: wppa-settings-autosave.php:7132
msgid "Rerate"
msgstr ""

#: wppa-settings-autosave.php:7133
msgid "Recalculate ratings."
msgstr ""

#: wppa-settings-autosave.php:7134
msgid ""
"This function will recalculate all mean photo ratings from the ratings table."
msgstr ""

#: wppa-settings-autosave.php:7135
msgid ""
"You may need this function after the re-import of previously exported photos"
msgstr ""

#: wppa-settings-autosave.php:7146
msgid "Lost and found"
msgstr ""

#: wppa-settings-autosave.php:7147
msgid "Find \"lost\" photos."
msgstr ""

#: wppa-settings-autosave.php:7148
msgid "This function will attempt to find lost photos."
msgstr ""

#: wppa-settings-autosave.php:7159
msgid "Recuperate"
msgstr ""

#: wppa-settings-autosave.php:7160
msgid "Recuperate IPTC and EXIF data from photos in WPPA+."
msgstr ""

#: wppa-settings-autosave.php:7161
msgid ""
"This action will attempt to find and register IPTC and EXIF data from photos "
"in the WPPA+ system."
msgstr ""

#: wppa-settings-autosave.php:7172
msgid "Remake Index Albums"
msgstr ""

#: wppa-settings-autosave.php:7173
msgid "Remakes the index database table for albums."
msgstr ""

#: wppa-settings-autosave.php:7185
msgid "Remake Index Photos"
msgstr ""

#: wppa-settings-autosave.php:7186
msgid "Remakes the index database table for photos."
msgstr ""

#: wppa-settings-autosave.php:7198
msgid "Clean Index"
msgstr ""

#: wppa-settings-autosave.php:7199
msgid "Remove obsolete entries from index db table."
msgstr ""

#: wppa-settings-autosave.php:7217
msgid "Convert to tree"
msgstr ""

#: wppa-settings-autosave.php:7218
msgid "Convert filesystem to tree structure."
msgstr ""

#: wppa-settings-autosave.php:7221
msgid "Convert to flat"
msgstr ""

#: wppa-settings-autosave.php:7222
msgid "Convert filesystem to flat structure."
msgstr ""

#: wppa-settings-autosave.php:7224
msgid ""
"If you want to go back to a wppa+ version prior to 5.0.16, you MUST convert "
"to flat first."
msgstr ""

#: wppa-settings-autosave.php:7235
msgid "Remake"
msgstr ""

#: wppa-settings-autosave.php:7236
msgid "Remake the photofiles from photo sourcefiles."
msgstr ""

#: wppa-settings-autosave.php:7237
msgid ""
"This action will remake the fullsize images, thumbnail images, and will "
"refresh the iptc and exif data for all photos where the source is found in "
"the corresponding album sub-directory of the source directory."
msgstr ""

#: wppa-settings-autosave.php:7249
msgid "Orientation only"
msgstr ""

#: wppa-settings-autosave.php:7250
msgid "Remake non standard orientated photos only."
msgstr ""

#: wppa-settings-autosave.php:7263
msgid "Missing only"
msgstr ""

#: wppa-settings-autosave.php:7264
msgid "Remake missing photofiles only."
msgstr ""

#: wppa-settings-autosave.php:7277
msgid "Recalc sizes"
msgstr ""

#: wppa-settings-autosave.php:7278
msgid "Recalculate photosizes and save to db."
msgstr ""

#: wppa-settings-autosave.php:7290
msgid "Renew album crypt"
msgstr ""

#: wppa-settings-autosave.php:7291
msgid "Renew album encrcryption codes."
msgstr ""

#: wppa-settings-autosave.php:7303
msgid "Renew photo crypt"
msgstr ""

#: wppa-settings-autosave.php:7304
msgid "Renew photo encrcryption codes."
msgstr ""

#: wppa-settings-autosave.php:7316
msgid "Create orietation sources"
msgstr ""

#: wppa-settings-autosave.php:7317
msgid "Creates correctly oriented pseudo source file."
msgstr ""

#: wppa-settings-autosave.php:7330
msgid "Clearing and other irreverseable actions"
msgstr ""

#: wppa-settings-autosave.php:7332
msgid "Clear ratings"
msgstr ""

#: wppa-settings-autosave.php:7333
msgid "Reset all ratings."
msgstr ""

#: wppa-settings-autosave.php:7334
msgid "WARNING: If checked, this will clear all ratings in the system!"
msgstr ""

#: wppa-settings-autosave.php:7345
msgid "Clear viewcounts"
msgstr ""

#: wppa-settings-autosave.php:7346
msgid "Reset all viewcounts."
msgstr ""

#: wppa-settings-autosave.php:7347
msgid "WARNING: If checked, this will clear all viewcounts in the system!"
msgstr ""

#: wppa-settings-autosave.php:7358
msgid "Reset IPTC"
msgstr ""

#: wppa-settings-autosave.php:7359
msgid "Clear all IPTC data."
msgstr ""

#: wppa-settings-autosave.php:7360
msgid "WARNING: If checked, this will clear all IPTC data in the system!"
msgstr ""

#: wppa-settings-autosave.php:7371
msgid "Reset EXIF"
msgstr ""

#: wppa-settings-autosave.php:7372
msgid "Clear all EXIF data."
msgstr ""

#: wppa-settings-autosave.php:7373
msgid "WARNING: If checked, this will clear all EXIF data in the system!"
msgstr ""

#: wppa-settings-autosave.php:7384
msgid "Apply Default Photoname"
msgstr ""

#: wppa-settings-autosave.php:7385
msgid "Apply Default photo name on all photos in the system."
msgstr ""

#: wppa-settings-autosave.php:7397
msgid "Apply New Photodesc"
msgstr ""

#: wppa-settings-autosave.php:7398
msgid "Apply New photo description on all photos in the system."
msgstr ""

#: wppa-settings-autosave.php:7410
msgid "Append to photodesc"
msgstr ""

#: wppa-settings-autosave.php:7411
msgid "Append this text to all photo descriptions."
msgstr ""

#: wppa-settings-autosave.php:7425
msgid "Remove from photodesc"
msgstr ""

#: wppa-settings-autosave.php:7426
msgid "Remove this text from all photo descriptions."
msgstr ""

#: wppa-settings-autosave.php:7440
msgid "Remove empty albums"
msgstr ""

#: wppa-settings-autosave.php:7441
msgid "Removes albums that are not used."
msgstr ""

#: wppa-settings-autosave.php:7453
msgid "Remove file-ext"
msgstr ""

#: wppa-settings-autosave.php:7454
msgid "Remove possible file extension from photo name."
msgstr ""

#: wppa-settings-autosave.php:7455
msgid ""
"This may be required for old photos, uploaded when the option in Table IX-D3 "
"was not yet available/selected."
msgstr ""

#: wppa-settings-autosave.php:7466
msgid "Re-add file-ext"
msgstr ""

#: wppa-settings-autosave.php:7467
msgid "Revert the <i>Remove file-ext</i> action."
msgstr ""

#: wppa-settings-autosave.php:7479
msgid "All to lower"
msgstr ""

#: wppa-settings-autosave.php:7480
msgid "Convert all file-extensions to lowercase."
msgstr ""

#: wppa-settings-autosave.php:7481
msgid ""
"Affects display files, thumbnail files, and saved extensions in database "
"table. Leaves sourcefiles untouched"
msgstr ""

#: wppa-settings-autosave.php:7482
msgid ""
"If both upper and lowercase files exist, the file with the uppercase "
"extension will be removed."
msgstr ""

#: wppa-settings-autosave.php:7493
msgid "Watermark all"
msgstr ""

#: wppa-settings-autosave.php:7494
msgid "Apply watermark according to current settings to all photos."
msgstr ""

#: wppa-settings-autosave.php:7495
msgid "See Table IX_F for the current watermark settings"
msgstr ""

#: wppa-settings-autosave.php:7506
msgid "Create all autopages"
msgstr ""

#: wppa-settings-autosave.php:7507
msgid "Create all the pages to display slides individually."
msgstr ""

#: wppa-settings-autosave.php:7508 wppa-settings-autosave.php:7522
msgid "See also Table IV-A10."
msgstr ""

#: wppa-settings-autosave.php:7509
msgid ""
"Make sure you have a custom menu and the \"Automatically add new top-level "
"pages to this menu\" box UNticked!!"
msgstr ""

#: wppa-settings-autosave.php:7520
msgid "Delete all autopages"
msgstr ""

#: wppa-settings-autosave.php:7521
msgid "Delete all the pages to display slides individually."
msgstr ""

#: wppa-settings-autosave.php:7534
msgid "Leading zeroes"
msgstr ""

#: wppa-settings-autosave.php:7535
msgid "If photoname numeric, add leading zeros"
msgstr ""

#: wppa-settings-autosave.php:7536
msgid ""
"You can extend the name with leading zeros, so alphabetic sort becomes equal "
"to numeric sort order."
msgstr ""

#: wppa-settings-autosave.php:7539
msgid "Total chars"
msgstr ""

#: wppa-settings-autosave.php:7548
msgid "Add GPX tag"
msgstr ""

#: wppa-settings-autosave.php:7549
msgid "Make sure photos with gpx data have a Gpx tag"
msgstr ""

#: wppa-settings-autosave.php:7561
msgid "Add HD tag"
msgstr ""

#: wppa-settings-autosave.php:7562
msgid "Make sure photos >= 1920 x 1080 have a HD tag"
msgstr ""

#: wppa-settings-autosave.php:7575 wppa-settings-autosave.php:8740
msgid "Optimize files"
msgstr ""

#: wppa-settings-autosave.php:7576
msgid "Optimize with EWWW image optimizer"
msgstr ""

#: wppa-settings-autosave.php:7589
msgid "Edit tag"
msgstr ""

#: wppa-settings-autosave.php:7590
msgid "Globally change a tagname."
msgstr ""

#: wppa-settings-autosave.php:7596
msgid "-select a tag-"
msgstr ""

#: wppa-settings-autosave.php:7602
msgid "Tag:"
msgstr ""

#: wppa-settings-autosave.php:7603
msgid "Change to:"
msgstr ""

#: wppa-settings-autosave.php:7612
msgid "Synchronize Cloudinary"
msgstr ""

#: wppa-settings-autosave.php:7613
msgid "Removes/adds images in the cloud."
msgstr ""

#: wppa-settings-autosave.php:7614
msgid "Removes old images and verifies/adds new images to Cloudinary."
msgstr ""

#: wppa-settings-autosave.php:7615
msgid "See Table IX-K4.7 for the configured lifetime."
msgstr ""

#: wppa-settings-autosave.php:7626
msgid "Fix tags"
msgstr ""

#: wppa-settings-autosave.php:7627
msgid "Make sure photo tags format is uptodate"
msgstr ""

#: wppa-settings-autosave.php:7628
msgid "Fixes tags to be conform current database rules."
msgstr ""

#: wppa-settings-autosave.php:7639
msgid "Fix cats"
msgstr ""

#: wppa-settings-autosave.php:7640
msgid "Make sure album cats format is uptodate"
msgstr ""

#: wppa-settings-autosave.php:7641
msgid "Fixes cats to be conform current database rules."
msgstr ""

#: wppa-settings-autosave.php:7652
msgid "Set owner to name"
msgstr ""

#: wppa-settings-autosave.php:7653
msgid "If photoname equals user display name, set him owner."
msgstr ""

#: wppa-settings-autosave.php:7665
msgid "Move all photos"
msgstr ""

#: wppa-settings-autosave.php:7666
msgid "Move all photos from one album to another album."
msgstr ""

#: wppa-settings-autosave.php:7680 wppa-settings-autosave.php:7703
msgid "From"
msgstr ""

#: wppa-settings-autosave.php:7681
msgid "Move from album number"
msgstr ""

#: wppa-settings-autosave.php:7690 wppa-settings-autosave.php:7730
msgid "To"
msgstr ""

#: wppa-settings-autosave.php:7691
msgid "Move to album number"
msgstr ""

#: wppa-settings-autosave.php:7704
msgid "Move from album"
msgstr ""

#: wppa-settings-autosave.php:7731
msgid "Move to album"
msgstr ""

#: wppa-settings-autosave.php:7760
msgid "Custom album proc"
msgstr ""

#: wppa-settings-autosave.php:7761
msgid "The php code to execute on all albums"
msgstr ""

#: wppa-settings-autosave.php:7762 wppa-settings-autosave.php:7775
msgid "Only run this if you know what you are doing!"
msgstr ""

#: wppa-settings-autosave.php:7773
msgid "Custom photo proc"
msgstr ""

#: wppa-settings-autosave.php:7774
msgid "The php code to execute on all photos"
msgstr ""

#: wppa-settings-autosave.php:7787
msgid "Listings"
msgstr ""

#: wppa-settings-autosave.php:7789
msgid "List Logfile"
msgstr ""

#: wppa-settings-autosave.php:7790
msgid "Show the content of wppa+ (error) log."
msgstr ""

#: wppa-settings-autosave.php:7795
msgid "Purge logfile"
msgstr ""

#: wppa-settings-autosave.php:7797
msgid "On menu"
msgstr ""

#: wppa-settings-autosave.php:7804
msgid "List Ratings"
msgstr ""

#: wppa-settings-autosave.php:7805
msgid "Show the most recent ratings."
msgstr ""

#: wppa-settings-autosave.php:7818
msgid "List Index"
msgstr ""

#: wppa-settings-autosave.php:7819
msgid "Show the content of the index table."
msgstr ""

#: wppa-settings-autosave.php:7823
msgid "Start at text:"
msgstr ""

#: wppa-settings-autosave.php:7833
msgid "List active sessions"
msgstr ""

#: wppa-settings-autosave.php:7834
msgid "Show the content of the sessions table."
msgstr ""

#: wppa-settings-autosave.php:7847
msgid "List comments"
msgstr ""

#: wppa-settings-autosave.php:7848
msgid "Show the content of the comments table."
msgstr ""

#: wppa-settings-autosave.php:7854
msgid "Order by:"
msgstr ""

#: wppa-settings-autosave.php:7883
msgid "Table IX:"
msgstr ""

#: wppa-settings-autosave.php:7883
msgid "Miscellaneous:"
msgstr ""

#: wppa-settings-autosave.php:7884
msgid "This table lists all settings that do not fit into an other table"
msgstr ""

#: wppa-settings-autosave.php:7902
msgid "Internal engine related settings"
msgstr ""

#: wppa-settings-autosave.php:7904
msgid "WPPA+ Filter priority"
msgstr ""

#: wppa-settings-autosave.php:7905
msgid "Sets the priority of the wppa+ content filter."
msgstr ""

#: wppa-settings-autosave.php:7906 wppa-settings-autosave.php:7915
msgid ""
"If you encounter conflicts with the theme or other plugins, increasing this "
"value sometimes helps. Use with great care!"
msgstr ""

#: wppa-settings-autosave.php:7913
msgid "Do_shortcode priority"
msgstr ""

#: wppa-settings-autosave.php:7914
msgid "Sets the priority of the do_shortcode() content filter."
msgstr ""

#: wppa-settings-autosave.php:7922
msgid "WPPA shortcode at Filter priority"
msgstr ""

#: wppa-settings-autosave.php:7923
msgid "Execute shortcode expansion on filter priority in posts and pages."
msgstr ""

#: wppa-settings-autosave.php:7924 wppa-settings-autosave.php:7933
msgid "Use to fix certain layout problems"
msgstr ""

#: wppa-settings-autosave.php:7931
msgid "WPPA shortcode at Filter priority widget"
msgstr ""

#: wppa-settings-autosave.php:7932
msgid "Execute shortcode expansion on filter priority in widgets."
msgstr ""

#: wppa-settings-autosave.php:7940
msgid "JPG image quality"
msgstr ""

#: wppa-settings-autosave.php:7941
msgid "The jpg quality when photos are downsized"
msgstr ""

#: wppa-settings-autosave.php:7942
msgid "The higher the number the better the quality but the larger the file"
msgstr ""

#: wppa-settings-autosave.php:7943
msgid "Possible values 20..100"
msgstr ""

#: wppa-settings-autosave.php:7950
msgid "Allow WPPA+ Debugging"
msgstr ""

#: wppa-settings-autosave.php:7951
msgid "Allow the use of &amp;debug=.. in urls to this site."
msgstr ""

#: wppa-settings-autosave.php:7952
msgid ""
"If checked: appending (?)(&)debug or (?)(&)debug=<int> to an url to this "
"site will generate the display of special WPPA+ diagnostics, as well as php "
"warnings"
msgstr ""

#: wppa-settings-autosave.php:7959
msgid "Auto continue"
msgstr ""

#: wppa-settings-autosave.php:7960
msgid "Continue automatic after time out"
msgstr ""

#: wppa-settings-autosave.php:7961
msgid ""
"If checked, an attempt will be made to restart an admin process when the "
"time is out."
msgstr ""

#: wppa-settings-autosave.php:7969
msgid "Set max execution time here."
msgstr ""

#: wppa-settings-autosave.php:7970
msgid ""
"If your php config does not properly set the max execution time, you can set "
"it here. Seconds, 0 means do not change."
msgstr ""

#: wppa-settings-autosave.php:7971
msgid "A safe value is 45 in most cases"
msgstr ""

#: wppa-settings-autosave.php:7972
#, php-format
msgid "The PHP setting max_execution_time is set to %s."
msgstr ""

#: wppa-settings-autosave.php:7980
msgid "Feed use thumb"
msgstr ""

#: wppa-settings-autosave.php:7981
msgid "Feeds use thumbnail pictures always."
msgstr ""

#: wppa-settings-autosave.php:7989
msgid "Enable <i>in-line</i> settings"
msgstr ""

#: wppa-settings-autosave.php:7990
msgid "Activates shortcode [wppa_set][/wppa_set]."
msgstr ""

#: wppa-settings-autosave.php:7991
msgid ""
"Syntax: [wppa_set name=\"any wppa setting\" value=\"new value\"][/wppa_set]"
msgstr ""

#: wppa-settings-autosave.php:7992
msgid ""
"Example: [wppa_set name=\"wppa_thumbtype\" value=\"masonry-v\"][/wppa_set] "
"sets the thumbnail type to vertical masonry style"
msgstr ""

#: wppa-settings-autosave.php:7993
msgid "Do not forget to reset with [wppa_set][/wppa_set]"
msgstr ""

#: wppa-settings-autosave.php:7994
msgid "Use with great care! There is no check on validity of values!"
msgstr ""

#: wppa-settings-autosave.php:8001
msgid "Runtime modifyable settings"
msgstr ""

#: wppa-settings-autosave.php:8002
msgid "The setting slugs that may be altered using [wppa_set] shortcode."
msgstr ""

#: wppa-settings-autosave.php:8010
msgid "Log Cron"
msgstr ""

#: wppa-settings-autosave.php:8011
msgid "Keep track of cron activity in the wppa logfile."
msgstr ""

#: wppa-settings-autosave.php:8019
msgid "Log Ajax"
msgstr ""

#: wppa-settings-autosave.php:8020
msgid "Keep track of ajax activity in the wppa logfile."
msgstr ""

#: wppa-settings-autosave.php:8028
msgid "Log Comments"
msgstr ""

#: wppa-settings-autosave.php:8029
msgid "Keep track of commenting activity in the wppa logfile."
msgstr ""

#: wppa-settings-autosave.php:8037
msgid "Retry failed mails"
msgstr ""

#: wppa-settings-autosave.php:8038
msgid "Select number of retries for failed mails"
msgstr ""

#: wppa-settings-autosave.php:8039
msgid "Retries occur at the background every hour"
msgstr ""

#: wppa-settings-autosave.php:8046
msgid "Minimum tags"
msgstr ""

#: wppa-settings-autosave.php:8047
msgid "These tags exist even when they do not occur in any photo."
msgstr ""

#: wppa-settings-autosave.php:8048
msgid "Enter tags, separated by comma's (,)"
msgstr ""

#: wppa-settings-autosave.php:8049
msgid ""
"Tags exist when they appear on any photo, and vanish when they do no longer "
"appear. Except the tags you list here; they exist always."
msgstr ""

#: wppa-settings-autosave.php:8056
msgid "Login link"
msgstr ""

#: wppa-settings-autosave.php:8057
msgid "Modify this link if you have a custom login page."
msgstr ""

#: wppa-settings-autosave.php:8066
msgid "WPPA+ Admin related miscellaneous settings"
msgstr ""

#: wppa-settings-autosave.php:8068
msgid "Allow HTML"
msgstr ""

#: wppa-settings-autosave.php:8069
msgid "Allow HTML in album and photo descriptions."
msgstr ""

#: wppa-settings-autosave.php:8070 wppa-settings-autosave.php:8079
msgid ""
"If checked: html is allowed. WARNING: No checks on syntax, it is your own "
"responsibility to close tags properly!"
msgstr ""

#: wppa-settings-autosave.php:8077
msgid "Allow HTML custom"
msgstr ""

#: wppa-settings-autosave.php:8078
msgid "Allow HTML in custom photo datafields."
msgstr ""

#: wppa-settings-autosave.php:8086
msgid "Check tag balance"
msgstr ""

#: wppa-settings-autosave.php:8087
msgid "Check if the HTML tags are properly closed: \"balanced\"."
msgstr ""

#: wppa-settings-autosave.php:8088
msgid ""
"If the HTML tags in an album or a photo description are not in balance, the "
"description is not updated, an errormessage is displayed"
msgstr ""

#: wppa-settings-autosave.php:8095
msgid "Use WP editor"
msgstr ""

#: wppa-settings-autosave.php:8096
msgid "Use the wp editor for multiline text fields."
msgstr ""

#: wppa-settings-autosave.php:8104
msgid "Album sel hierarchic"
msgstr ""

#: wppa-settings-autosave.php:8105
msgid "Show albums with (grand)parents in selection lists."
msgstr ""

#: wppa-settings-autosave.php:8113
msgid "Page sel hierarchic"
msgstr ""

#: wppa-settings-autosave.php:8114
msgid "Show pages with (grand)parents in selection lists."
msgstr ""

#: wppa-settings-autosave.php:8123
msgid "Album admin page size"
msgstr ""

#: wppa-settings-autosave.php:8124
msgid "The number of albums per page on the Edit Album admin page."
msgstr ""

#: wppa-settings-autosave.php:8134
msgid "Photo admin page size"
msgstr ""

#: wppa-settings-autosave.php:8135
msgid ""
"The number of photos per page on the <br/>Edit Album -> Manage photos and "
"Edit Photos admin pages."
msgstr ""

#: wppa-settings-autosave.php:8145
msgid "Photo admin max albums"
msgstr ""

#: wppa-settings-autosave.php:8146
msgid "Max albums to show in album selectionbox."
msgstr ""

#: wppa-settings-autosave.php:8147
msgid ""
"If there are more albums in the system, display an input box asking for "
"album id#"
msgstr ""

#: wppa-settings-autosave.php:8156
msgid "Comment admin page size"
msgstr ""

#: wppa-settings-autosave.php:8157
msgid "The number of comments per page on the Comments admin pages."
msgstr ""

#: wppa-settings-autosave.php:8167
msgid "Geo info edit"
msgstr ""

#: wppa-settings-autosave.php:8168
msgid "Lattitude and longitude may be edited in photo admin."
msgstr ""

#: wppa-settings-autosave.php:8176
msgid "Admin bar menu admin"
msgstr ""

#: wppa-settings-autosave.php:8177
msgid "Show menu on admin bar on admin pages."
msgstr ""

#: wppa-settings-autosave.php:8185
msgid "Admin bar menu frontend"
msgstr ""

#: wppa-settings-autosave.php:8186
msgid "Show menu on admin bar on frontend pages."
msgstr ""

#: wppa-settings-autosave.php:8194
msgid "Add shortcode to posts"
msgstr ""

#: wppa-settings-autosave.php:8195
msgid "Add a shortcode to the end of all posts."
msgstr ""

#: wppa-settings-autosave.php:8203
msgid "Shortcode to add"
msgstr ""

#: wppa-settings-autosave.php:8204
msgid "The shortcode to be added to the posts."
msgstr ""

#: wppa-settings-autosave.php:8212
msgid "Import page previews"
msgstr ""

#: wppa-settings-autosave.php:8213
msgid "Show thumbnail previews in import admin page."
msgstr ""

#: wppa-settings-autosave.php:8221
msgid "Upload audiostub"
msgstr ""

#: wppa-settings-autosave.php:8222
msgid "Upload a new audio stub file"
msgstr ""

#: wppa-settings-autosave.php:8226
msgid "Upload audio stub image"
msgstr ""

#: wppa-settings-autosave.php:8231
msgid "Confirm create"
msgstr ""

#: wppa-settings-autosave.php:8232
msgid "Display confirmation dialog before creating album."
msgstr ""

#: wppa-settings-autosave.php:8240
msgid "Import source root"
msgstr ""

#: wppa-settings-autosave.php:8241
msgid "Specify the highest level in the filesystem where to import from"
msgstr ""

#: wppa-settings-autosave.php:8258
msgid "Allow import from WPPA+ source folders"
msgstr ""

#: wppa-settings-autosave.php:8259
msgid "Only switch this on if you know what you are doing!"
msgstr ""

#: wppa-settings-autosave.php:8267
msgid "Enable shortcode generator"
msgstr ""

#: wppa-settings-autosave.php:8268
msgid "Show album icon above page/post edit window"
msgstr ""

#: wppa-settings-autosave.php:8269
msgid ""
"Administrators and wppa super users will always have the shortcode generator "
"available."
msgstr ""

#: wppa-settings-autosave.php:8276
msgid "Bulk photo moderation"
msgstr ""

#: wppa-settings-autosave.php:8277
msgid "Use bulk edit for photo moderation"
msgstr ""

#: wppa-settings-autosave.php:8285
msgid "SEO related settings"
msgstr ""

#: wppa-settings-autosave.php:8287
msgid "Meta on page"
msgstr ""

#: wppa-settings-autosave.php:8288
msgid "Meta tags for photos on the page."
msgstr ""

#: wppa-settings-autosave.php:8289
msgid ""
"If checked, the header of the page will contain metatags that refer to "
"featured photos on the page in the page context."
msgstr ""

#: wppa-settings-autosave.php:8296
msgid "Meta all"
msgstr ""

#: wppa-settings-autosave.php:8297
msgid "Meta tags for all featured photos."
msgstr ""

#: wppa-settings-autosave.php:8298
msgid ""
"If checked, the header of the page will contain metatags that refer to all "
"featured photo files."
msgstr ""

#: wppa-settings-autosave.php:8299
msgid ""
"If you have many featured photos, you might wish to uncheck this item to "
"reduce the size of the page header."
msgstr ""

#: wppa-settings-autosave.php:8306
msgid "Add og meta tags"
msgstr ""

#: wppa-settings-autosave.php:8307
msgid "Add og meta tags to the page header."
msgstr ""

#: wppa-settings-autosave.php:8310
msgid ""
"Turning this off may affect the functionality of social media items in the "
"share box that rely on open graph tags information."
msgstr ""

#: wppa-settings-autosave.php:8316
msgid "Image Alt attribute type"
msgstr ""

#: wppa-settings-autosave.php:8317
msgid "Select kind of HTML alt=\"\" content for images."
msgstr ""

#: wppa-settings-autosave.php:8320
msgid "photo name"
msgstr ""

#: wppa-settings-autosave.php:8320
msgid "name without file-ext"
msgstr ""

#: wppa-settings-autosave.php:8320
msgid "set in album admin"
msgstr ""

#: wppa-settings-autosave.php:8327
msgid "New Album and New Photo related miscellaneous settings"
msgstr ""

#: wppa-settings-autosave.php:8365
msgid "Maximum time an album is indicated as New"
msgstr ""

#: wppa-settings-autosave.php:8373
msgid "New Photo"
msgstr ""

#: wppa-settings-autosave.php:8374
msgid "Maximum time a photo is indicated as New"
msgstr ""

#: wppa-settings-autosave.php:8382
msgid "Modified Album"
msgstr ""

#: wppa-settings-autosave.php:8383
msgid "Maximum time an album is indicated as Modified"
msgstr ""

#: wppa-settings-autosave.php:8391
msgid "Modified Photo"
msgstr ""

#: wppa-settings-autosave.php:8392
msgid "Maximum time a photo is indicated as Modified"
msgstr ""

#: wppa-settings-autosave.php:8400
msgid "Use text labels"
msgstr ""

#: wppa-settings-autosave.php:8401
msgid "Use editable text for the New and Modified labels"
msgstr ""

#: wppa-settings-autosave.php:8402
msgid "If UNticked, you can specify the urls for custom images to be used."
msgstr ""

#: wppa-settings-autosave.php:8413
msgid "Orange"
msgstr ""

#: wppa-settings-autosave.php:8414
msgid "Yellow"
msgstr ""

#: wppa-settings-autosave.php:8417
msgid "Purple"
msgstr ""

#: wppa-settings-autosave.php:8418
msgid "Black/white"
msgstr ""

#: wppa-settings-autosave.php:8430 wppa-settings-autosave.php:8452
msgid "New label"
msgstr ""

#: wppa-settings-autosave.php:8431
msgid "Specify the \"New\" indicator details."
msgstr ""

#: wppa-settings-autosave.php:8432 wppa-settings-autosave.php:8443
msgid "If you use qTranslate, the text may be multilingual."
msgstr ""

#: wppa-settings-autosave.php:8441 wppa-settings-autosave.php:8461
msgid "Modified label"
msgstr ""

#: wppa-settings-autosave.php:8442
msgid "Specify the \"Modified\" indicator details."
msgstr ""

#: wppa-settings-autosave.php:8453
msgid "Specify the \"New\" indicator url."
msgstr ""

#: wppa-settings-autosave.php:8462
msgid "Specify the \"Modified\" indicator url."
msgstr ""

#: wppa-settings-autosave.php:8470
msgid "Limit LasTen New"
msgstr ""

#: wppa-settings-autosave.php:8471
msgid "Limits the LasTen photos to those that are 'New', or newly modified."
msgstr ""

#: wppa-settings-autosave.php:8472
msgid ""
"If you tick this box and configured the new photo time, you can even limit "
"the number by the setting in Table I-F7, or set that number to an unlikely "
"high value."
msgstr ""

#: wppa-settings-autosave.php:8479
msgid "LasTen use Modified"
msgstr ""

#: wppa-settings-autosave.php:8480
msgid ""
"Use the time modified rather than time upload for LasTen widget/shortcode."
msgstr ""

#: wppa-settings-autosave.php:8488
msgid "Apply Newphoto desc"
msgstr ""

#: wppa-settings-autosave.php:8489
msgid "Give each new photo a standard description."
msgstr ""

#: wppa-settings-autosave.php:8490
msgid ""
"If checked, each new photo will get the description (template) as specified "
"in the next item."
msgstr ""

#: wppa-settings-autosave.php:8497
msgid "New photo desc"
msgstr ""

#: wppa-settings-autosave.php:8498
msgid "The description (template) to add to a new photo."
msgstr ""

#: wppa-settings-autosave.php:8499
msgid "Enter the default description."
msgstr ""

#: wppa-settings-autosave.php:8500
msgid "If you use html, please check item B-1 of this table."
msgstr ""

#: wppa-settings-autosave.php:8507
msgid "New photo owner"
msgstr ""

#: wppa-settings-autosave.php:8508
msgid "The owner of a new uploaded photo."
msgstr ""

#: wppa-settings-autosave.php:8509
msgid "If you leave this blank, the uploader will be set as the owner"
msgstr ""

#: wppa-settings-autosave.php:8511
msgid "leave blank or enter login name"
msgstr ""

#: wppa-settings-autosave.php:8517
msgid "New albums are created with this upload limit."
msgstr ""

#: wppa-settings-autosave.php:8518
msgid ""
"Administrators can change the limit settings in the \"Edit Album Information"
"\" admin page."
msgstr ""

#: wppa-settings-autosave.php:8535
msgid "Default parent"
msgstr ""

#: wppa-settings-autosave.php:8536
msgid "The parent album of new albums."
msgstr ""

#: wppa-settings-autosave.php:8553
msgid "Default parent always"
msgstr ""

#: wppa-settings-autosave.php:8554
msgid ""
"The parent album of new albums is always the default, except for "
"administrators."
msgstr ""

#: wppa-settings-autosave.php:8562
msgid "Show album full"
msgstr ""

#: wppa-settings-autosave.php:8563
msgid "Show the Upload limit reached message if appropriate."
msgstr ""

#: wppa-settings-autosave.php:8571
msgid "Grant an album"
msgstr ""

#: wppa-settings-autosave.php:8572
msgid "Create an album for each user logging in."
msgstr ""

#: wppa-settings-autosave.php:8580
msgid "Grant album name"
msgstr ""

#: wppa-settings-autosave.php:8581
msgid "The name to be used for the album."
msgstr ""

#: wppa-settings-autosave.php:8584
msgid "Login name"
msgstr ""

#: wppa-settings-autosave.php:8584 wppa-upldr-widget.php:201
msgid "Display name"
msgstr ""

#: wppa-settings-autosave.php:8584
msgid "Id"
msgstr ""

#: wppa-settings-autosave.php:8584
msgid "Firstname Lastname"
msgstr ""

#: wppa-settings-autosave.php:8591
msgid "Grant parent selection method"
msgstr ""

#: wppa-settings-autosave.php:8592
msgid "The way the grant parents are defined."
msgstr ""

#: wppa-settings-autosave.php:8595
msgid "An album (multi)selectionbox"
msgstr ""

#: wppa-settings-autosave.php:8596
msgid "An album category"
msgstr ""

#: wppa-settings-autosave.php:8597
msgid "An index search token"
msgstr ""

#: wppa-settings-autosave.php:8611
msgid "Grant parent"
msgstr ""

#: wppa-settings-autosave.php:8612
msgid "The parent album(s) of the auto created albums."
msgstr ""

#: wppa-settings-autosave.php:8613
msgid ""
"You may select multiple albums. All logged in visitors will get their own "
"sub-album in each granted parent."
msgstr ""

#: wppa-settings-autosave.php:8631
msgid "Grant parent category"
msgstr ""

#: wppa-settings-autosave.php:8632
msgid "The category of the parent album(s) of the auto created albums."
msgstr ""

#: wppa-settings-autosave.php:8648
msgid "Grant parent index token"
msgstr ""

#: wppa-settings-autosave.php:8649
msgid ""
"The index token that defines the parent album(s) of the auto created albums."
msgstr ""

#: wppa-settings-autosave.php:8659
msgid "Grant categories"
msgstr ""

#: wppa-settings-autosave.php:8660
msgid "The categories a new granted album will get."
msgstr ""

#: wppa-settings-autosave.php:8668
msgid "Grant tags"
msgstr ""

#: wppa-settings-autosave.php:8669
msgid "The default tags the photos in a new granted album will get."
msgstr ""

#: wppa-settings-autosave.php:8677
msgid "Max user albums"
msgstr ""

#: wppa-settings-autosave.php:8678
msgid "The max number of albums a user can create."
msgstr ""

#: wppa-settings-autosave.php:8679
msgid ""
"The maximum number of albums a user can create when he is not admin and "
"owner only is active"
msgstr ""

#: wppa-settings-autosave.php:8680
msgid "A number of 0 means No limit"
msgstr ""

#: wppa-settings-autosave.php:8687
msgid "Default photo name"
msgstr ""

#: wppa-settings-autosave.php:8688
msgid "Select the way the name of a new uploaded photo should be determined."
msgstr ""

#: wppa-settings-autosave.php:8691
msgid "Filename"
msgstr ""

#: wppa-settings-autosave.php:8692
msgid "Filename without extension"
msgstr ""

#: wppa-settings-autosave.php:8693
msgid "IPTC Tag 2#005 (Graphic name)"
msgstr ""

#: wppa-settings-autosave.php:8694
msgid "IPTC Tag 2#120 (Caption)"
msgstr ""

#: wppa-settings-autosave.php:8695
msgid "No name at all"
msgstr ""

#: wppa-settings-autosave.php:8696
msgid "Photo w#id (literally)"
msgstr ""

#: wppa-settings-autosave.php:8704
msgid "Default coverphoto"
msgstr ""

#: wppa-settings-autosave.php:8705
msgid "Name of photofile to become cover image"
msgstr ""

#: wppa-settings-autosave.php:8706
msgid ""
"If you name a photofile like this setting before upload, it will become the "
"coverimage automatically."
msgstr ""

#: wppa-settings-autosave.php:8713
msgid "Copy Timestamp"
msgstr ""

#: wppa-settings-autosave.php:8714
msgid "Copy timestamp when copying photo."
msgstr ""

#: wppa-settings-autosave.php:8715
msgid "If checked, the copied photo is not \"new\""
msgstr ""

#: wppa-settings-autosave.php:8722
msgid "Copy Owner"
msgstr ""

#: wppa-settings-autosave.php:8723
msgid "Copy the owner when copying photo."
msgstr ""

#: wppa-settings-autosave.php:8731
msgid "FE Albums public"
msgstr ""

#: wppa-settings-autosave.php:8732
msgid "Frontend created albums are --- public ---"
msgstr ""

#: wppa-settings-autosave.php:8741
msgid "Optimize image files right after upload/import"
msgstr ""

#: wppa-settings-autosave.php:8742
msgid "This option requires the plugin EWWW Image Optimizer to be activated"
msgstr ""

#: wppa-settings-autosave.php:8749
msgid "Default album linktype"
msgstr ""

#: wppa-settings-autosave.php:8750
msgid "The album linktype for new albums"
msgstr ""

#: wppa-settings-autosave.php:8769
msgid "Search Albums and Photos related settings"
msgstr ""

#: wppa-settings-autosave.php:8771
msgid "Search page"
msgstr ""

#: wppa-settings-autosave.php:8772
msgid "Display the search results on page."
msgstr ""

#: wppa-settings-autosave.php:8773
msgid ""
"Select the page to be used to display search results. The page MUST contain "
"[wppa][/wppa]."
msgstr ""

#: wppa-settings-autosave.php:8774
msgid "You may give it the title \"Search results\" or something alike."
msgstr ""

#: wppa-settings-autosave.php:8775
msgid ""
"Or you may use the standard page on which you display the generic album."
msgstr ""

#: wppa-settings-autosave.php:8782
msgid "--- Please select a page ---"
msgstr ""

#: wppa-settings-autosave.php:8815
msgid "Exclude separate"
msgstr ""

#: wppa-settings-autosave.php:8816
msgid "Do not search 'separate' albums."
msgstr ""

#: wppa-settings-autosave.php:8817
msgid ""
"When checked, albums (and photos in them) that have the parent set to --- "
"separate --- will be excluded from being searched."
msgstr ""

#: wppa-settings-autosave.php:8818
msgid ""
"Except when you start searching in a 'saparate' album, with the \"search in "
"current section\" box ticked."
msgstr ""

#: wppa-settings-autosave.php:8825
msgid "Include tags"
msgstr ""

#: wppa-settings-autosave.php:8826
msgid "Do also search the photo tags."
msgstr ""

#: wppa-settings-autosave.php:8827
msgid "When checked, the tags of the photo will also be searched."
msgstr ""

#: wppa-settings-autosave.php:8834
msgid "Include categories"
msgstr ""

#: wppa-settings-autosave.php:8835
msgid "Do also search the album categories."
msgstr ""

#: wppa-settings-autosave.php:8836
msgid "When checked, the categories of the album will also be searched."
msgstr ""

#: wppa-settings-autosave.php:8843
msgid "Include comments"
msgstr ""

#: wppa-settings-autosave.php:8844
msgid "Do also search the comments on photos."
msgstr ""

#: wppa-settings-autosave.php:8845
msgid "When checked, the comments of the photos will also be searched."
msgstr ""

#: wppa-settings-autosave.php:8852
msgid "Photos only"
msgstr ""

#: wppa-settings-autosave.php:8853
msgid "Search for photos only."
msgstr ""

#: wppa-settings-autosave.php:8854
msgid "When checked, only photos will be searched for."
msgstr ""

#: wppa-settings-autosave.php:8869
msgid "Max albums found"
msgstr ""

#: wppa-settings-autosave.php:8870
msgid "The maximum number of albums to be displayed."
msgstr ""

#: wppa-settings-autosave.php:8878
msgid "Max photos found"
msgstr ""

#: wppa-settings-autosave.php:8879
msgid "The maximum number of photos to be displayed."
msgstr ""

#: wppa-settings-autosave.php:8887
msgid "Tags OR only"
msgstr ""

#: wppa-settings-autosave.php:8888
msgid "No and / or buttons"
msgstr ""

#: wppa-settings-autosave.php:8889
msgid ""
"Hide the and/or radiobuttons and do the or method in the multitag widget and "
"shortcode."
msgstr ""

#: wppa-settings-autosave.php:8896
msgid "Tags add Inverse"
msgstr ""

#: wppa-settings-autosave.php:8897
msgid "Add a checkbox to invert the selection."
msgstr ""

#: wppa-settings-autosave.php:8898
msgid "Adds an Invert (NOT) checkbox on the multitag widget and shortcode."
msgstr ""

#: wppa-settings-autosave.php:8905
msgid "Floating searchtoken"
msgstr ""

#: wppa-settings-autosave.php:8906
msgid "A match need not start at the first char."
msgstr ""

#: wppa-settings-autosave.php:8907
msgid ""
"A match is found while searching also when the entered token is somewhere in "
"the middle of a word."
msgstr ""

#: wppa-settings-autosave.php:8908
msgid "This works in indexed search only!"
msgstr ""

#: wppa-settings-autosave.php:8915
msgid "Search results display"
msgstr ""

#: wppa-settings-autosave.php:8916
msgid "Select the way the search results should be displayed."
msgstr ""

#: wppa-settings-autosave.php:8917
msgid ""
"If you select anything different from \"Albums and thumbnails\", \"Photos "
"only\" is assumed (Table IX-E6)."
msgstr ""

#: wppa-settings-autosave.php:8919
msgid "Albums and thumbnails"
msgstr ""

#: wppa-settings-autosave.php:8919
msgid "Slideonly slideshow"
msgstr ""

#: wppa-settings-autosave.php:8926
msgid "Name max length"
msgstr ""

#: wppa-settings-autosave.php:8927
msgid "Max length of displayed photonames in supersearch selectionlist"
msgstr ""

#: wppa-settings-autosave.php:8928 wppa-settings-autosave.php:8937
msgid ""
"To limit the length of the selectionlist, enter the number of characters to "
"show."
msgstr ""

#: wppa-settings-autosave.php:8935
msgid "Text max length"
msgstr ""

#: wppa-settings-autosave.php:8936
msgid "Max length of displayed photo text in supersearch selectionlist"
msgstr ""

#: wppa-settings-autosave.php:8944
msgid "Search toptext"
msgstr ""

#: wppa-settings-autosave.php:8945
msgid "The text at the top of the search box."
msgstr ""

#: wppa-settings-autosave.php:8946
msgid ""
"This is the equivalence of the text you can enter in the widget activation "
"screen to show above the input box, but now for the search shortcode display."
msgstr ""

#: wppa-settings-autosave.php:8947
msgid "May contain unfiltered HTML."
msgstr ""

#: wppa-settings-autosave.php:8954
msgid "Section search text"
msgstr ""

#: wppa-settings-autosave.php:8955
msgid ""
"The labeltext at the checkbox for the 'Search in current section' checkbox."
msgstr ""

#: wppa-settings-autosave.php:8963
msgid "Results search text"
msgstr ""

#: wppa-settings-autosave.php:8964
msgid ""
"The labeltext at the checkbox for the 'Search in current results' checkbox."
msgstr ""

#: wppa-settings-autosave.php:8972
msgid "Minimum search token length"
msgstr ""

#: wppa-settings-autosave.php:8973
msgid "The minmum number of chars in a search request."
msgstr ""

#: wppa-settings-autosave.php:8981
msgid "Exclude from search"
msgstr ""

#: wppa-settings-autosave.php:8982
msgid "Exclude these words from search index."
msgstr ""

#: wppa-settings-autosave.php:8983
msgid "Enter words separated by commas (,)"
msgstr ""

#: wppa-settings-autosave.php:8990
msgid "Exclude numbers"
msgstr ""

#: wppa-settings-autosave.php:8991
msgid "Exclude numbers from search index."
msgstr ""

#: wppa-settings-autosave.php:8992
msgid "If ticked, photos and albums are not searchable by numbers."
msgstr ""

#: wppa-settings-autosave.php:8999
msgid "Ignore slash"
msgstr ""

#: wppa-settings-autosave.php:9000
msgid "Ignore slash chracter (/)."
msgstr ""

#: wppa-settings-autosave.php:9008
msgid "Watermark related settings"
msgstr ""

#: wppa-settings-autosave.php:9011
msgid "Enable the application of watermarks."
msgstr ""

#: wppa-settings-autosave.php:9012
msgid "If checked, photos can be watermarked during upload / import."
msgstr ""

#: wppa-settings-autosave.php:9021
msgid "Watermark file"
msgstr ""

#: wppa-settings-autosave.php:9022
msgid "The default watermarkfile to be used."
msgstr ""

#: wppa-settings-autosave.php:9023
msgid "Watermark files are of type png and reside in"
msgstr ""

#: wppa-settings-autosave.php:9024
msgid ""
"A suitable watermarkfile typically consists of a transparent background and "
"a black text or drawing."
msgstr ""

#: wppa-settings-autosave.php:9026
msgid ""
"You may also select one of the textual watermark types at the bottom of the "
"selection list."
msgstr ""

#: wppa-settings-autosave.php:9030
msgid "position:"
msgstr ""

#: wppa-settings-autosave.php:9036
msgid "Upload watermark"
msgstr ""

#: wppa-settings-autosave.php:9037
msgid "Upload a new watermark file"
msgstr ""

#: wppa-settings-autosave.php:9041
msgid "Upload watermark image"
msgstr ""

#: wppa-settings-autosave.php:9046
msgid "Watermark opacity image"
msgstr ""

#: wppa-settings-autosave.php:9047
msgid "You can set the intensity of image watermarks here."
msgstr ""

#: wppa-settings-autosave.php:9048 wppa-settings-autosave.php:9153
msgid ""
"The higher the number, the intenser the watermark. Value must be > 0 and <= "
"100."
msgstr ""

#: wppa-settings-autosave.php:9055
msgid "Textual watermark style"
msgstr ""

#: wppa-settings-autosave.php:9056
msgid "The way the textual watermarks look like"
msgstr ""

#: wppa-settings-autosave.php:9060
msgid "TV subtitle style"
msgstr ""

#: wppa-settings-autosave.php:9060
msgid "White text on black background"
msgstr ""

#: wppa-settings-autosave.php:9060
msgid "Black text on white background"
msgstr ""

#: wppa-settings-autosave.php:9060
msgid "Reverse TV style (Utopia)"
msgstr ""

#: wppa-settings-autosave.php:9060
msgid "White on transparent background"
msgstr ""

#: wppa-settings-autosave.php:9060
msgid "Black on transparent background"
msgstr ""

#: wppa-settings-autosave.php:9070
msgid "Predefined watermark text"
msgstr ""

#: wppa-settings-autosave.php:9071
msgid "The text to use when --- pre-defined --- is selected."
msgstr ""

#: wppa-settings-autosave.php:9072
msgid "You may use the following keywords:"
msgstr ""

#: wppa-settings-autosave.php:9073
msgid ""
"w#site, w#displayname, all standard photo keywords, iptc and exif keywords"
msgstr ""

#: wppa-settings-autosave.php:9080
msgid "Textual watermark font"
msgstr ""

#: wppa-settings-autosave.php:9081
msgid "The font to use with textual watermarks."
msgstr ""

#: wppa-settings-autosave.php:9082
msgid "Except for the system font, are font files of type ttf and reside in"
msgstr ""

#: wppa-settings-autosave.php:9110
msgid "Textual watermark font size"
msgstr ""

#: wppa-settings-autosave.php:9111
msgid "You can set the size of the truetype fonts only."
msgstr ""

#: wppa-settings-autosave.php:9112
msgid ""
"System font can have size 1,2,3,4 or 5, in some stoneage fontsize units. Any "
"value > 5 will be treated as 5."
msgstr ""

#: wppa-settings-autosave.php:9113
msgid ""
"Truetype fonts can have any positive integer size, if your PHPs GD version "
"is 1, in pixels, in GD2 in points."
msgstr ""

#: wppa-settings-autosave.php:9114
msgid "It is unclear how many pixels a point is..."
msgstr ""

#: wppa-settings-autosave.php:9121
msgid "Foreground color"
msgstr ""

#: wppa-settings-autosave.php:9122
msgid "Textual watermark foreground color (black)."
msgstr ""

#: wppa-settings-autosave.php:9132
msgid "Textual watermark background color (white)."
msgstr ""

#: wppa-settings-autosave.php:9141
msgid "Upload watermark font"
msgstr ""

#: wppa-settings-autosave.php:9142
msgid "Upload a new watermark font file"
msgstr ""

#: wppa-settings-autosave.php:9143
msgid ""
"Upload truetype fonts (.ttf) only, and test if they work on your server "
"platform."
msgstr ""

#: wppa-settings-autosave.php:9146
msgid "Upload TrueType font"
msgstr ""

#: wppa-settings-autosave.php:9151
msgid "Watermark opacity text"
msgstr ""

#: wppa-settings-autosave.php:9152
msgid "You can set the intensity of a text watermarks here."
msgstr ""

#: wppa-settings-autosave.php:9161
msgid "A real life preview. To update: refresh the page."
msgstr ""

#: wppa-settings-autosave.php:9172
msgid "Watermark thumbnails"
msgstr ""

#: wppa-settings-autosave.php:9173
msgid "Watermark also the thumbnail image files."
msgstr ""

#: wppa-settings-autosave.php:9181
msgid "Slideshow elements sequence order settings"
msgstr ""

#: wppa-settings-autosave.php:9187 wppa-settings-autosave.php:9233
msgid "StartStop"
msgstr ""

#: wppa-settings-autosave.php:9188 wppa-settings-autosave.php:9234
msgid "SlideFrame"
msgstr ""

#: wppa-settings-autosave.php:9190
msgid "Desc"
msgstr ""

#: wppa-settings-autosave.php:9193 wppa-settings-autosave.php:9238
msgid "FilmStrip"
msgstr ""

#: wppa-settings-autosave.php:9194 wppa-settings-autosave.php:9239
msgid "Browsebar"
msgstr ""

#: wppa-settings-autosave.php:9196 wppa-settings-autosave.php:9241
msgid "IPTC data"
msgstr ""

#: wppa-settings-autosave.php:9197 wppa-settings-autosave.php:9242
msgid "EXIF data"
msgstr ""

#: wppa-settings-autosave.php:9198 wppa-settings-autosave.php:9243
msgid "Share box"
msgstr ""

#: wppa-settings-autosave.php:9200 wppa-settings-autosave.php:9245
msgid "Enabled"
msgstr ""

#: wppa-settings-autosave.php:9201 wppa-settings-autosave.php:9246
msgid "Disabled"
msgstr ""

#: wppa-settings-autosave.php:9203 wppa-settings-autosave.php:9248
msgid "Start/Stop & Slower/Faster navigation bar"
msgstr ""

#: wppa-settings-autosave.php:9204 wppa-settings-autosave.php:9249
msgid "The Slide Frame"
msgstr ""

#: wppa-settings-autosave.php:9204 wppa-settings-autosave.php:9249
msgid "( Always )"
msgstr ""

#: wppa-settings-autosave.php:9205
msgid "Photo Name Box"
msgstr ""

#: wppa-settings-autosave.php:9206
msgid "Photo Description Box"
msgstr ""

#: wppa-settings-autosave.php:9207 wppa-settings-autosave.php:9251
msgid "Custom Box"
msgstr ""

#: wppa-settings-autosave.php:9208 wppa-settings-autosave.php:9252
msgid "Rating Bar"
msgstr ""

#: wppa-settings-autosave.php:9209 wppa-settings-autosave.php:9253
msgid "Film Strip with embedded Start/Stop and Goto functionality"
msgstr ""

#: wppa-settings-autosave.php:9210 wppa-settings-autosave.php:9254
msgid "Browse Bar with Photo X of Y counter"
msgstr ""

#: wppa-settings-autosave.php:9211 wppa-settings-autosave.php:9255
msgid "Comments Box"
msgstr ""

#: wppa-settings-autosave.php:9212 wppa-settings-autosave.php:9256
msgid "IPTC box"
msgstr ""

#: wppa-settings-autosave.php:9213 wppa-settings-autosave.php:9257
msgid "EXIF box"
msgstr ""

#: wppa-settings-autosave.php:9214 wppa-settings-autosave.php:9258
msgid "Social media share box"
msgstr ""

#: wppa-settings-autosave.php:9220 wppa-settings-autosave.php:9264
msgid "Move Up"
msgstr ""

#: wppa-settings-autosave.php:9235
msgid "NameDesc"
msgstr ""

#: wppa-settings-autosave.php:9250
msgid "Photo Name & Description Box"
msgstr ""

#: wppa-settings-autosave.php:9274
msgid "Swap Namedesc"
msgstr ""

#: wppa-settings-autosave.php:9275
msgid "Swap the order sequence of name and description"
msgstr ""

#: wppa-settings-autosave.php:9283
msgid "Split Name and Desc"
msgstr ""

#: wppa-settings-autosave.php:9284
msgid "Put Name and Description in separate boxes"
msgstr ""

#: wppa-settings-autosave.php:9287
msgid "Please reload this page after the green checkmark appears!"
msgstr ""

#: wppa-settings-autosave.php:9292
msgid "Source file management and other upload/import settings and actions."
msgstr ""

#: wppa-settings-autosave.php:9294
msgid "Keep sourcefiles admin"
msgstr ""

#: wppa-settings-autosave.php:9295
msgid "Keep the original uploaded and imported photo files."
msgstr ""

#: wppa-settings-autosave.php:9296 wppa-settings-autosave.php:9306
msgid ""
"The files will be kept in a separate directory with subdirectories for each "
"album"
msgstr ""

#: wppa-settings-autosave.php:9297 wppa-settings-autosave.php:9307
msgid ""
"These files can be used to update the photos used in displaying in wppa+ and "
"optionally for downloading original, un-downsized images."
msgstr ""

#: wppa-settings-autosave.php:9304
msgid "Keep sourcefiles frontend"
msgstr ""

#: wppa-settings-autosave.php:9305
msgid "Keep the original frontend uploaded photo files."
msgstr ""

#: wppa-settings-autosave.php:9314
msgid "Source directory"
msgstr ""

#: wppa-settings-autosave.php:9315
msgid "The path to the directory where the original photofiles will be saved."
msgstr ""

#: wppa-settings-autosave.php:9316
msgid "You may change the directory path, but it can not be an url."
msgstr ""

#: wppa-settings-autosave.php:9317
msgid ""
"The parent of the directory that you enter here must exist and be writable."
msgstr ""

#: wppa-settings-autosave.php:9318
msgid "The directory itsself will be created if it does not exist yet."
msgstr ""

#: wppa-settings-autosave.php:9325
msgid "Keep sync"
msgstr ""

#: wppa-settings-autosave.php:9326
msgid "Keep source synchronously with wppa system."
msgstr ""

#: wppa-settings-autosave.php:9327
msgid ""
"If checked, photos that are deleted from wppa, will also be removed from the "
"sourcefiles."
msgstr ""

#: wppa-settings-autosave.php:9328
msgid ""
"Also, copying or moving photos to different albums, will also copy/move the "
"sourcefiles."
msgstr ""

#: wppa-settings-autosave.php:9335
msgid "Remake add"
msgstr ""

#: wppa-settings-autosave.php:9336
msgid "Photos will be added from the source pool"
msgstr ""

#: wppa-settings-autosave.php:9337
msgid ""
"If checked: If photo files are found in the source directory that do not "
"exist in the corresponding album, they will be added to the album."
msgstr ""

#: wppa-settings-autosave.php:9344
msgid "Save IPTC data"
msgstr ""

#: wppa-settings-autosave.php:9345
msgid "Store the iptc data from the photo into the iptc db table"
msgstr ""

#: wppa-settings-autosave.php:9346
msgid ""
"You will need this if you enabled the display of iptc data in Table II-B17 "
"or if you use it in the photo descriptions."
msgstr ""

#: wppa-settings-autosave.php:9353
msgid "Save EXIF data"
msgstr ""

#: wppa-settings-autosave.php:9354
msgid "Store the exif data from the photo into the exif db table"
msgstr ""

#: wppa-settings-autosave.php:9355
msgid ""
"You will need this if you enabled the display of exif data in Table II-B18 "
"or if you use it in the photo descriptions."
msgstr ""

#: wppa-settings-autosave.php:9362
msgid "Max EXIF tag array size"
msgstr ""

#: wppa-settings-autosave.php:9363
msgid "Truncate array tags to ..."
msgstr ""

#: wppa-settings-autosave.php:9364
msgid "A value of 0 disables this feature"
msgstr ""

#: wppa-settings-autosave.php:9366
msgid "elements"
msgstr ""

#: wppa-settings-autosave.php:9371
msgid "Import Create page"
msgstr ""

#: wppa-settings-autosave.php:9372
msgid ""
"Create wp page that shows the album when a directory to album is imported."
msgstr ""

#: wppa-settings-autosave.php:9373
msgid ""
"As soon as an album is created when a directory is imported, a wp page is "
"made that displays the album content."
msgstr ""

#: wppa-settings-autosave.php:9381
msgid "Page content"
msgstr ""

#: wppa-settings-autosave.php:9382
msgid "The content of the page. Must contain <b>w#album</b>"
msgstr ""

#: wppa-settings-autosave.php:9383
msgid ""
"The content of the page. Note: it must contain w#album. This will be "
"replaced by the album number in the generated shortcode."
msgstr ""

#: wppa-settings-autosave.php:9391
msgid "Page type"
msgstr ""

#: wppa-settings-autosave.php:9392
msgid "Select the type of page to create."
msgstr ""

#: wppa-settings-autosave.php:9396
msgid "Post"
msgstr ""

#: wppa-settings-autosave.php:9403
msgid "Page status"
msgstr ""

#: wppa-settings-autosave.php:9404
msgid "Select the initial status of the page."
msgstr ""

#: wppa-settings-autosave.php:9408
msgid "Published"
msgstr ""

#: wppa-settings-autosave.php:9408
msgid "Draft"
msgstr ""

#: wppa-settings-autosave.php:9416
msgid "Permalink root"
msgstr ""

#: wppa-settings-autosave.php:9417
msgid "The name of the root for the photofile permalink structure."
msgstr ""

#: wppa-settings-autosave.php:9418
msgid ""
"Choose a convenient name like \"albums\" or so; this will be the name of a "
"folder inside .../wp-content/. Make sure you choose a unique name"
msgstr ""

#: wppa-settings-autosave.php:9419
msgid "If you make this field empty, the feature is disabled."
msgstr ""

#: wppa-settings-autosave.php:9427
msgid "Import parent check"
msgstr ""

#: wppa-settings-autosave.php:9428
msgid "Makes the album tree like the directory tree on Import Dirs to albums."
msgstr ""

#: wppa-settings-autosave.php:9429
msgid ""
"Untick only if all your albums have unique names. In this case additional "
"photos may be ftp'd to toplevel depot subdirs."
msgstr ""

#: wppa-settings-autosave.php:9436
msgid "Keep dir to album files"
msgstr ""

#: wppa-settings-autosave.php:9437
msgid "Keep imported files after dir to album import"
msgstr ""

#: wppa-settings-autosave.php:9446
msgid "Other plugins related settings"
msgstr ""

#: wppa-settings-autosave.php:9448
msgid "Foreign shortcodes general"
msgstr ""

#: wppa-settings-autosave.php:9449
msgid "Enable foreign shortcodes in album names, albums desc and photo names"
msgstr ""

#: wppa-settings-autosave.php:9457
msgid "Foreign shortcodes fullsize"
msgstr ""

#: wppa-settings-autosave.php:9458
msgid "Enable the use of non-wppa+ shortcodes in fullsize photo descriptions."
msgstr ""

#: wppa-settings-autosave.php:9459 wppa-settings-autosave.php:9470
msgid ""
"When checked, you can use shortcodes from other plugins in the description "
"of photos."
msgstr ""

#: wppa-settings-autosave.php:9460
msgid "The shortcodes will be expanded in the descriptions of fullsize images."
msgstr ""

#: wppa-settings-autosave.php:9461 wppa-settings-autosave.php:9472
msgid "You will most likely need also to check Table IX-A1 (Allow HTML)."
msgstr ""

#: wppa-settings-autosave.php:9468
msgid "Foreign shortcodes thumbnails"
msgstr ""

#: wppa-settings-autosave.php:9469
msgid "Enable the use of non-wppa+ shortcodes in thumbnail photo descriptions."
msgstr ""

#: wppa-settings-autosave.php:9471
msgid ""
"The shortcodes will be expanded in the descriptions of thumbnail images."
msgstr ""

#: wppa-settings-autosave.php:9479
msgid "Lightbox keyname"
msgstr ""

#: wppa-settings-autosave.php:9480
msgid "The identifier of lightbox."
msgstr ""

#: wppa-settings-autosave.php:9481
msgid ""
"If you use a lightbox plugin that uses rel=\"lbox-id\" you can enter the "
"lbox-id here."
msgstr ""

#: wppa-settings-autosave.php:9488
msgid "myCRED / Cube Points: Comment"
msgstr ""

#: wppa-settings-autosave.php:9489
msgid "Number of points for giving a comment"
msgstr ""

#: wppa-settings-autosave.php:9490 wppa-settings-autosave.php:9499
#: wppa-settings-autosave.php:9508 wppa-settings-autosave.php:9517
msgid "This setting requires the plugin myCRED or Cube Points"
msgstr ""

#: wppa-settings-autosave.php:9492 wppa-settings-autosave.php:9501
msgid "points per comment"
msgstr ""

#: wppa-settings-autosave.php:9497
msgid "myCRED / Cube Points: Appr Comment"
msgstr ""

#: wppa-settings-autosave.php:9498
msgid "Number of points for receiving an approved comment"
msgstr ""

#: wppa-settings-autosave.php:9506
msgid "myCRED / Cube Points: Rating"
msgstr ""

#: wppa-settings-autosave.php:9507
msgid "Number of points for a rating vote"
msgstr ""

#: wppa-settings-autosave.php:9510
msgid "points per vote"
msgstr ""

#: wppa-settings-autosave.php:9515
msgid "myCRED / Cube Points: Upload"
msgstr ""

#: wppa-settings-autosave.php:9516
msgid "Number of points for a successfull frontend upload"
msgstr ""

#: wppa-settings-autosave.php:9519
msgid "points per upload"
msgstr ""

#: wppa-settings-autosave.php:9524
msgid "Use SCABN"
msgstr ""

#: wppa-settings-autosave.php:9525
msgid "Use the wppa interface to Simple Cart & Buy Now plugin."
msgstr ""

#: wppa-settings-autosave.php:9526
msgid ""
"If checked, the shortcode to use for the \"add to cart\" button in photo "
"descriptions is [cart ...]"
msgstr ""

#: wppa-settings-autosave.php:9527
msgid ""
"as opposed to [scabn ...] for the original scabn \"add to cart\" button."
msgstr ""

#: wppa-settings-autosave.php:9528
msgid "The shortcode for the check-out page is still [scabn]"
msgstr ""

#: wppa-settings-autosave.php:9529
msgid ""
"The arguments are the same, the defaults are: name = photoname, price = 0.01."
msgstr ""

#: wppa-settings-autosave.php:9530
msgid ""
"Supplying the price should be sufficient; supply a name only when it differs "
"from the photo name."
msgstr ""

#: wppa-settings-autosave.php:9531
msgid "This shortcode handler will also work with Ajax enabled."
msgstr ""

#: wppa-settings-autosave.php:9532
msgid ""
"Using this interface makes sure that the item urls and callback action urls "
"are correct."
msgstr ""

#: wppa-settings-autosave.php:9539
msgid "Use CM Tooltip Glossary"
msgstr ""

#: wppa-settings-autosave.php:9540
msgid "Use plugin CM Tooltip Glossary on photo and album descriptions."
msgstr ""

#: wppa-settings-autosave.php:9541
msgid ""
"You MUST set Table IV-A13: Defer javascript, also if you do not want this "
"plugin to act on album and photo descriptions!"
msgstr ""

#: wppa-settings-autosave.php:9549
msgid "External services related settings and actions."
msgstr ""

#: wppa-settings-autosave.php:9551
msgid "QR Code widget size"
msgstr ""

#: wppa-settings-autosave.php:9552
msgid "The size of the QR code display."
msgstr ""

#: wppa-settings-autosave.php:9560
msgid "QR color"
msgstr ""

#: wppa-settings-autosave.php:9561
msgid "The display color of the qr code (dark)"
msgstr ""

#: wppa-settings-autosave.php:9562
msgid "This color MUST be given in hexadecimal format!"
msgstr ""

#: wppa-settings-autosave.php:9569
msgid "QR background color"
msgstr ""

#: wppa-settings-autosave.php:9570
msgid "The background color of the qr code (light)"
msgstr ""

#: wppa-settings-autosave.php:9578
msgid "QR cache"
msgstr ""

#: wppa-settings-autosave.php:9579
msgid "Enable caching QR codes"
msgstr ""

#: wppa-settings-autosave.php:9579
#, php-format
msgid "So far %d cache hits, %d miss"
msgstr ""

#: wppa-settings-autosave.php:9587
msgid "CDN Service"
msgstr ""

#: wppa-settings-autosave.php:9588
msgid "Select a CDN Service you want to use."
msgstr ""

#: wppa-settings-autosave.php:9591
msgid "Cloudinary in maintenance mode"
msgstr ""

#: wppa-settings-autosave.php:9601
msgid "Cloud name"
msgstr ""

#: wppa-settings-autosave.php:9610
msgid "API key"
msgstr ""

#: wppa-settings-autosave.php:9619
msgid "API secret"
msgstr ""

#: wppa-settings-autosave.php:9628
msgid "Delete all"
msgstr ""

#: wppa-settings-autosave.php:9629
msgid "Deletes them all !!!"
msgstr ""

#: wppa-settings-autosave.php:9637
msgid "Delete derived images"
msgstr ""

#: wppa-settings-autosave.php:9638
msgid "Deletes all derived images !!!"
msgstr ""

#: wppa-settings-autosave.php:9646
msgid "Max lifetime"
msgstr ""

#: wppa-settings-autosave.php:9647
msgid "Old images from local server, new images from Cloudinary."
msgstr ""

#: wppa-settings-autosave.php:9648
msgid ""
"If NOT set to Forever (0): You need to run Table VIII-B15 on a regular basis."
msgstr ""

#: wppa-settings-autosave.php:9650
msgid "Forever"
msgstr ""

#: wppa-settings-autosave.php:9681
msgid "Cloudinary usage"
msgstr ""

#: wppa-settings-autosave.php:9717
msgid "Cloudinary usage data not available"
msgstr ""

#: wppa-settings-autosave.php:9721
msgid "Cloudinary routines not installed."
msgstr ""

#: wppa-settings-autosave.php:9732
msgid "Cloudinary"
msgstr ""

#: wppa-settings-autosave.php:9733
msgid "<span style=\"color:red;\">Requires at least PHP version 5.3</span>"
msgstr ""

#: wppa-settings-autosave.php:9742
msgid "GPX Implementation"
msgstr ""

#: wppa-settings-autosave.php:9743
msgid "The way the maps are produced."
msgstr ""

#: wppa-settings-autosave.php:9744
msgid "Select the way the maps are produced."
msgstr ""

#: wppa-settings-autosave.php:9745
msgid ""
"When using an external plugin, most of the times you can not use Ajax (Table "
"IV-A1)."
msgstr ""

#: wppa-settings-autosave.php:9746
msgid ""
"Also: it may or may not be required to load the maps js api (Table IX-K5.1)"
msgstr ""

#: wppa-settings-autosave.php:9748
msgid "WPPA+ Embedded code"
msgstr ""

#: wppa-settings-autosave.php:9748
msgid "External plugin"
msgstr ""

#: wppa-settings-autosave.php:9756
msgid "Map height"
msgstr ""

#: wppa-settings-autosave.php:9757
msgid "The height of the map display."
msgstr ""

#: wppa-settings-autosave.php:9758
msgid "This setting is for embedded implementation only."
msgstr ""

#: wppa-settings-autosave.php:9765
msgid "Load maps api"
msgstr ""

#: wppa-settings-autosave.php:9766
msgid "Load the Google maps js api"
msgstr ""

#: wppa-settings-autosave.php:9767
msgid "If you use an external maps plugin, you may need to tick this box."
msgstr ""

#: wppa-settings-autosave.php:9774
msgid "Google maps API key"
msgstr ""

#: wppa-settings-autosave.php:9775
msgid "Enter your Google maps api key here if you have one."
msgstr ""

#: wppa-settings-autosave.php:9783
msgid "GPX Shortcode"
msgstr ""

#: wppa-settings-autosave.php:9784
msgid "The shortcode to be used for the gpx feature."
msgstr ""

#: wppa-settings-autosave.php:9785
msgid ""
"Enter / modify the shortcode to be generated for the gpx plugin. It must "
"contain w#lat and w#lon as placeholders for the latitude and longitude."
msgstr ""

#: wppa-settings-autosave.php:9786
msgid ""
"This item is required for using an external Google maps viewer plugin only"
msgstr ""

#: wppa-settings-autosave.php:9793
msgid "Fotomoto"
msgstr ""

#: wppa-settings-autosave.php:9794
msgid "Yes, we use Fotomoto on this site. Read the help text!"
msgstr ""

#: wppa-settings-autosave.php:9795
msgid "In order to function properly:"
msgstr ""

#: wppa-settings-autosave.php:9796
msgid "1. Get yourself a Fotomoto account."
msgstr ""

#: wppa-settings-autosave.php:9797
msgid ""
"2. Install the Fotomoto plugin, enter the \"Fotomoto Site Key:\" and check "
"the \"Use API Mode:\" checkbox."
msgstr ""

#: wppa-settings-autosave.php:9798
msgid "Note: Do NOT Disable the Custom box in Table II-B14."
msgstr ""

#: wppa-settings-autosave.php:9799
msgid "Do NOT remove the text w#fotomoto from the Custombox ( Table II-B15 )."
msgstr ""

#: wppa-settings-autosave.php:9807
msgid "Fotomoto fontsize"
msgstr ""

#: wppa-settings-autosave.php:9808
msgid "Fontsize for the Fotomoto toolbar."
msgstr ""

#: wppa-settings-autosave.php:9809
msgid ""
"If you set it here, it overrules a possible setting for font-size in ."
"FotomotoToolbarClass on the Fotomoto dashboard."
msgstr ""

#: wppa-settings-autosave.php:9817
msgid "Hide toolbar on running slideshows"
msgstr ""

#: wppa-settings-autosave.php:9818
msgid "The Fotomoto toolbar will re-appear when the slideshow stops."
msgstr ""

#: wppa-settings-autosave.php:9825
msgid "Fotomoto minwidth"
msgstr ""

#: wppa-settings-autosave.php:9826
msgid "Minimum width to display Fotomoto toolbar."
msgstr ""

#: wppa-settings-autosave.php:9827
msgid ""
"The display of the Fotomoto Toolbar will be suppressed on smaller slideshows."
msgstr ""

#: wppa-settings-autosave.php:9834
msgid "Image Magick"
msgstr ""

#: wppa-settings-autosave.php:9835
msgid "Absolute path to the ImageMagick commands"
msgstr ""

#: wppa-settings-autosave.php:9836
msgid ""
"If you want to use ImageMagick, enter the absolute path to the ImageMagick "
"commands"
msgstr ""

#: wppa-settings-autosave.php:9843
msgid "Photo shortcode related settings"
msgstr ""

#: wppa-settings-autosave.php:9845
msgid "Enable shortcode [photo ..]"
msgstr ""

#: wppa-settings-autosave.php:9846
msgid "Make the use of shortcode [photo ..] possible"
msgstr ""

#: wppa-settings-autosave.php:9847
msgid "Only disable this when there is a conflict with another plugin"
msgstr ""

#: wppa-settings-autosave.php:9854
msgid "Single image type"
msgstr ""

#: wppa-settings-autosave.php:9855
msgid "Specify the single image type the shortcode [photo ..] should show."
msgstr ""

#: wppa-settings-autosave.php:9858 wppa-tinymce-shortcodes.php:175
msgid "A plain single photo"
msgstr ""

#: wppa-settings-autosave.php:9859 wppa-tinymce-shortcodes.php:176
msgid "A single photo with caption"
msgstr ""

#: wppa-settings-autosave.php:9860 wppa-tinymce-shortcodes.php:177
msgid "A single photo with extended caption"
msgstr ""

#: wppa-settings-autosave.php:9861 wppa-tinymce-shortcodes.php:178
msgid "A single photo in the style of a slideshow"
msgstr ""

#: wppa-settings-autosave.php:9873 wppa-upload.php:291
msgid "Size"
msgstr ""

#: wppa-settings-autosave.php:9874
msgid "Specify the size (width) of the image."
msgstr ""

#: wppa-settings-autosave.php:9875
msgid "Use the same syntax as in the [wppa size=\"..\"] shortcode"
msgstr ""

#: wppa-settings-autosave.php:9876
msgid ""
"Examples: 350 for a fixed width of 350 pixels, or: 0.75 for a responsive "
"display of 75% width, or: auto,350 for responsive with a maximum of 350 "
"pixels."
msgstr ""

#: wppa-settings-autosave.php:9883
msgid "Align"
msgstr ""

#: wppa-settings-autosave.php:9884
msgid "Specify the alignment of the image."
msgstr ""

#: wppa-settings-autosave.php:9924
msgid "Table X:"
msgstr ""

#: wppa-settings-autosave.php:9924
msgid "IPTC Configuration:"
msgstr ""

#: wppa-settings-autosave.php:9925
msgid "This table defines the IPTC configuration"
msgstr ""

#: wppa-settings-autosave.php:9956 wppa-settings-autosave.php:10024
#: wppa-thumbnail-widget.php:224 wppa-topten-widget.php:341
msgid "Display"
msgstr ""

#: wppa-settings-autosave.php:9956 wppa-settings-autosave.php:10024
msgid "Hide"
msgstr ""

#: wppa-settings-autosave.php:9986
msgid "Table XI:"
msgstr ""

#: wppa-settings-autosave.php:9986
msgid "EXIF Configuration:"
msgstr ""

#: wppa-settings-autosave.php:9987
msgid "This table defines the EXIF configuration"
msgstr ""

#: wppa-settings-autosave.php:10010
msgid ""
"Function exif_read_data() does not exist. This means that <b>EXIF</b> is not "
"enabled. If you want to use <b>EXIF</b> data, ask your hosting provider to "
"add <b>'--enable-exif'</b> to the php <b>Configure Command</b>."
msgstr ""

#: wppa-settings-autosave.php:10054
msgid "Table XII:"
msgstr ""

#: wppa-settings-autosave.php:10054
msgid "WPPA+ and PHP Configuration:"
msgstr ""

#: wppa-settings-autosave.php:10055
msgid ""
"This table lists all WPPA+ constants and PHP server configuration parameters "
"and is read only"
msgstr ""

#: wppa-settings-autosave.php:10070
msgid "Value"
msgstr ""

#: wppa-settings-autosave.php:10076
msgid "Albums db table name."
msgstr ""

#: wppa-settings-autosave.php:10088
msgid "Photos db table name."
msgstr ""

#: wppa-settings-autosave.php:10100
msgid "Rating db table name."
msgstr ""

#: wppa-settings-autosave.php:10112
msgid "Comments db table name."
msgstr ""

#: wppa-settings-autosave.php:10124
msgid "IPTC db table name."
msgstr ""

#: wppa-settings-autosave.php:10136
msgid "EXIF db table name."
msgstr ""

#: wppa-settings-autosave.php:10148
msgid "Index db table name."
msgstr ""

#: wppa-settings-autosave.php:10160
msgid "Session db table name."
msgstr ""

#: wppa-settings-autosave.php:10172
msgid "Plugins main file name."
msgstr ""

#: wppa-settings-autosave.php:10179
msgid "WP absolute path."
msgstr ""

#: wppa-settings-autosave.php:10185
msgid "ABSPATH windows proof"
msgstr ""

#: wppa-settings-autosave.php:10191
msgid "Path to plugins directory."
msgstr ""

#: wppa-settings-autosave.php:10197
msgid "Plugins directory name."
msgstr ""

#: wppa-settings-autosave.php:10203
msgid "Plugins directory url."
msgstr ""

#: wppa-settings-autosave.php:10209
msgid "The relative upload directory."
msgstr ""

#: wppa-settings-autosave.php:10215
msgid "The upload directory path."
msgstr ""

#: wppa-settings-autosave.php:10221
msgid "The upload directory url."
msgstr ""

#: wppa-settings-autosave.php:10227
msgid "The relative depot directory."
msgstr ""

#: wppa-settings-autosave.php:10233
msgid "The depot directory path."
msgstr ""

#: wppa-settings-autosave.php:10239
msgid "The depot directory url."
msgstr ""

#: wppa-settings-autosave.php:10245
msgid "The path to wp-content."
msgstr ""

#: wppa-settings-autosave.php:10251
msgid "WP Content url."
msgstr ""

#: wppa-settings-autosave.php:10257
msgid "WP Base upload dir."
msgstr ""

#: wppa-settings-autosave.php:10277
#, php-format
msgid "<br />Memory used on this page: %6.2f Mb."
msgstr ""

#: wppa-settings-autosave.php:10278
#, php-format
msgid "<br />There are %d settings and %d runtime parameters."
msgstr ""

#: wppa-settings-autosave.php:10557 wppa-settings-autosave.php:10577
#: wppa-settings-autosave.php:10598
msgid "Warning!"
msgstr ""

#: wppa-settings-autosave.php:10578 wppa-settings-autosave.php:10598
msgid "Please read the help"
msgstr ""

#: wppa-settings-autosave.php:10809
msgid "Show!"
msgstr ""

#: wppa-settings-autosave.php:10829
msgid "Not done yet"
msgstr ""

#: wppa-settings-autosave.php:10836
msgid "Start as cron job"
msgstr ""

#: wppa-settings-autosave.php:10841 wppa-settings-autosave.php:10880
msgid "Locked!"
msgstr ""

#: wppa-settings-autosave.php:10851
msgid "Crashed!"
msgstr ""

#: wppa-settings-autosave.php:10866
msgid "Click me to resume"
msgstr ""

#: wppa-settings-autosave.php:10875
msgid "Start!"
msgstr ""

#: wppa-settings-autosave.php:10916
msgid ""
"You can not have popup and lightbox on thumbnails at the same time. Uncheck "
"either Table IV-C8 or choose a different linktype in Table VI-2."
msgstr ""

#: wppa-settings-autosave.php:10919
msgid ""
"It is important that you select a page that contains at least [wppa][/wppa]."
msgstr ""

#: wppa-settings-autosave.php:10920
msgid ""
"If you omit this, the link will not work at all or simply refresh the "
"(home)page."
msgstr ""

#: wppa-setup.php:615
msgid "IMPORTANT UPGRADE NOTICE"
msgstr ""

#: wppa-setup.php:616
msgid ""
"Please CHECK your customized WPPA-STYLE.CSS file against the newly supplied "
"one. You may wish to add or modify some attributes. Be aware of the fact "
"that most settings can now be set in the admin settings page."
msgstr ""

#: wppa-setup.php:617
msgid ""
"Please REPLACE your customized WPPA-THEME.PHP file by the newly supplied "
"one, or just remove it from your theme directory. You may modify it later if "
"you wish. Your current customized version is NOT compatible with this "
"version of the plugin software."
msgstr ""

#: wppa-setup.php:645
#, php-format
msgid "WPPA+ successfully updated in multi site mode to db version %s."
msgstr ""

#: wppa-setup.php:648
#, php-format
msgid "WPPA+ successfully updated in single site mode to db version %s."
msgstr ""

#: wppa-setup.php:653
msgid "An error occurred during update"
msgstr ""

#: wppa-setup.php:971
msgid ""
"<span style=\"color:red\" >Warning: Do not upload copyrighted material!</"
"span>"
msgstr ""

#: wppa-setup.php:980 wppa-setup.php:984 wppa-setup.php:988
msgid "Select tags:"
msgstr ""

#: wppa-setup.php:991
msgid "Enter new tags:"
msgstr ""

#: wppa-setup.php:1215
msgid "Vote for me!"
msgstr ""

#: wppa-setup.php:1216
msgid "Voted for me"
msgstr ""

#: wppa-setup.php:1603
msgid "NEW"
msgstr ""

#: wppa-setup.php:1605
msgid "MODIFIED"
msgstr ""

#: wppa-setup.php:1659
msgid "Search in current section"
msgstr ""

#: wppa-setup.php:1660
msgid "Search in current results"
msgstr ""

#: wppa-setup.php:1747
msgid "Type your custom url here"
msgstr ""

#: wppa-setup.php:1748
msgid "Type the title here"
msgstr ""

#: wppa-setup.php:1767 wppa-topten-widget.php:35 wppa-topten-widget.php:49
#: wppa-topten-widget.php:311
msgid "Top Ten Photos"
msgstr ""

#: wppa-setup.php:1770 wppa-thumbnail-widget.php:36
#: wppa-thumbnail-widget.php:167
msgid "Thumbnail Photos"
msgstr ""

#: wppa-setup.php:1773
msgid "Search photos"
msgstr ""

#: wppa-setup.php:1817
msgid ""
"The uploads directory does not exist, please do a regular WP upload first."
msgstr ""

#: wppa-setup.php:1821
msgid "Successfully created uploads directory."
msgstr ""

#: wppa-setup.php:1832
msgid "Could not create the wppa directory."
msgstr ""

#: wppa-setup.php:1836
msgid "Successfully created wppa directory."
msgstr ""

#: wppa-setup.php:1846
msgid "Could not create the wppa thumbs directory."
msgstr ""

#: wppa-setup.php:1850
msgid "Successfully created wppa thumbs directory."
msgstr ""

#: wppa-setup.php:1860
msgid "Could not create the wppa watermarks directory."
msgstr ""

#: wppa-setup.php:1864
msgid "Successfully created wppa watermarks directory."
msgstr ""

#: wppa-setup.php:1874
msgid "Could not create the wppa fonts directory."
msgstr ""

#: wppa-setup.php:1878
msgid "Successfully created wppa fonts directory."
msgstr ""

#: wppa-setup.php:1890
msgid "Unable to create depot directory."
msgstr ""

#: wppa-setup.php:1894
msgid "Successfully created wppa depot directory."
msgstr ""

#: wppa-setup.php:1905
msgid "Unable to create user depot directory"
msgstr ""

#: wppa-setup.php:1909
msgid "Successfully created wppa user depot directory."
msgstr ""

#: wppa-setup.php:1919
msgid "Unable to create temp directory"
msgstr ""

#: wppa-setup.php:1923
msgid "Successfully created temp directory."
msgstr ""

#: wppa-setup.php:1931
#, php-format
msgid ""
"Ask your administrator to give you more rights, or create <b>%s</b> manually "
"using an FTP program."
msgstr ""

#: wppa-setup.php:2033
msgid "Default photo album for"
msgstr "默认照片专辑"

#: wppa-slideshow-widget.php:18
msgid "Display a slideshow"
msgstr ""

#: wppa-slideshow-widget.php:19
msgid "WPPA+ Sidebar Slideshow"
msgstr ""

#: wppa-slideshow-widget.php:37 wppa-slideshow-widget.php:169
msgid "Sidebar Slideshow"
msgstr ""

#: wppa-slideshow-widget.php:61
msgid "Widget landing page"
msgstr ""

#: wppa-slideshow-widget.php:130
msgid "Unknown album or album does not exist"
msgstr ""

#: wppa-slideshow-widget.php:201
msgid "Sizes and alignment"
msgstr ""

#: wppa-slideshow-widget.php:203
msgid ""
"Enter the width and optionally the height of the area wherein the slides "
"will appear. If you specify a 0 for the height, it will be calculated. The "
"value for the height will be ignored if you set the vertical alignment to "
"'fit'."
msgstr ""

#: wppa-slideshow-widget.php:205
msgid ""
"Tick the portrait only checkbox if there are only portrait images in the "
"album and you want the photos to fill the full width of the widget."
msgstr ""

#: wppa-slideshow-widget.php:207
msgid ""
"If portrait only is checked, the vertical alignment will be forced to 'fit'."
msgstr ""

#: wppa-slideshow-widget.php:214
msgid "Width in pixels"
msgstr ""

#: wppa-slideshow-widget.php:226
msgid "Height in pixels"
msgstr ""

#: wppa-slideshow-widget.php:238
msgid "Portrait only:"
msgstr ""

#: wppa-slideshow-widget.php:261
msgid "Set the desired vertical alignment method."
msgstr ""

#: wppa-slideshow-widget.php:270
msgid "Slideshow timeout in seconds"
msgstr ""

#: wppa-slideshow-widget.php:277
msgid ""
"If you want that a click on the image links to another web address, type the "
"full url here."
msgstr ""

#: wppa-slideshow-widget.php:281
msgid "Show name"
msgstr ""

#: wppa-slideshow-widget.php:284
msgid "Show description"
msgstr ""

#: wppa-slideshow-widget.php:287
msgid "Show filmstrip"
msgstr ""

#: wppa-slideshow-widget.php:290
msgid "Show browsebar"
msgstr ""

#: wppa-slideshow-widget.php:293
msgid "Show number bar"
msgstr ""

#: wppa-slideshow-widget.php:297
msgid "The following text fields support qTranslate"
msgstr ""

#: wppa-slideshow-widget.php:301
msgid "Tooltip text"
msgstr ""

#: wppa-slideshow-widget.php:304
msgid "Text above photos"
msgstr ""

#: wppa-slideshow-widget.php:307
msgid "Text below photos"
msgstr ""

#: wppa-slideshow.php:270
msgid "Start / stop slideshow"
msgstr ""

#: wppa-slideshow.php:348
msgid "Paused"
msgstr "未知位置-03"

#: wppa-slideshow.php:356
msgid ""
"To see the full size images, you need to enable javascript in your browser."
msgstr "要看到完整大小的图像，您必须启用浏览器的JavaScript。"

#: wppa-slideshow.php:762
msgid "Checkout"
msgstr "结账"

#: wppa-slideshow.php:880 wppa-slideshow.php:909 wppa-slideshow.php:1184
#: wppa-slideshow.php:1254
#, php-format
msgid "You must <a href=\"%s\">login</a> to vote"
msgstr "你必须<a href=\"%s\">登录</a>到投票"

#: wppa-slideshow.php:883 wppa-slideshow.php:912 wppa-slideshow.php:1187
#: wppa-slideshow.php:1257
msgid "You must login to vote"
msgstr "你必须登录到投票"

#: wppa-slideshow.php:894
#, php-format
msgid "Number of votes: <span id=\"wppa-vote-count-%s\" >%s</span>&nbsp;"
msgstr "投票數<span id=\"wppa-vote-count-%s\" >%s</span>&nbsp;"

#: wppa-slideshow.php:964
msgid "Sorry, you can rate a photo only once"
msgstr ""

#: wppa-slideshow.php:996
msgid "very low"
msgstr "极低"

#: wppa-slideshow.php:997
msgid "low"
msgstr "低"

#: wppa-slideshow.php:998
msgid "average"
msgstr "平均"

#: wppa-slideshow.php:999
msgid "high"
msgstr "高"

#: wppa-slideshow.php:1000
msgid "very high"
msgstr "非常高"

#: wppa-slideshow.php:1085 wppa-slideshow.php:1216
msgid "Are you sure you want to mark this image as inappropriate?"
msgstr "你确定要纪念这个形象，不恰当的？"

#: wppa-slideshow.php:1088 wppa-slideshow.php:1219
msgid "Click this if you do NOT like this image!"
msgstr "点击这个，如果你不喜欢这个形象！"

#: wppa-slideshow.php:1120 wppa-slideshow.php:1243
msgid "Number of people who marked this photo as inappropriate"
msgstr "标志着这张照片是不恰当的人数"

#: wppa-slideshow.php:1249
msgid "My rating:"
msgstr "我的评价："

#: wppa-slideshow.php:1344
msgid "First"
msgstr "名"

#: wppa-slideshow.php:1358
msgid "Last"
msgstr "姓"

#: wppa-slideshow.php:1535
#, php-format
msgid "Photo %s of %s"
msgstr "%s 的照片 %s"

#: wppa-slideshow.php:1600 wppa-slideshow.php:1643
msgid "Click to start/stop"
msgstr "单击开始/停止"

#: wppa-slideshow.php:1655
msgid "- - - Comments box activated - - -"
msgstr "-   -   - 意见栏中激活 -   -   -"

#: wppa-slideshow.php:1679
msgid "- - - IPTC box activated - - -"
msgstr "-   -   -  IPTC箱激活 -   -   -"

#: wppa-slideshow.php:1703
msgid "- - - EXIF box activated - - -"
msgstr "-   -   -  EXIF​​箱激活 -   -   -"

#: wppa-statistics.php:27
#, php-format
msgid "There is %d photo album"
msgid_plural "There are %d photo albums"
msgstr[0] ""

#: wppa-statistics.php:28
msgid "The last album added is"
msgstr ""

#: wppa-statistics.php:32
msgid ", a subalbum of"
msgstr "，子专辑"

#: wppa-stereo-widget.php:13
msgid "Display stereo photo settings dialog"
msgstr ""

#: wppa-stereo-widget.php:14
msgid "WPPA+ Stereo Photo Settings"
msgstr ""

#: wppa-stereo-widget.php:34 wppa-stereo-widget.php:65
msgid "Stereo Photo Settings"
msgstr ""

#: wppa-stereo.php:32
msgid "Half color"
msgstr ""

#: wppa-stereo.php:33
msgid "Gray"
msgstr ""

#: wppa-stereo.php:34
msgid "True anaglyph"
msgstr ""

#: wppa-stereo.php:35
msgid "Optimized"
msgstr ""

#: wppa-stereo.php:36
msgid "Flat"
msgstr ""

#: wppa-stereo.php:38
msgid "Red - Cyan"
msgstr ""

#: wppa-stereo.php:39
msgid "Green - Magenta"
msgstr ""

#: wppa-super-view-widget.php:14
msgid "Display a super selection dialog"
msgstr ""

#: wppa-super-view-widget.php:15
msgid "WPPA+ Super View"
msgstr ""

#: wppa-super-view-widget.php:33 wppa-super-view-widget.php:66
msgid "Super View"
msgstr ""

#: wppa-super-view-widget.php:78
msgid "Enable (sub)albums of"
msgstr ""

#: wppa-super-view-widget.php:85
msgid "Sort alphabetically"
msgstr ""

#: wppa-super-view-widget.php:86
msgid "If unticked, the album sort method for the album or system will be used"
msgstr ""

#: wppa-tagcloud-widget.php:14
msgid "Display a cloud of photo tags"
msgstr ""

#: wppa-tagcloud-widget.php:15
msgid "WPPA+ Photo Tag Cloud"
msgstr ""

#: wppa-tagcloud-widget.php:35 wppa-tagcloud-widget.php:65
msgid "Photo Tag Cloud"
msgstr ""

#: wppa-thumbnail-widget.php:13
msgid "Display thumbnails of the photos in an album"
msgstr ""

#: wppa-thumbnail-widget.php:14
msgid "WPPA+ Thumbnail Photos"
msgstr ""

#: wppa-thumbnail-widget.php:49
msgid "Thumbnail photos"
msgstr ""

#: wppa-thumbnail-widget.php:182
msgid "Link from the title"
msgstr ""

#: wppa-thumbnail-widget.php:185
msgid "Link Title ( tooltip )"
msgstr ""

#: wppa-thumbnail-widget.php:214
msgid "Max number"
msgstr ""

#: wppa-thumbnail-widget.php:228
msgid "Show photo names under thumbnails"
msgstr ""

#: wppa-thumbnail-widget.php:233
msgid "Table I-F5 and 6"
msgstr ""

#: wppa-thumbnails.php:181 wppa-thumbnails.php:1179 wppa-topten-widget.php:167
msgid "View the top rated photos"
msgstr "看最精彩的照片"

#: wppa-thumbnails.php:1086
#, php-format
msgid "Missing thumbnail image #%s"
msgstr "缺少缩略图＃%s"

#: wppa-tinymce-shortcodes.php:59
msgid "No Preview available"
msgstr ""

#: wppa-tinymce-shortcodes.php:64
msgid "Unimplemented virtual album"
msgstr ""

#: wppa-tinymce-shortcodes.php:128
msgid "Type of WPPA display:"
msgstr ""

#: wppa-tinymce-shortcodes.php:131
msgid "Please select a display type"
msgstr ""

#: wppa-tinymce-shortcodes.php:132
msgid "A gallery with covers and/or thumbnails"
msgstr ""

#: wppa-tinymce-shortcodes.php:133
msgid "A slideshow"
msgstr ""

#: wppa-tinymce-shortcodes.php:134
msgid "A single image"
msgstr ""

#: wppa-tinymce-shortcodes.php:135
msgid "A search/selection box"
msgstr ""

#: wppa-tinymce-shortcodes.php:136
msgid "An other box type"
msgstr ""

#: wppa-tinymce-shortcodes.php:143
msgid "Type of gallery display:"
msgstr ""

#: wppa-tinymce-shortcodes.php:146
msgid "Please select a gallery type"
msgstr ""

#: wppa-tinymce-shortcodes.php:147
msgid "The cover(s) of specific album(s)"
msgstr ""

#: wppa-tinymce-shortcodes.php:148
msgid "The content of specific album(s)"
msgstr ""

#: wppa-tinymce-shortcodes.php:149
msgid "The covers of the subalbums of specific album(s)"
msgstr ""

#: wppa-tinymce-shortcodes.php:150
msgid "The thumbnails of specific album(s)"
msgstr ""

#: wppa-tinymce-shortcodes.php:157
msgid "Type of slideshow:"
msgstr ""

#: wppa-tinymce-shortcodes.php:160
msgid "Please select a slideshow type"
msgstr ""

#: wppa-tinymce-shortcodes.php:161
msgid "A fully featured slideshow"
msgstr ""

#: wppa-tinymce-shortcodes.php:162
msgid "A slideshow without supporting boxes"
msgstr ""

#: wppa-tinymce-shortcodes.php:163
msgid "A slideshow with a filmstrip only"
msgstr ""

#: wppa-tinymce-shortcodes.php:164
msgid "A filmstrip only"
msgstr ""

#: wppa-tinymce-shortcodes.php:171
msgid "Type of single image:"
msgstr ""

#: wppa-tinymce-shortcodes.php:174
msgid "Please select a single image type"
msgstr ""

#: wppa-tinymce-shortcodes.php:185
msgid "Type of search:"
msgstr ""

#: wppa-tinymce-shortcodes.php:188
msgid "Please select a search type"
msgstr ""

#: wppa-tinymce-shortcodes.php:189
msgid "A search box"
msgstr ""

#: wppa-tinymce-shortcodes.php:190
msgid "A supersearch box"
msgstr ""

#: wppa-tinymce-shortcodes.php:191
msgid "A tagcloud box"
msgstr ""

#: wppa-tinymce-shortcodes.php:192
msgid "A multitag box"
msgstr ""

#: wppa-tinymce-shortcodes.php:193
msgid "A superview box"
msgstr ""

#: wppa-tinymce-shortcodes.php:194
msgid "A calendar box"
msgstr ""

#: wppa-tinymce-shortcodes.php:201
msgid "Type miscellaneous:"
msgstr ""

#: wppa-tinymce-shortcodes.php:204
msgid "Please select a miscellaneous display"
msgstr ""

#: wppa-tinymce-shortcodes.php:205
msgid "A generic albums display"
msgstr ""

#: wppa-tinymce-shortcodes.php:206
msgid "An upload box"
msgstr ""

#: wppa-tinymce-shortcodes.php:207
msgid "A landing page shortcode"
msgstr ""

#: wppa-tinymce-shortcodes.php:208
msgid "A 3D stereo settings box"
msgstr ""

#: wppa-tinymce-shortcodes.php:209
msgid "An admins choice box"
msgstr ""

#: wppa-tinymce-shortcodes.php:216
msgid "Users:"
msgstr ""

#: wppa-tinymce-shortcodes.php:219
msgid "All"
msgstr ""

#: wppa-tinymce-shortcodes.php:236
msgid "Kind of selection:"
msgstr ""

#: wppa-tinymce-shortcodes.php:239
msgid "Please select a type of selection to be used"
msgstr ""

#: wppa-tinymce-shortcodes.php:240
msgid "One or more wppa+ albums"
msgstr ""

#: wppa-tinymce-shortcodes.php:241
msgid "A special selection"
msgstr ""

#: wppa-tinymce-shortcodes.php:248 wppa-tinymce-shortcodes.php:268
msgid "The selection to be used:"
msgstr ""

#: wppa-tinymce-shortcodes.php:251 wppa-tinymce-shortcodes.php:271
msgid "Please select a virtual album"
msgstr ""

#: wppa-tinymce-shortcodes.php:252 wppa-tinymce-shortcodes.php:272
msgid "The most recently modified album"
msgstr ""

#: wppa-tinymce-shortcodes.php:253
msgid "The top rated photos"
msgstr ""

#: wppa-tinymce-shortcodes.php:254
msgid "The most recently uploaded photos"
msgstr ""

#: wppa-tinymce-shortcodes.php:255
msgid "A random selection of featured photos"
msgstr ""

#: wppa-tinymce-shortcodes.php:256
msgid "The most recently commented photos"
msgstr ""

#: wppa-tinymce-shortcodes.php:257
msgid "Photos tagged with certain tags"
msgstr ""

#: wppa-tinymce-shortcodes.php:258
msgid "Albums tagged with a certain category"
msgstr ""

#: wppa-tinymce-shortcodes.php:259
msgid "Photos in albums owned by a certain user"
msgstr ""

#: wppa-tinymce-shortcodes.php:260
msgid "Photos uploaded by a certain user"
msgstr ""

#: wppa-tinymce-shortcodes.php:261
msgid "All photos in the system"
msgstr ""

#: wppa-tinymce-shortcodes.php:273
msgid "Albums owned by a certain user"
msgstr ""

#: wppa-tinymce-shortcodes.php:274
msgid "Albums tagged with certain categories"
msgstr ""

#: wppa-tinymce-shortcodes.php:275
msgid "All albums in the system"
msgstr ""

#: wppa-tinymce-shortcodes.php:282 wppa-tinymce-shortcodes.php:315
msgid "The Album(s) to be used:"
msgstr ""

#: wppa-tinymce-shortcodes.php:288
msgid "Please select one or more albums"
msgstr ""

#: wppa-tinymce-shortcodes.php:297 wppa-tinymce-shortcodes.php:330
#: wppa-tinymce-shortcodes.php:375 wppa-tinymce-shortcodes.php:398
#: wppa-tinymce-shortcodes.php:586
msgid "There are no albums yet"
msgstr ""

#: wppa-tinymce-shortcodes.php:303
msgid "Filter album:"
msgstr ""

#: wppa-tinymce-shortcodes.php:308
msgid ""
"Enter a (part of) the album name to limit the options in the selection box "
"above."
msgstr ""

#: wppa-tinymce-shortcodes.php:338
msgid "The album owner:"
msgstr ""

#: wppa-tinymce-shortcodes.php:341
msgid "Please select a user"
msgstr ""

#: wppa-tinymce-shortcodes.php:342
msgid "The logged in visitor"
msgstr ""

#: wppa-tinymce-shortcodes.php:348
msgid "Too many users, edit manually"
msgstr ""

#: wppa-tinymce-shortcodes.php:363
msgid "No parent specification"
msgstr ""

#: wppa-tinymce-shortcodes.php:366 wppa-tinymce-shortcodes.php:389
#: wppa-tinymce-shortcodes.php:577
msgid "The generic parent"
msgstr ""

#: wppa-tinymce-shortcodes.php:406
msgid "Max Albums:"
msgstr ""

#: wppa-tinymce-shortcodes.php:414
msgid "Max Photos:"
msgstr ""

#: wppa-tinymce-shortcodes.php:422
msgid "The album cat(s):"
msgstr ""

#: wppa-tinymce-shortcodes.php:425
msgid "--- please select category ---"
msgstr ""

#: wppa-tinymce-shortcodes.php:436
msgid "The Photo to be used:"
msgstr ""

#: wppa-tinymce-shortcodes.php:442
msgid "Please select a photo"
msgstr ""

#: wppa-tinymce-shortcodes.php:443 wppa-tinymce-shortcodes.php:457
msgid "The photo of the day"
msgstr ""

#: wppa-tinymce-shortcodes.php:456
msgid "The most recently uploaded photo"
msgstr ""

#: wppa-tinymce-shortcodes.php:460
msgid "There are no photos yet"
msgstr ""

#: wppa-tinymce-shortcodes.php:466
msgid "Specify the photo to be used"
msgstr ""

#: wppa-tinymce-shortcodes.php:467
msgid "You can select from a maximum of 100 most recently added photos"
msgstr ""

#: wppa-tinymce-shortcodes.php:474
msgid "Preview image:"
msgstr ""

#: wppa-tinymce-shortcodes.php:481
msgid "The photo tag(s):"
msgstr ""

#: wppa-tinymce-shortcodes.php:484
msgid "--- please select tag(s) ---"
msgstr ""

#: wppa-tinymce-shortcodes.php:495
msgid "Or / And:"
msgstr ""

#: wppa-tinymce-shortcodes.php:497
msgid "Meet any"
msgstr ""

#: wppa-tinymce-shortcodes.php:498
msgid "Meet all"
msgstr ""

#: wppa-tinymce-shortcodes.php:504 wppa-tinymce-shortcodes.php:552
msgid "Additional features:"
msgstr ""

#: wppa-tinymce-shortcodes.php:506
msgid "Enable Subsearch"
msgstr ""

#: wppa-tinymce-shortcodes.php:507
msgid "Enable Rootsearch"
msgstr ""

#: wppa-tinymce-shortcodes.php:513
msgid "Search root:"
msgstr ""

#: wppa-tinymce-shortcodes.php:532
msgid "Landing page:"
msgstr ""

#: wppa-tinymce-shortcodes.php:554
msgid "Enable all tags"
msgstr ""

#: wppa-tinymce-shortcodes.php:557
msgid "Please select the tags to show"
msgstr ""

#: wppa-tinymce-shortcodes.php:563
msgid "There are no tags"
msgstr ""

#: wppa-tinymce-shortcodes.php:594
msgid "Calendar type:"
msgstr ""

#: wppa-tinymce-shortcodes.php:597
msgid "By EXIF date"
msgstr ""

#: wppa-tinymce-shortcodes.php:598
msgid "By date of upload"
msgstr ""

#: wppa-tinymce-shortcodes.php:599
msgid "By date last modified"
msgstr ""

#: wppa-tinymce-shortcodes.php:602
msgid "Last date first"
msgstr ""

#: wppa-tinymce-shortcodes.php:603
msgid "Initially display all"
msgstr ""

#: wppa-tinymce-shortcodes.php:609
msgid "The size of the display:"
msgstr ""

#: wppa-tinymce-shortcodes.php:614
msgid ""
"Specify the horizontal size in pixels or <span style=\"color:blue\" >auto</"
"span>."
msgstr ""

#: wppa-tinymce-shortcodes.php:615
msgid ""
"A value less than <span style=\"color:blue\" >100</span> will automatically "
"be interpreted as a <span style=\"color:blue\" >percentage</span> of the "
"available space."
msgstr ""

#: wppa-tinymce-shortcodes.php:616
msgid ""
"For responsive with a fixed maximum, add the max to auto e.g. <span style="
"\"color:blue\" >auto,550</span>"
msgstr ""

#: wppa-tinymce-shortcodes.php:617
msgid "Leave this blank for default size"
msgstr ""

#: wppa-tinymce-shortcodes.php:633
msgid "Specify the alignment to be used or --- none ---"
msgstr ""

#: wppa-tinymce-shortcodes.php:641
msgid ""
"This is a preview of the shortcode that is being generated. You may edit the "
"comment"
msgstr ""

#: wppa-tinymce-shortcodes.php:643 wppa-tinymce-shortcodes.php:644
msgid "Insert Shortcode"
msgstr ""

#: wppa-tinymce-shortcodes.php:644
msgid "Please complete the shortcode specs"
msgstr ""

#: wppa-topten-widget.php:13
msgid "Display top rated photos"
msgstr ""

#: wppa-topten-widget.php:14
msgid "WPPA+ Top Ten Photos"
msgstr ""

#: wppa-topten-widget.php:50
msgid "Top Ten Photo album"
msgstr ""

#: wppa-topten-widget.php:346
msgid "Number of views"
msgstr ""

#: wppa-topten-widget.php:357
msgid "Include sub albums"
msgstr ""

#: wppa-topten-widget.php:361
msgid "Only with medals"
msgstr ""

#: wppa-topten-widget.php:365
msgid "Subtitles"
msgstr ""

#: wppa-topten-widget.php:375
msgid "Mean rating"
msgstr ""

#: wppa-topten-widget.php:381
msgid "View count"
msgstr ""

#: wppa-topten-widget.php:389
msgid "Table I-F1 and 2"
msgstr ""

#: wppa-upldr-widget.php:15
msgid "Display which users uploaded how many photos"
msgstr ""

#: wppa-upldr-widget.php:16
msgid "WPPA+ Uploader Photos"
msgstr ""

#: wppa-upldr-widget.php:37
msgid "Uploader Photoswp-photo-album-plus"
msgstr ""

#: wppa-upldr-widget.php:43
msgid "User uploaded photos"
msgstr ""

#: wppa-upldr-widget.php:130
#, php-format
msgid "Number of contributors: %d"
msgid_plural "Number of contributors: %d"
msgstr[0] ""

#: wppa-upldr-widget.php:133
#, php-format
msgid "Number of photos: %d"
msgid_plural "Number of photos: %d"
msgstr[0] ""

#: wppa-upldr-widget.php:188
msgid "Uploader Photos"
msgstr ""

#: wppa-upldr-widget.php:202
msgid "Number of photos"
msgstr ""

#: wppa-upldr-widget.php:203
msgid "Most recent photo"
msgstr ""

#: wppa-upldr-widget.php:217
msgid "Ignore"
msgstr ""

#: wppa-upldr-widget.php:218
msgid "Enter loginnames seperated by commas"
msgstr ""

#: wppa-upldr-widget.php:223
msgid "Look only in albums (including sub-albums):"
msgstr ""

#: wppa-upldr-widget.php:257
msgid "Show count of owners"
msgstr ""

#: wppa-upldr-widget.php:261
msgid "Show count of photos"
msgstr ""

#: wppa-upload-widget.php:13
msgid "Display upload photos dialog"
msgstr ""

#: wppa-upload-widget.php:14
msgid "WPPA+ Upload Photos"
msgstr ""

#: wppa-upload.php:80 wppa-upload.php:97
msgid "Connecting to edit album..."
msgstr ""

#: wppa-upload.php:86 wppa-upload.php:103
msgid "Connecting to edit photos..."
msgstr ""

#: wppa-upload.php:113
msgid "Connecting to your depot..."
msgstr ""

#: wppa-upload.php:158
msgid "before you can upload your photos."
msgstr ""

#: wppa-upload.php:167
msgid "There are no albums where you are allowed to upload photos to."
msgstr ""

#: wppa-upload.php:169
msgid ""
"Ask your administrator to create at least one album that is accessible for "
"you to upload to, or ask him to give you album admin rights."
msgstr ""

#: wppa-upload.php:181
msgid "Upload a single photo"
msgstr ""

#: wppa-upload.php:211 wppa-upload.php:358 wppa-upload.php:435
msgid "Please select an album"
msgstr ""

#: wppa-upload.php:238
#, php-format
msgid ""
"<b>Notice:</b> your server allows you to upload <b>%s</b> files of maximum "
"total <b>%s</b> bytes and allows <b>%s</b> seconds to complete."
msgstr ""

#: wppa-upload.php:240
msgid ""
"If your request exceeds these limitations, it will fail, probably without an "
"errormessage."
msgstr ""

#: wppa-upload.php:242
msgid ""
"Additionally your hosting provider may have set other limitations on "
"uploading files."
msgstr ""

#: wppa-upload.php:258
msgid "Box A:"
msgstr ""

#: wppa-upload.php:258
msgid "Multiple Photos in one selection"
msgstr ""

#: wppa-upload.php:260
#, php-format
msgid "You can select up to %s photos in one selection and upload them."
msgstr ""

#: wppa-upload.php:263
msgid "You need a modern browser that supports HTML-5 to select multiple files"
msgstr ""

#: wppa-upload.php:279 wppa-upload.php:288 wppa-upload.php:404
msgid "Selected Files:"
msgstr ""

#: wppa-upload.php:292
msgid "Type"
msgstr ""

#: wppa-upload.php:313
msgid "Too many!"
msgstr ""

#: wppa-upload.php:317
msgid "Too big!"
msgstr ""

#: wppa-upload.php:321
msgid "Try again!"
msgstr ""

#: wppa-upload.php:323
msgid "Total"
msgstr ""

#: wppa-upload.php:357
msgid "Upload Multiple Photos"
msgstr ""

#: wppa-upload.php:375 wppa-upload.php:451
msgid "After upload: Go to the <b>Edit Album</b> page."
msgstr ""

#: wppa-upload.php:378 wppa-upload.php:454
msgid "After upload: Go to the <b>Edit Photos</b> page."
msgstr ""

#: wppa-upload.php:389
msgid "Box B:"
msgstr ""

#: wppa-upload.php:389
msgid "Single Photos in multiple selections"
msgstr ""

#: wppa-upload.php:391
#, php-format
msgid "You can select up to %s photos one by one and upload them at once."
msgstr ""

#: wppa-upload.php:434
msgid "Upload Single Photos"
msgstr ""

#: wppa-upload.php:473
msgid "Box C:"
msgstr ""

#: wppa-upload.php:473
msgid "Zipped Photos in one selection"
msgstr ""

#: wppa-upload.php:475
#, php-format
msgid ""
"You can upload one zipfile. It will be placed in your personal wppa-depot: "
"<b>.../%s</b><br/>Once uploaded, use <b>Import Photos</b> to unzip the file "
"and place the photos in any album."
msgstr ""

#: wppa-upload.php:492
msgid "Upload Zipped Photos"
msgstr ""

#: wppa-upload.php:504
msgid "After upload: Go to the <b>Import Photos</b> page."
msgstr ""

#: wppa-upload.php:512
msgid ""
"Ask your administrator to upgrade php to version 5.2.7 or later. This will "
"enable you to upload zipped photos."
msgstr ""

#: wppa-upload.php:537
#, php-format
msgid "Time out. %s photos uploaded in album nr %s."
msgstr ""

#: wppa-upload.php:560 wppa-upload.php:594
msgid "Photos Uploaded in album nr"
msgstr ""

#: wppa-upload.php:635
msgid "Zipfile"
msgstr ""

#: wppa-upload.php:635
msgid "sucessfully uploaded."
msgstr ""

#: wppa-upload.php:636
msgid "during upload."
msgstr ""

#: wppa-utils.php:345 wppa-utils.php:372
#, php-format
msgid "Rating: %s"
msgstr ""

#: wppa-utils.php:838 wppa-utils.php:849 wppa-utils.php:860
msgid "Notification of inappropriate image"
msgstr ""

#: wppa-utils.php:839 wppa-utils.php:850 wppa-utils.php:861
#, php-format
msgid "Photo %s has been marked as inappropriate by %s different visitors."
msgstr ""

#: wppa-utils.php:851
msgid "The status has been changed to 'pending'."
msgstr ""

#: wppa-utils.php:862
msgid "It has been deleted."
msgstr ""

#: wppa-utils.php:957
msgid "Your photo has a new approved comment"
msgstr ""

#: wppa-utils.php:960
msgid "From:"
msgstr ""

#: wppa-utils.php:963
msgid "Comment:"
msgstr ""

#: wppa-utils.php:968
msgid "Approved comment on photo"
msgstr ""

#: wppa-utils.php:1015
#, php-format
msgid "The visitors email address is: <a href=\"mailto:%s\">%s</a>"
msgstr ""

#: wppa-utils.php:1020
#, php-format
msgid "The visitor says his email address is: <a href=\"mailto:%s\">%s</a>"
msgstr ""

#: wppa-utils.php:1028
#, php-format
msgid ""
"This message is automatically generated at %s. It is useless to respond to "
"it."
msgstr ""

#: wppa-utils.php:1116
#, php-format
msgid "Time out after processing %s items."
msgstr ""

#: wppa-utils.php:1119 wppa-utils.php:1123
#, php-format
msgid "Time out after processing %s items. Please restart this operation"
msgstr ""

#: wppa-utils.php:1321
msgid "Could not delete photo"
msgstr ""

#: wppa-utils.php:1323 wppa-utils.php:1330
msgid "Photo is still in use in post/page"
msgstr ""

#: wppa-utils.php:2107
msgid "Photo w#id"
msgstr ""

#: wppa-utils.php:2279
msgid "There are no ratings between"
msgstr ""

#: wppa-utils.php:3706
#, php-format
msgid "You and %d other person like this"
msgid_plural "You and %d other people like this"
msgstr[0] ""

#: wppa-utils.php:3709
msgid "You are the first one who likes this"
msgstr ""

#: wppa-utils.php:3712
msgid "Click again if you do no longer like this"
msgstr ""

#: wppa-utils.php:3716
#, php-format
msgid "%d person likes this"
msgid_plural "%d people like this"
msgstr[0] ""

#: wppa-utils.php:3719
msgid "Be the first one to like this"
msgstr ""

#: wppa-utils.php:3725
#, php-format
msgid "%d like"
msgid_plural "%d likes"
msgstr[0] ""

#: wppa-video.php:195
msgid ""
"There is no filetype available for your browser, or your browser does not "
"support html5 video"
msgstr "有您的浏览器没有可用的文件类型，或者你的浏览器不支持HTML5视频"

#: wppa-watermark.php:600
msgid "--- text: name ---"
msgstr ""

#: wppa-watermark.php:602
msgid "--- text: filename ---"
msgstr ""

#: wppa-watermark.php:604
msgid "--- text: description ---"
msgstr ""

#: wppa-watermark.php:606
msgid "--- text: pre-defined ---"
msgstr ""

#: wppa-widget-functions.php:421
#, php-format
msgid "Please enter a number >= %1s and <= %2s"
msgstr ""

#. Plugin Name of the plugin/theme
msgid "WP Photo Album Plus"
msgstr "WP Photo Album Plus"

#. Plugin URI of the plugin/theme
msgid "http://wordpress.org/extend/plugins/wp-photo-album-plus/"
msgstr "http://wordpress.org/extend/plugins/wp-photo-album-plus/"

#. Description of the plugin/theme
msgid ""
"Easily manage and display your photo albums and slideshows within your "
"WordPress site."
msgstr ""
"Easily manage and display your photo albums and slideshows within your "
"WordPress site."

#. Author of the plugin/theme
msgid "J.N. Breetvelt a.k.a. OpaJaap"
msgstr "J.N. Breetvelt a.k.a. OpaJaap"

#. Author URI of the plugin/theme
msgid "http://wppa.opajaap.nl/"
msgstr "http://wppa.opajaap.nl/"

#~ msgid "photo"
#~ msgstr "相片"
