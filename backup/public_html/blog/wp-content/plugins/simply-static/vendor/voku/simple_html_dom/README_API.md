[//]: # (AUTO-GENERATED BY "PHP README Helper": base file -> docs/api.md)
# :scroll: Simple Html Dom Parser for PHP 

### DomParser API

<p id="voku-php-readme-class-methods"></p><table><tr><td><a href="#findstring-selector-intnull-idx-mixed">find</a>
</td><td><a href="#findmultistring-selector-mixed">findMulti</a>
</td><td><a href="#findmultiorfalsestring-selector-mixed">findMultiOrFalse</a>
</td><td><a href="#findonestring-selector-static">findOne</a>
</td></tr><tr><td><a href="#findoneorfalsestring-selector-mixed">findOneOrFalse</a>
</td><td><a href="#fixhtmloutputstring-content-bool-multidecodenewhtmlentity-string">fixHtmlOutput</a>
</td><td><a href="#getdocument-domdocument">getDocument</a>
</td><td><a href="#getelementbyclassstring-class-mixed">getElementByClass</a>
</td></tr><tr><td><a href="#getelementbyidstring-id-mixed">getElementById</a>
</td><td><a href="#getelementbytagnamestring-name-mixed">getElementByTagName</a>
</td><td><a href="#getelementsbyidstring-id-intnull-idx-mixed">getElementsById</a>
</td><td><a href="#getelementsbytagnamestring-name-intnull-idx-mixed">getElementsByTagName</a>
</td></tr><tr><td><a href="#htmlbool-multidecodenewhtmlentity-string">html</a>
</td><td><a href="#innerhtmlbool-multidecodenewhtmlentity-string">innerHtml</a>
</td><td><a href="#innerxmlbool-multidecodenewhtmlentity-string">innerXml</a>
</td><td><a href="#loadhtmlstring-html-intnull-libxmlextraoptions-domparserinterface">loadHtml</a>
</td></tr><tr><td><a href="#loadhtmlfilestring-filepath-intnull-libxmlextraoptions-domparserinterface">loadHtmlFile</a>
</td><td><a href="#savestring-filepath-string">save</a>
</td><td><a href="#set_callbackcallable-functionname-mixed">set_callback</a>
</td><td><a href="#textbool-multidecodenewhtmlentity-string">text</a>
</td></tr><tr><td><a href="#xmlbool-multidecodenewhtmlentity-bool-htmltoxml-bool-removexmlheader-int-options-string">xml</a>
</td></tr></table>

### SimpleHtmlDomNode (group of dom elements) API

<p id="voku-php-readme-class-methods"></p><table><tr><td><a href="#count-int">count</a>
</td><td><a href="#findstring-selector-int-idx-simplehtmldomnodesimplehtmldomnodenull">find</a>
</td><td><a href="#findmultistring-selector-simplehtmldominterfacesimplehtmldomnodeinterfacesimplehtmldominterface">findMulti</a>
</td><td><a href="#findmultiorfalsestring-selector-falsesimplehtmldominterfacesimplehtmldomnodeinterfacesimplehtmldominterface">findMultiOrFalse</a>
</td></tr><tr><td><a href="#findonestring-selector-simplehtmldomnodenull">findOne</a>
</td><td><a href="#findoneorfalsestring-selector-falsesimplehtmldomnode">findOneOrFalse</a>
</td><td><a href="#innerhtml-string">innerHtml</a>
</td><td><a href="#innertext-string">innertext</a>
</td></tr><tr><td><a href="#outertext-string">outertext</a>
</td><td><a href="#text-string">text</a>
</td></tr></table>

### SimpleHtmlDom (single dom element) API

<p id="voku-php-readme-class-methods"></p><table><tr><td><a href="#childnodesint-idx-simplehtmldominterfacesimplehtmldominterfacesimplehtmldomnodeinterfacenull">childNodes</a>
</td><td><a href="#delete-mixed">delete</a>
</td><td><a href="#findstring-selector-intnull-idx-simplehtmldominterfacesimplehtmldominterfacesimplehtmldomnodeinterfacesimplehtmldominterface">find</a>
</td><td><a href="#findmultistring-selector-simplehtmldominterfacesimplehtmldomnodeinterfacesimplehtmldominterface">findMulti</a>
</td></tr><tr><td><a href="#findmultiorfalsestring-selector-falsesimplehtmldominterfacesimplehtmldomnodeinterfacesimplehtmldominterface">findMultiOrFalse</a>
</td><td><a href="#findonestring-selector-simplehtmldominterface">findOne</a>
</td><td><a href="#findoneorfalsestring-selector-falsesimplehtmldominterface">findOneOrFalse</a>
</td><td><a href="#firstchild-simplehtmldominterfacenull">firstChild</a>
</td></tr><tr><td><a href="#getallattributes-stringnull">getAllAttributes</a>
</td><td><a href="#getattributestring-name-string">getAttribute</a>
</td><td><a href="#getelementbyclassstring-class-simplehtmldominterfacesimplehtmldomnodeinterfacesimplehtmldominterface">getElementByClass</a>
</td><td><a href="#getelementbyidstring-id-simplehtmldominterface">getElementById</a>
</td></tr><tr><td><a href="#getelementbytagnamestring-name-simplehtmldominterface">getElementByTagName</a>
</td><td><a href="#getelementsbyidstring-id-intnull-idx-simplehtmldominterfacesimplehtmldominterfacesimplehtmldomnodeinterfacesimplehtmldominterface">getElementsById</a>
</td><td><a href="#getelementsbytagnamestring-name-intnull-idx-simplehtmldominterfacesimplehtmldominterfacesimplehtmldomnodeinterfacesimplehtmldominterface">getElementsByTagName</a>
</td><td><a href="#gethtmldomparser-htmldomparser">getHtmlDomParser</a>
</td></tr><tr><td><a href="#getiterator-simplehtmldomnodeinterfacesimplehtmldominterface">getIterator</a>
</td><td><a href="#getnode-domnode">getNode</a>
</td><td><a href="#gettag-string">getTag</a>
</td><td><a href="#hasattributestring-name-bool">hasAttribute</a>
</td></tr><tr><td><a href="#htmlbool-multidecodenewhtmlentity-string">html</a>
</td><td><a href="#innerhtmlbool-multidecodenewhtmlentity-string">innerHtml</a>
</td><td><a href="#innerxmlbool-multidecodenewhtmlentity-string">innerXml</a>
</td><td><a href="#isremoved-bool">isRemoved</a>
</td></tr><tr><td><a href="#lastchild-simplehtmldominterfacenull">lastChild</a>
</td><td><a href="#nextnonwhitespacesibling-simplehtmldominterfacenull">nextNonWhitespaceSibling</a>
</td><td><a href="#nextsibling-simplehtmldominterfacenull">nextSibling</a>
</td><td><a href="#parentnode-simplehtmldominterface">parentNode</a>
</td></tr><tr><td><a href="#previousnonwhitespacesibling-simplehtmldominterfacenull">previousNonWhitespaceSibling</a>
</td><td><a href="#previoussibling-simplehtmldominterfacenull">previousSibling</a>
</td><td><a href="#removeattributestring-name-simplehtmldominterface">removeAttribute</a>
</td><td><a href="#removeattributes-simplehtmldominterface">removeAttributes</a>
</td></tr><tr><td><a href="#setattributestring-name-stringnull-value-bool-strictemptyvaluecheck-simplehtmldominterface">setAttribute</a>
</td><td><a href="#text-string">text</a>
</td><td><a href="#valstringstringnull-value-stringstringnull">val</a>
</td></tr></table>

---

## find(string $selector, int|null $idx): mixed
<a href="#voku-php-readme-class-methods">↑</a>
Find list of nodes with a CSS selector.

**Parameters:**
- `string $selector`
- `int|null $idx`

**Return:**
- `mixed`

--------

## findMulti(string $selector): mixed
<a href="#voku-php-readme-class-methods">↑</a>
Find nodes with a CSS selector.

**Parameters:**
- `string $selector`

**Return:**
- `mixed`

--------

## findMultiOrFalse(string $selector): mixed
<a href="#voku-php-readme-class-methods">↑</a>
Find nodes with a CSS selector or false, if no element is found.

**Parameters:**
- `string $selector`

**Return:**
- `mixed`

--------

## findOne(string $selector): static
<a href="#voku-php-readme-class-methods">↑</a>
Find one node with a CSS selector.

**Parameters:**
- `string $selector`

**Return:**
- `static`

--------

## findOneOrFalse(string $selector): mixed
<a href="#voku-php-readme-class-methods">↑</a>
Find one node with a CSS selector or false, if no element is found.

**Parameters:**
- `string $selector`

**Return:**
- `mixed`

--------

## fixHtmlOutput(string $content, bool $multiDecodeNewHtmlEntity): string
<a href="#voku-php-readme-class-methods">↑</a>


**Parameters:**
- `string $content`
- `bool $multiDecodeNewHtmlEntity`

**Return:**
- `string`

--------

## getDocument(): DOMDocument
<a href="#voku-php-readme-class-methods">↑</a>


**Parameters:**
__nothing__

**Return:**
- `\DOMDocument`

--------

## getElementByClass(string $class): mixed
<a href="#voku-php-readme-class-methods">↑</a>
Return elements by ".class".

**Parameters:**
- `string $class`

**Return:**
- `mixed`

--------

## getElementById(string $id): mixed
<a href="#voku-php-readme-class-methods">↑</a>
Return element by #id.

**Parameters:**
- `string $id`

**Return:**
- `mixed`

--------

## getElementByTagName(string $name): mixed
<a href="#voku-php-readme-class-methods">↑</a>
Return element by tag name.

**Parameters:**
- `string $name`

**Return:**
- `mixed`

--------

## getElementsById(string $id, int|null $idx): mixed
<a href="#voku-php-readme-class-methods">↑</a>
Returns elements by "#id".

**Parameters:**
- `string $id`
- `int|null $idx`

**Return:**
- `mixed`

--------

## getElementsByTagName(string $name, int|null $idx): mixed
<a href="#voku-php-readme-class-methods">↑</a>
Returns elements by tag name.

**Parameters:**
- `string $name`
- `int|null $idx`

**Return:**
- `mixed`

--------

## html(bool $multiDecodeNewHtmlEntity): string
<a href="#voku-php-readme-class-methods">↑</a>
Get dom node's outer html.

**Parameters:**
- `bool $multiDecodeNewHtmlEntity`

**Return:**
- `string`

--------

## innerHtml(bool $multiDecodeNewHtmlEntity): string
<a href="#voku-php-readme-class-methods">↑</a>
Get dom node's inner html.

**Parameters:**
- `bool $multiDecodeNewHtmlEntity`

**Return:**
- `string`

--------

## innerXml(bool $multiDecodeNewHtmlEntity): string
<a href="#voku-php-readme-class-methods">↑</a>
Get dom node's inner xml.

**Parameters:**
- `bool $multiDecodeNewHtmlEntity`

**Return:**
- `string`

--------

## loadHtml(string $html, int|null $libXMLExtraOptions): DomParserInterface
<a href="#voku-php-readme-class-methods">↑</a>
Load HTML from string.

**Parameters:**
- `string $html`
- `int|null $libXMLExtraOptions`

**Return:**
- `\DomParserInterface`

--------

## loadHtmlFile(string $filePath, int|null $libXMLExtraOptions): DomParserInterface
<a href="#voku-php-readme-class-methods">↑</a>
Load HTML from file.

**Parameters:**
- `string $filePath`
- `int|null $libXMLExtraOptions`

**Return:**
- `\DomParserInterface`

--------

## save(string $filepath): string
<a href="#voku-php-readme-class-methods">↑</a>
Save the html-dom as string.

**Parameters:**
- `string $filepath`

**Return:**
- `string`

--------

## set_callback(callable $functionName): mixed
<a href="#voku-php-readme-class-methods">↑</a>


**Parameters:**
- `callable $functionName`

**Return:**
- `mixed`

--------

## text(bool $multiDecodeNewHtmlEntity): string
<a href="#voku-php-readme-class-methods">↑</a>
Get dom node's plain text.

**Parameters:**
- `bool $multiDecodeNewHtmlEntity`

**Return:**
- `string`

--------

## xml(bool $multiDecodeNewHtmlEntity, bool $htmlToXml, bool $removeXmlHeader, int $options): string
<a href="#voku-php-readme-class-methods">↑</a>
Get the HTML as XML or plain XML if needed.

**Parameters:**
- `bool $multiDecodeNewHtmlEntity`
- `bool $htmlToXml`
- `bool $removeXmlHeader`
- `int $options`

**Return:**
- `string`

--------


## count(): int
<a href="#voku-php-readme-class-methods">↑</a>
Get the number of items in this dom node.

**Parameters:**
__nothing__

**Return:**
- `int`

--------

## find(string $selector, int $idx): SimpleHtmlDomNode|\SimpleHtmlDomNode[]|null
<a href="#voku-php-readme-class-methods">↑</a>
Find list of nodes with a CSS selector.

**Parameters:**
- `string $selector`
- `int $idx`

**Return:**
- `\SimpleHtmlDomNode|\SimpleHtmlDomNode[]|null`

--------

## findMulti(string $selector): SimpleHtmlDomInterface[]|\SimpleHtmlDomNodeInterface<\SimpleHtmlDomInterface>
<a href="#voku-php-readme-class-methods">↑</a>
Find nodes with a CSS selector.

**Parameters:**
- `string $selector`

**Return:**
- `\SimpleHtmlDomInterface[]|\SimpleHtmlDomNodeInterface<\SimpleHtmlDomInterface>`

--------

## findMultiOrFalse(string $selector): false|\SimpleHtmlDomInterface[]|\SimpleHtmlDomNodeInterface<\SimpleHtmlDomInterface>
<a href="#voku-php-readme-class-methods">↑</a>
Find nodes with a CSS selector or false, if no element is found.

**Parameters:**
- `string $selector`

**Return:**
- `false|\SimpleHtmlDomInterface[]|\SimpleHtmlDomNodeInterface<\SimpleHtmlDomInterface>`

--------

## findOne(string $selector): SimpleHtmlDomNode|null
<a href="#voku-php-readme-class-methods">↑</a>
Find one node with a CSS selector.

**Parameters:**
- `string $selector`

**Return:**
- `\SimpleHtmlDomNode|null`

--------

## findOneOrFalse(string $selector): false|\SimpleHtmlDomNode
<a href="#voku-php-readme-class-methods">↑</a>
Find one node with a CSS selector or false, if no element is found.

**Parameters:**
- `string $selector`

**Return:**
- `false|\SimpleHtmlDomNode`

--------

## innerHtml(): string[]
<a href="#voku-php-readme-class-methods">↑</a>
Get html of elements.

**Parameters:**
__nothing__

**Return:**
- `string[]`

--------

## innertext(): string[]
<a href="#voku-php-readme-class-methods">↑</a>
alias for "$this->innerHtml()" (added for compatibly-reasons with v1.x)

**Parameters:**
__nothing__

**Return:**
- `string[]`

--------

## outertext(): string[]
<a href="#voku-php-readme-class-methods">↑</a>
alias for "$this->innerHtml()" (added for compatibly-reasons with v1.x)

**Parameters:**
__nothing__

**Return:**
- `string[]`

--------

## text(): string[]
<a href="#voku-php-readme-class-methods">↑</a>
Get plain text.

**Parameters:**
__nothing__

**Return:**
- `string[]`

--------


## childNodes(int $idx): SimpleHtmlDomInterface|\SimpleHtmlDomInterface[]|\SimpleHtmlDomNodeInterface|null
<a href="#voku-php-readme-class-methods">↑</a>
Returns children of node.

**Parameters:**
- `int $idx`

**Return:**
- `\SimpleHtmlDomInterface|\SimpleHtmlDomInterface[]|\SimpleHtmlDomNodeInterface|null`

--------

## delete(): mixed
<a href="#voku-php-readme-class-methods">↑</a>
Delete

**Parameters:**
__nothing__

**Return:**
- `mixed`

--------

## find(string $selector, int|null $idx): SimpleHtmlDomInterface|\SimpleHtmlDomInterface[]|\SimpleHtmlDomNodeInterface<\SimpleHtmlDomInterface>
<a href="#voku-php-readme-class-methods">↑</a>
Find list of nodes with a CSS selector.

**Parameters:**
- `string $selector`
- `int|null $idx`

**Return:**
- `\SimpleHtmlDomInterface|\SimpleHtmlDomInterface[]|\SimpleHtmlDomNodeInterface<\SimpleHtmlDomInterface>`

--------

## findMulti(string $selector): SimpleHtmlDomInterface[]|\SimpleHtmlDomNodeInterface<\SimpleHtmlDomInterface>
<a href="#voku-php-readme-class-methods">↑</a>
Find nodes with a CSS selector.

**Parameters:**
- `string $selector`

**Return:**
- `\SimpleHtmlDomInterface[]|\SimpleHtmlDomNodeInterface<\SimpleHtmlDomInterface>`

--------

## findMultiOrFalse(string $selector): false|\SimpleHtmlDomInterface[]|\SimpleHtmlDomNodeInterface<\SimpleHtmlDomInterface>
<a href="#voku-php-readme-class-methods">↑</a>
Find nodes with a CSS selector or false, if no element is found.

**Parameters:**
- `string $selector`

**Return:**
- `false|\SimpleHtmlDomInterface[]|\SimpleHtmlDomNodeInterface<\SimpleHtmlDomInterface>`

--------

## findOne(string $selector): SimpleHtmlDomInterface
<a href="#voku-php-readme-class-methods">↑</a>
Find one node with a CSS selector.

**Parameters:**
- `string $selector`

**Return:**
- `\SimpleHtmlDomInterface`

--------

## findOneOrFalse(string $selector): false|\SimpleHtmlDomInterface
<a href="#voku-php-readme-class-methods">↑</a>
Find one node with a CSS selector or false, if no element is found.

**Parameters:**
- `string $selector`

**Return:**
- `false|\SimpleHtmlDomInterface`

--------

## firstChild(): SimpleHtmlDomInterface|null
<a href="#voku-php-readme-class-methods">↑</a>
Returns the first child of node.

**Parameters:**
__nothing__

**Return:**
- `\SimpleHtmlDomInterface|null`

--------

## getAllAttributes(): string[]|null
<a href="#voku-php-readme-class-methods">↑</a>
Returns an array of attributes.

**Parameters:**
__nothing__

**Return:**
- `string[]|null`

--------

## getAttribute(string $name): string
<a href="#voku-php-readme-class-methods">↑</a>
Return attribute value.

**Parameters:**
- `string $name`

**Return:**
- `string`

--------

## getElementByClass(string $class): SimpleHtmlDomInterface[]|\SimpleHtmlDomNodeInterface<\SimpleHtmlDomInterface>
<a href="#voku-php-readme-class-methods">↑</a>
Return elements by ".class".

**Parameters:**
- `string $class`

**Return:**
- `\SimpleHtmlDomInterface[]|\SimpleHtmlDomNodeInterface<\SimpleHtmlDomInterface>`

--------

## getElementById(string $id): SimpleHtmlDomInterface
<a href="#voku-php-readme-class-methods">↑</a>
Return element by "#id".

**Parameters:**
- `string $id`

**Return:**
- `\SimpleHtmlDomInterface`

--------

## getElementByTagName(string $name): SimpleHtmlDomInterface
<a href="#voku-php-readme-class-methods">↑</a>
Return element by tag name.

**Parameters:**
- `string $name`

**Return:**
- `\SimpleHtmlDomInterface`

--------

## getElementsById(string $id, int|null $idx): SimpleHtmlDomInterface|\SimpleHtmlDomInterface[]|\SimpleHtmlDomNodeInterface<\SimpleHtmlDomInterface>
<a href="#voku-php-readme-class-methods">↑</a>
Returns elements by "#id".

**Parameters:**
- `string $id`
- `int|null $idx`

**Return:**
- `\SimpleHtmlDomInterface|\SimpleHtmlDomInterface[]|\SimpleHtmlDomNodeInterface<\SimpleHtmlDomInterface>`

--------

## getElementsByTagName(string $name, int|null $idx): SimpleHtmlDomInterface|\SimpleHtmlDomInterface[]|\SimpleHtmlDomNodeInterface<\SimpleHtmlDomInterface>
<a href="#voku-php-readme-class-methods">↑</a>
Returns elements by tag name.

**Parameters:**
- `string $name`
- `int|null $idx`

**Return:**
- `\SimpleHtmlDomInterface|\SimpleHtmlDomInterface[]|\SimpleHtmlDomNodeInterface<\SimpleHtmlDomInterface>`

--------

## getHtmlDomParser(): HtmlDomParser
<a href="#voku-php-readme-class-methods">↑</a>
Create a new "HtmlDomParser"-object from the current context.

**Parameters:**
__nothing__

**Return:**
- `\HtmlDomParser`

--------

## getIterator(): SimpleHtmlDomNodeInterface<\SimpleHtmlDomInterface>
<a href="#voku-php-readme-class-methods">↑</a>
Retrieve an external iterator.

**Parameters:**
__nothing__

**Return:**
- `\SimpleHtmlDomNodeInterface<\SimpleHtmlDomInterface> <p>
   An instance of an object implementing <b>Iterator</b> or
   <b>Traversable</b>
</p>`

--------

## getNode(): DOMNode
<a href="#voku-php-readme-class-methods">↑</a>


**Parameters:**
__nothing__

**Return:**
- `\DOMNode`

--------

## getTag(): string
<a href="#voku-php-readme-class-methods">↑</a>
Return the tag of node

**Parameters:**
__nothing__

**Return:**
- `string`

--------

## hasAttribute(string $name): bool
<a href="#voku-php-readme-class-methods">↑</a>
Determine if an attribute exists on the element.

**Parameters:**
- `string $name`

**Return:**
- `bool`

--------

## html(bool $multiDecodeNewHtmlEntity): string
<a href="#voku-php-readme-class-methods">↑</a>
Get dom node's outer html.

**Parameters:**
- `bool $multiDecodeNewHtmlEntity`

**Return:**
- `string`

--------

## innerHtml(bool $multiDecodeNewHtmlEntity): string
<a href="#voku-php-readme-class-methods">↑</a>
Get dom node's inner html.

**Parameters:**
- `bool $multiDecodeNewHtmlEntity`

**Return:**
- `string`

--------

## innerXml(bool $multiDecodeNewHtmlEntity): string
<a href="#voku-php-readme-class-methods">↑</a>
Get dom node's inner html.

**Parameters:**
- `bool $multiDecodeNewHtmlEntity`

**Return:**
- `string`

--------

## isRemoved(): bool
<a href="#voku-php-readme-class-methods">↑</a>
Nodes can get partially destroyed in which they're still an
actual DOM node (such as \DOMElement) but almost their entire
body is gone, including the `nodeType` attribute.

**Parameters:**
__nothing__

**Return:**
- `bool true if node has been destroyed`

--------

## lastChild(): SimpleHtmlDomInterface|null
<a href="#voku-php-readme-class-methods">↑</a>
Returns the last child of node.

**Parameters:**
__nothing__

**Return:**
- `\SimpleHtmlDomInterface|null`

--------

## nextNonWhitespaceSibling(): SimpleHtmlDomInterface|null
<a href="#voku-php-readme-class-methods">↑</a>
Returns the next sibling of node, and it will ignore whitespace elements.

**Parameters:**
__nothing__

**Return:**
- `\SimpleHtmlDomInterface|null`

--------

## nextSibling(): SimpleHtmlDomInterface|null
<a href="#voku-php-readme-class-methods">↑</a>
Returns the next sibling of node.

**Parameters:**
__nothing__

**Return:**
- `\SimpleHtmlDomInterface|null`

--------

## parentNode(): SimpleHtmlDomInterface
<a href="#voku-php-readme-class-methods">↑</a>
Returns the parent of node.

**Parameters:**
__nothing__

**Return:**
- `\SimpleHtmlDomInterface`

--------

## previousNonWhitespaceSibling(): SimpleHtmlDomInterface|null
<a href="#voku-php-readme-class-methods">↑</a>
Returns the previous sibling of node, and it will ignore whitespace elements.

**Parameters:**
__nothing__

**Return:**
- `\SimpleHtmlDomInterface|null`

--------

## previousSibling(): SimpleHtmlDomInterface|null
<a href="#voku-php-readme-class-methods">↑</a>
Returns the previous sibling of node.

**Parameters:**
__nothing__

**Return:**
- `\SimpleHtmlDomInterface|null`

--------

## removeAttribute(string $name): SimpleHtmlDomInterface
<a href="#voku-php-readme-class-methods">↑</a>
Remove attribute.

**Parameters:**
- `string $name <p>The name of the html-attribute.</p>`

**Return:**
- `\SimpleHtmlDomInterface`

--------

## removeAttributes(): SimpleHtmlDomInterface
<a href="#voku-php-readme-class-methods">↑</a>
Remove all attributes

**Parameters:**
__nothing__

**Return:**
- `\SimpleHtmlDomInterface`

--------

## setAttribute(string $name, string|null $value, bool $strictEmptyValueCheck): SimpleHtmlDomInterface
<a href="#voku-php-readme-class-methods">↑</a>
Set attribute value.

**Parameters:**
- `string $name <p>The name of the html-attribute.</p>`
- `string|null $value <p>Set to NULL or empty string, to remove the attribute.</p>`
- `bool $strictEmptyValueCheck </p>
$value must be NULL, to remove the attribute,
so that you can set an empty string as attribute-value e.g. autofocus=""
</p>`

**Return:**
- `\SimpleHtmlDomInterface`

--------

## text(): string
<a href="#voku-php-readme-class-methods">↑</a>
Get dom node's plain text.

**Parameters:**
__nothing__

**Return:**
- `string`

--------

## val(string|string[]|null $value): string|string[]|null
<a href="#voku-php-readme-class-methods">↑</a>


**Parameters:**
- `string|string[]|null $value <p>
null === get the current input value
text === set a new input value
</p>`

**Return:**
- `string|string[]|null`

--------

