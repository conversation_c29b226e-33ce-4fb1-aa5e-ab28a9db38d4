<?php

static $data = array (
  ' ' => ' ',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '	' => '	',
  '
' => '
',
  '' => '',
  '' => '',
  '
' => '
',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  ' ' => ' ',
  '!' => '!',
  '"' => '"',
  '#' => '#',
  '$' => '$',
  '%' => '%',
  '&' => '&',
  '\'' => '\'',
  '(' => '(',
  ')' => ')',
  '*' => '*',
  '+' => '+',
  ',' => ',',
  '-' => '-',
  '.' => '.',
  '/' => '/',
  0 => '0',
  1 => '1',
  2 => '2',
  3 => '3',
  4 => '4',
  5 => '5',
  6 => '6',
  7 => '7',
  8 => '8',
  9 => '9',
  ':' => ':',
  ';' => ';',
  '<' => '<',
  '=' => '=',
  '>' => '>',
  '?' => '?',
  '@' => '@',
  'A' => 'A',
  'B' => 'B',
  'C' => 'C',
  'D' => 'D',
  'E' => 'E',
  'F' => 'F',
  'G' => 'G',
  'H' => 'H',
  'I' => 'I',
  'J' => 'J',
  'K' => 'K',
  'L' => 'L',
  'M' => 'M',
  'N' => 'N',
  'O' => 'O',
  'P' => 'P',
  'Q' => 'Q',
  'R' => 'R',
  'S' => 'S',
  'T' => 'T',
  'U' => 'U',
  'V' => 'V',
  'W' => 'W',
  'X' => 'X',
  'Y' => 'Y',
  'Z' => 'Z',
  '[' => '[',
  '\\' => '\\',
  ']' => ']',
  '^' => '^',
  '_' => '_',
  '`' => '`',
  'a' => 'a',
  'b' => 'b',
  'c' => 'c',
  'd' => 'd',
  'e' => 'e',
  'f' => 'f',
  'g' => 'g',
  'h' => 'h',
  'i' => 'i',
  'j' => 'j',
  'k' => 'k',
  'l' => 'l',
  'm' => 'm',
  'n' => 'n',
  'o' => 'o',
  'p' => 'p',
  'q' => 'q',
  'r' => 'r',
  's' => 's',
  't' => 't',
  'u' => 'u',
  'v' => 'v',
  'w' => 'w',
  'x' => 'x',
  'y' => 'y',
  'z' => 'z',
  '{' => '{',
  '|' => '|',
  '}' => '}',
  '~' => '~',
  '' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => ' ',
  '�' => 'Ą',
  '�' => 'ĸ',
  '�' => 'Ŗ',
  '�' => '¤',
  '�' => 'Ĩ',
  '�' => 'Ļ',
  '�' => '§',
  '�' => '¨',
  '�' => 'Š',
  '�' => 'Ē',
  '�' => 'Ģ',
  '�' => 'Ŧ',
  '�' => '­',
  '�' => 'Ž',
  '�' => '¯',
  '�' => '°',
  '�' => 'ą',
  '�' => '˛',
  '�' => 'ŗ',
  '�' => '´',
  '�' => 'ĩ',
  '�' => 'ļ',
  '�' => 'ˇ',
  '�' => '¸',
  '�' => 'š',
  '�' => 'ē',
  '�' => 'ģ',
  '�' => 'ŧ',
  '�' => 'Ŋ',
  '�' => 'ž',
  '�' => 'ŋ',
  '�' => 'Ā',
  '�' => 'Á',
  '�' => 'Â',
  '�' => 'Ã',
  '�' => 'Ä',
  '�' => 'Å',
  '�' => 'Æ',
  '�' => 'Į',
  '�' => 'Č',
  '�' => 'É',
  '�' => 'Ę',
  '�' => 'Ë',
  '�' => 'Ė',
  '�' => 'Í',
  '�' => 'Î',
  '�' => 'Ī',
  '�' => 'Đ',
  '�' => 'Ņ',
  '�' => 'Ō',
  '�' => 'Ķ',
  '�' => 'Ô',
  '�' => 'Õ',
  '�' => 'Ö',
  '�' => '×',
  '�' => 'Ø',
  '�' => 'Ų',
  '�' => 'Ú',
  '�' => 'Û',
  '�' => 'Ü',
  '�' => 'Ũ',
  '�' => 'Ū',
  '�' => 'ß',
  '�' => 'ā',
  '�' => 'á',
  '�' => 'â',
  '�' => 'ã',
  '�' => 'ä',
  '�' => 'å',
  '�' => 'æ',
  '�' => 'į',
  '�' => 'č',
  '�' => 'é',
  '�' => 'ę',
  '�' => 'ë',
  '�' => 'ė',
  '�' => 'í',
  '�' => 'î',
  '�' => 'ī',
  '�' => 'đ',
  '�' => 'ņ',
  '�' => 'ō',
  '�' => 'ķ',
  '�' => 'ô',
  '�' => 'õ',
  '�' => 'ö',
  '�' => '÷',
  '�' => 'ø',
  '�' => 'ų',
  '�' => 'ú',
  '�' => 'û',
  '�' => 'ü',
  '�' => 'ũ',
  '�' => 'ū',
  '�' => '˙',
);

$result =& $data;
unset($data);

return $result;
