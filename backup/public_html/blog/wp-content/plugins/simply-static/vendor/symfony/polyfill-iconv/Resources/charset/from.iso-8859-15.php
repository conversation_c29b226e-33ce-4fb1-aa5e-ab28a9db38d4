<?php

static $data = array (
  ' ' => ' ',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '	' => '	',
  '
' => '
',
  '' => '',
  '' => '',
  '
' => '
',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  '' => '',
  ' ' => ' ',
  '!' => '!',
  '"' => '"',
  '#' => '#',
  '$' => '$',
  '%' => '%',
  '&' => '&',
  '\'' => '\'',
  '(' => '(',
  ')' => ')',
  '*' => '*',
  '+' => '+',
  ',' => ',',
  '-' => '-',
  '.' => '.',
  '/' => '/',
  0 => '0',
  1 => '1',
  2 => '2',
  3 => '3',
  4 => '4',
  5 => '5',
  6 => '6',
  7 => '7',
  8 => '8',
  9 => '9',
  ':' => ':',
  ';' => ';',
  '<' => '<',
  '=' => '=',
  '>' => '>',
  '?' => '?',
  '@' => '@',
  'A' => 'A',
  'B' => 'B',
  'C' => 'C',
  'D' => 'D',
  'E' => 'E',
  'F' => 'F',
  'G' => 'G',
  'H' => 'H',
  'I' => 'I',
  'J' => 'J',
  'K' => 'K',
  'L' => 'L',
  'M' => 'M',
  'N' => 'N',
  'O' => 'O',
  'P' => 'P',
  'Q' => 'Q',
  'R' => 'R',
  'S' => 'S',
  'T' => 'T',
  'U' => 'U',
  'V' => 'V',
  'W' => 'W',
  'X' => 'X',
  'Y' => 'Y',
  'Z' => 'Z',
  '[' => '[',
  '\\' => '\\',
  ']' => ']',
  '^' => '^',
  '_' => '_',
  '`' => '`',
  'a' => 'a',
  'b' => 'b',
  'c' => 'c',
  'd' => 'd',
  'e' => 'e',
  'f' => 'f',
  'g' => 'g',
  'h' => 'h',
  'i' => 'i',
  'j' => 'j',
  'k' => 'k',
  'l' => 'l',
  'm' => 'm',
  'n' => 'n',
  'o' => 'o',
  'p' => 'p',
  'q' => 'q',
  'r' => 'r',
  's' => 's',
  't' => 't',
  'u' => 'u',
  'v' => 'v',
  'w' => 'w',
  'x' => 'x',
  'y' => 'y',
  'z' => 'z',
  '{' => '{',
  '|' => '|',
  '}' => '}',
  '~' => '~',
  '' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => '',
  '�' => ' ',
  '�' => '¡',
  '�' => '¢',
  '�' => '£',
  '�' => '€',
  '�' => '¥',
  '�' => 'Š',
  '�' => '§',
  '�' => 'š',
  '�' => '©',
  '�' => 'ª',
  '�' => '«',
  '�' => '¬',
  '�' => '­',
  '�' => '®',
  '�' => '¯',
  '�' => '°',
  '�' => '±',
  '�' => '²',
  '�' => '³',
  '�' => 'Ž',
  '�' => 'µ',
  '�' => '¶',
  '�' => '·',
  '�' => 'ž',
  '�' => '¹',
  '�' => 'º',
  '�' => '»',
  '�' => 'Œ',
  '�' => 'œ',
  '�' => 'Ÿ',
  '�' => '¿',
  '�' => 'À',
  '�' => 'Á',
  '�' => 'Â',
  '�' => 'Ã',
  '�' => 'Ä',
  '�' => 'Å',
  '�' => 'Æ',
  '�' => 'Ç',
  '�' => 'È',
  '�' => 'É',
  '�' => 'Ê',
  '�' => 'Ë',
  '�' => 'Ì',
  '�' => 'Í',
  '�' => 'Î',
  '�' => 'Ï',
  '�' => 'Ð',
  '�' => 'Ñ',
  '�' => 'Ò',
  '�' => 'Ó',
  '�' => 'Ô',
  '�' => 'Õ',
  '�' => 'Ö',
  '�' => '×',
  '�' => 'Ø',
  '�' => 'Ù',
  '�' => 'Ú',
  '�' => 'Û',
  '�' => 'Ü',
  '�' => 'Ý',
  '�' => 'Þ',
  '�' => 'ß',
  '�' => 'à',
  '�' => 'á',
  '�' => 'â',
  '�' => 'ã',
  '�' => 'ä',
  '�' => 'å',
  '�' => 'æ',
  '�' => 'ç',
  '�' => 'è',
  '�' => 'é',
  '�' => 'ê',
  '�' => 'ë',
  '�' => 'ì',
  '�' => 'í',
  '�' => 'î',
  '�' => 'ï',
  '�' => 'ð',
  '�' => 'ñ',
  '�' => 'ò',
  '�' => 'ó',
  '�' => 'ô',
  '�' => 'õ',
  '�' => 'ö',
  '�' => '÷',
  '�' => 'ø',
  '�' => 'ù',
  '�' => 'ú',
  '�' => 'û',
  '�' => 'ü',
  '�' => 'ý',
  '�' => 'þ',
  '�' => 'ÿ',
);

$result =& $data;
unset($data);

return $result;
