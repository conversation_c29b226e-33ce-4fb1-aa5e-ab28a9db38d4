#!/usr/bin/env python3
import os
import json

def load_posts():
    """Load posts from JSON file"""
    with open('scraped_posts.json', 'r', encoding='utf-8') as f:
        return json.load(f)

def fix_post_paths():
    """Fix CSS paths, image paths, and navigation links in all post files"""
    posts = load_posts()
    
    fixed_count = 0
    for post in posts:
        slug = post['slug']
        file_path = f'public/blog/{slug}.html'
        
        if os.path.exists(file_path):
            try:
                # Read the content
                with open(file_path, 'r', encoding='utf-8') as f:
                    content = f.read()
                
                # Fix CSS path: ../../style.css -> style.css
                content = content.replace('href="../../style.css"', 'href="style.css"')
                
                # Fix avatar path: ../../avatar.png -> avatar.png
                content = content.replace('src="../../avatar.png"', 'src="avatar.png"')
                
                # Fix navigation links: /blog/posts/ -> /blog/posts/
                # (Keep this as is since the posts listing page is still at /blog/posts/)
                
                # Fix back link: /blog/posts/ -> /blog/posts/
                # (Keep this as is since the posts listing page is still at /blog/posts/)
                
                # Write the fixed content back
                with open(file_path, 'w', encoding='utf-8') as f:
                    f.write(content)
                
                print(f"Fixed paths in: {slug}")
                fixed_count += 1
                
            except Exception as e:
                print(f"Error fixing {slug}: {e}")
        else:
            print(f"File not found: {file_path}")
    
    print(f"\nFixed paths in {fixed_count} posts.")

if __name__ == "__main__":
    fix_post_paths() 